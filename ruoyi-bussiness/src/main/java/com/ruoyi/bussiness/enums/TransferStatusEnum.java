package com.ruoyi.bussiness.enums;

/**
 * 模型转换任务状态枚举
 */
public enum TransferStatusEnum {

    FAILED("0", "失败"),
    SUCCESS("1", "成功"),
    EXECUTING("2", "执行中")
    ;

    private final String code;
    private final String info;

    TransferStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
