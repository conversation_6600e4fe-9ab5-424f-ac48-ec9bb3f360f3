package com.ruoyi.bussiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 主机状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum HostStatusEnum {

    /**
     * 空闲状态
     */
    IDLE("0", "空闲"),

    /**
     * 执行中状态
     */
    RUNNING("1", "执行中"),

    /**
     * 离线状态
     */
    OFFLINE("2", "离线");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;
} 