package com.ruoyi.bussiness.task;

import com.ruoyi.bussiness.service.SysUploadTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 上传任务清理定时任务
 * 定期清理超时的上传任务，释放系统资源
 * 
 * <AUTHOR>
 */
@Component
@EnableScheduling
@Slf4j
public class UploadTaskCleanupTask {

    @Autowired
    private SysUploadTaskService sysUploadTaskService;

    /**
     * 清理超时的上传任务
     * 每小时执行一次，清理超过2小时未更新的上传任务
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时的0分0秒执行
    public void cleanupTimeoutUploadTasks() {
        try {
            log.info("开始执行上传任务清理定时任务");
            sysUploadTaskService.cleanupTimeoutUploadTasks();
            log.info("上传任务清理定时任务执行完成");
        } catch (Exception e) {
            log.error("执行上传任务清理定时任务时发生错误", e);
        }
    }
}
