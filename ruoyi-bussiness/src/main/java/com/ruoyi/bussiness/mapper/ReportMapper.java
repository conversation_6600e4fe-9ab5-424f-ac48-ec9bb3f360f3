package com.ruoyi.bussiness.mapper;

import com.ruoyi.bussiness.model.vo.ReportModuleDataVO;
import com.ruoyi.bussiness.model.vo.ReportRegionDataVO;
import com.ruoyi.bussiness.model.vo.ReportUserTypeDataVO;
import com.ruoyi.bussiness.model.vo.ReportConversionRateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 报表数据查询Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ReportMapper {

    /**
     * 查询模型转换地区分布数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 地区分布数据
     */
    List<Map<String, Object>> selectConversionRegionDistribution(@Param("startTime") String startTime,
                                                                  @Param("endTime") String endTime);

    /**
     * 查询SDK下载地区分布数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 地区分布数据
     */
    List<Map<String, Object>> selectSdkDownloadRegionDistribution(@Param("startTime") String startTime,
                                                                   @Param("endTime") String endTime);

    /**
     * 查询平台使用趋势数据 - 访问次数
     *
     * @param timeUnit 时间单位：day、month、year
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 访问次数趋势数据
     */
    List<Map<String, Object>> selectAccessTrend(@Param("timeUnit") String timeUnit,
                                                 @Param("startTime") String startTime,
                                                 @Param("endTime") String endTime);

    /**
     * 查询平台使用趋势数据 - 模型转换次数
     *
     * @param timeUnit 时间单位：day、month、year
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 模型转换次数趋势数据
     */
    List<Map<String, Object>> selectConversionTrend(@Param("timeUnit") String timeUnit,
                                                     @Param("startTime") String startTime,
                                                     @Param("endTime") String endTime);

    /**
     * 查询各模块访问分布数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 模块访问分布数据
     */
    List<Map<String, Object>> selectModuleDistribution(@Param("startTime") String startTime,
                                                        @Param("endTime") String endTime);

    /**
     * 查询用户类型分布数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户类型分布数据
     */
    List<Map<String, Object>> selectUserTypeDistribution(@Param("startTime") String startTime,
                                                          @Param("endTime") String endTime);

    /**
     * 查询模型转换成功率数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 转换成功率数据
     */
    List<Map<String, Object>> selectConversionRate(@Param("startTime") String startTime,
                                                    @Param("endTime") String endTime);

    /**
     * 查询注册用户总数
     *
     * @return 注册用户总数
     */
    Integer selectTotalUsers();

    /**
     * 查询用户操作总次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户操作总次数
     */
    Integer selectTotalVisits(@Param("startTime") String startTime,
                              @Param("endTime") String endTime);

    /**
     * 查询用户登录总次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户登录总次数
     */
    Integer selectTotalLogins(@Param("startTime") String startTime,
                              @Param("endTime") String endTime);

    /**
     * 查询SDK下载次数总和
     *
     * @return SDK下载次数总和
     */
    Integer selectSdkDownloadCount();

    /**
     * 查询示例训练工程下载次数总和
     *
     * @return 示例训练工程下载次数总和
     */
    Integer selectTrainEngineerDownloadCount();

    /**
     * 查询原始模型数（文件类型为model的文件数量）
     *
     * @return 原始模型数
     */
    Integer selectOriginalModelCount();
}
