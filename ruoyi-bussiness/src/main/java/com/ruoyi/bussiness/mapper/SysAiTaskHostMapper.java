package com.ruoyi.bussiness.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * AI任务主机Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SysAiTaskHostMapper extends BaseMapper<SysAiTaskHost> {

    /**
     * 更新主机状态
     *
     * @param hostId 主机ID
     * @param status 状态
     * @return 更新结果
     */
    @Update("UPDATE sys_ai_task_host SET status = #{status}, update_time = NOW() WHERE host_id = #{hostId}")
    int updateHostStatus(@Param("hostId") Long hostId, @Param("status") String status);

    /**
     * 更新主机资源使用情况
     *
     * @param hostId 主机ID
     * @param cpuUsage CPU使用率
     * @param memoryUsage 内存使用率
     * @param diskUsage 磁盘使用率
     * @return 更新结果
     */
    @Update("UPDATE sys_ai_task_host SET cpu_usage = #{cpuUsage}, memory_usage = #{memoryUsage}, " +
            "disk_usage = #{diskUsage}, update_time = NOW() WHERE host_id = #{hostId}")
    int updateHostResource(@Param("hostId") Long hostId,
                         @Param("cpuUsage") Double cpuUsage,
                         @Param("memoryUsage") Double memoryUsage,
                         @Param("diskUsage") Double diskUsage);
} 