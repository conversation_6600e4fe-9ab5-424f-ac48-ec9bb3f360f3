package com.ruoyi.bussiness.model.dto;

import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.common.utils.bean.BeanUtils;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 自定义PartSummary类，用于兼容V1和V2版本的转换
 */
@Data
public class CustomPartSummary {
    private Integer partNumber;
    private String eTag;
    private Long size;
}

