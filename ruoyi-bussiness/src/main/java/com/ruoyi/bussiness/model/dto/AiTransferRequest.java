package com.ruoyi.bussiness.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("AI模型转换请求参数")
public class AiTransferRequest {
    @ApiModelProperty("模型标识")
    private String modelIdentifier;

    @ApiModelProperty("配置标识")
    private String configIdentifier;

    @ApiModelProperty("前端自动生成的一个转换任务id")
    private String taskId;

    @ApiModelProperty("选择的执行工具标识")
    private String toolIdentifier;

    @ApiModelProperty("图片标识列表")
    private List<String> imageIdentifier;
}
