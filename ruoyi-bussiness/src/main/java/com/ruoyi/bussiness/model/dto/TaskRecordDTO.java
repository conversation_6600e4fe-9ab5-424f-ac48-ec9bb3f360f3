package com.ruoyi.bussiness.model.dto;

import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.common.utils.bean.BeanUtils;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ToString
@Accessors(chain = true)
public class TaskRecordDTO extends SysUploadTask {

    /**
     * 已上传完的分片
     */
    private List<CustomPartSummary> exitPartList;

    public static TaskRecordDTO convertFromEntity (SysUploadTask task) {
        TaskRecordDTO dto = new TaskRecordDTO();
        BeanUtils.copyProperties(task, dto);
        return dto;
    }
}
