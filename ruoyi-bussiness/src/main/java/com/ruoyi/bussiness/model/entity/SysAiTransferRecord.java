package com.ruoyi.bussiness.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI模型转换日志记录表
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_ai_transfer_record", autoResultMap = true)
public class SysAiTransferRecord {

    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 转换任务id
     */
    private String taskId;

    /**
     * 转换用户
     */
    private String username;

    /**
     * 原始转换模型标识
     */
    private String modelIdentifier;

    /**
     * 配置文件标识
     */
    private String configIdentifier;

    /**
     * 量化图片列表标识，JSON数组格式存储
     */
    @TableField(value = "image_identifier", typeHandler = JacksonTypeHandler.class)
    private List<String> imageIdentifiers;


    /**
     * 转换后文件标识
     */
    private String transferModelIdentifier;

    /**
     * 日志文件标识
     */
    private String logIdentifier;

    /**
     * 转换状态（0失败 1成功  2  转换中）
     */
    private String status;

    /**
     * 登录地址
     */
    private String optLocation;

    /**
     * 转换耗时 单位 s
     */
    private Long costs;

    /**
     * 备注
     */
    private String remark;


    /**
     * 结束时间
     */
    @TableField(exist = false)
    private String endTime;

    /**
     * 开始时间
     */
    @TableField(exist = false)
    private String startTime;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 