package com.ruoyi.bussiness.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 地区分布报表数据VO
 *
 * <AUTHOR>
 */
@Data
public class ReportRegionDataVO {

    /**
     * 数据类型: conversion(模型转换次数)或download(SDK下载次数)
     */
    private String dataType;

    /**
     * 地区数据列表
     */
    private List<RegionItem> regions;

    /**
     * 可视化映射配置
     */
    private VisualMap visualMap;

    @Data
    public static class RegionItem {
        /**
         * 省份/地区名称，必须与地图中的地区名称匹配
         */
        private String name;

        /**
         * 该地区的数值
         */
        private Integer value;
    }

    @Data
    public static class VisualMap {
        /**
         * 数据映射的最小值
         */
        private Integer min;

        /**
         * 数据映射的最大值
         */
        private Integer max;
    }
}
