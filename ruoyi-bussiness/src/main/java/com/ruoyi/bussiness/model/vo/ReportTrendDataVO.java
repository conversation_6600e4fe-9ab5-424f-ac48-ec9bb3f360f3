package com.ruoyi.bussiness.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 平台使用趋势报表数据VO
 *
 * <AUTHOR>
 */
@Data
public class ReportTrendDataVO {

    /**
     * 时间单位：day(日)、month(月)、year(年)
     */
    private String timeUnit;

    /**
     * 时间标签
     */
    private List<String> timeLabels;

    /**
     * 数据系列
     */
    private List<SeriesItem> series;

    @Data
    public static class SeriesItem {
        /**
         * 系列名称
         */
        private String name;

        /**
         * 对应每个时间点的数据
         */
        private List<Integer> data;
    }
}
