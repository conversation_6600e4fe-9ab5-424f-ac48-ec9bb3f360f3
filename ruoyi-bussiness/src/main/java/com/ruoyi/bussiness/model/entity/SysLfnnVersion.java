package com.ruoyi.bussiness.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 
 * @TableName sys_lfnn_version
 */
@TableName(value ="sys_lfnn_version")
@Data
public class SysLfnnVersion implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 版本号
     */
    private String lfnnVersion;

    /**
     * 版本描述
     */
    private String lfnnRemark;

    /**
     * 版本文件标识
     */
    private String lfnnIdentifier;

    /**
     * 芯片类型
     */
    private String chipType;


    /**
     * 版本文件名称
     */
    private String lfnnFileName;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /** 创建者 */
    private String createBy;

    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新者
    private String updateBy;
    //更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}