package com.ruoyi.bussiness.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 
 * @TableName sys_ai_model_zoo
 */
@TableName(value ="sys_ai_model_zoo")
@Data
public class SysAiModelZoo implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模型类型
     */
    private String modelType;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 芯片类型
     */
    private String chipType;

    /**
     * 推理示例代码文件
     */
    private String codeIdentififer;


    /**
     * 推理示例代码文件名
     */
    private String codeFileName;

    /**
     * 推理性能单位ms
     */
    private BigDecimal deductivePerformance;

    /**
     * 推理精度
     */
    private BigDecimal dedcutiveAccuracy;


    /**
     * 前推理性能
     */
    private BigDecimal beforeDedcutivePerformance;

    /**
     * 后推理性能
     */
    private BigDecimal afterDedcutivePerformance;

    /**
     * 模型输入size
     */
    private String size;

    /** 创建者 */
    private String createBy;

    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新者
    private String updateBy;
    //更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}