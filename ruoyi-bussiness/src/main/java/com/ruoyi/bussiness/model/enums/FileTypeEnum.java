package com.ruoyi.bussiness.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件类型枚举      增加了类型配置数据库，此类可去除
 */
@Getter
@AllArgsConstructor
public enum FileTypeEnum {
    /**
     * 模型文件
     */
    MODEL("model", "模型文件"),
    
    /**
     * 配置文件
     */
    CONFIG("config", "配置文件"),
    
    /**
     * 图片
     */
    IMAGE("image", "图片"),
    
    /**
     * 其他类型
     */
    OTHER("other", "其他"),

    /**
     * 文件管理-模型
     */
    UPLOAD_MODEL("upload-model", "文件管理-模型"),

    /**
     * 模型转换-示例原始文件
     */
    MODEL_ORIGINAL_FILE("model-original-file", "模型转换-示例原始文件"),

    /**
     * 模型转换-示例配置文件
     */
    MODEL_CONFIG("model-config", "模型转换-示例配置文件"),

    /**
     * 模型转换-示例配置文件
     */
    TRANSFER_MODEL("transfer-model", "转换后模型"),

    /**
     * 转换日志文件
     */
    TRANSFER_MODEL_LOG("transfer-model-log", "转换日志文件"),

    /**
     * 模型转换工具
     */
    MODEL_TRANSFER_TOOL("model-transfer-tool", "模型转换工具"),

    /**
     * 公司注册信息文件
     */
    COMPANY_REGISTRATION("company-registration", "公司注册信息"),

    /**
     * NDA合同文件
     */
    NDA_CONTRACT("nda-contract", "NDA合同"),

    /**
     * 训练工程
     */
    TRAIN_ENGINEER("train-engineer", "训练工程")
    ;

    /**
     * 类型编码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 获取枚举值的code
     * @return code
     */
    @JsonValue
    public String getCode() {
        return code;
    }
    
    /**
     * 根据code创建枚举
     * @param code 编码
     * @return 枚举实例
     */
    @JsonCreator
    public static FileTypeEnum fromCode(String code) {
        if (code == null) {
            return OTHER;
        }
        for (FileTypeEnum type : FileTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER;
    }
} 