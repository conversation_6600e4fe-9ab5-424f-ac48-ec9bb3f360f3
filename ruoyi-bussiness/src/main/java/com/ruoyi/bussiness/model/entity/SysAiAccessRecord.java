package com.ruoyi.bussiness.model.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 
 * @TableName sys_ai_access_record
 */
@TableName(value ="sys_ai_access_record")
@Data
public class SysAiAccessRecord implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 访问类型   modelConversion 模型转换
     *          document        文档
     *          lfnn            lfnn版本下载
     *          modelZoo        modelZoo访问
     *          trainEngineer   示例训练工程
     */
    private String accessType;

    /**
     * 访问次数
     */
    private Integer accessCount;


    /** 创建者 */
    private String createBy;

    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新者
    private String updateBy;
    //更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}