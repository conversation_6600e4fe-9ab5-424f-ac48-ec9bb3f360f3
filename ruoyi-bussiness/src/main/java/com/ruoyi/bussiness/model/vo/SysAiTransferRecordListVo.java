package com.ruoyi.bussiness.model.vo;

import com.ruoyi.bussiness.model.entity.SysAiTransferRecord;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * AI模型转换日志Mapper接口
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysAiTransferRecordListVo extends SysAiTransferRecord {

    /**
     * 模型标识任务数据
     */
    private SysUploadTask modelIdentifierTask;

    /**
     * 配置标识任务数据
     */
    private SysUploadTask configIdentifierTask;

    /**
     * 转换后模型标识任务数据
     */
    private SysUploadTask transferModelIdentifierTask;

    /**
     * 量化图片列表标识任务数据
     */
    private List<SysUploadTask> imageIdentifiersTasks;

    /**
     * 日志标识任务数据
     */
    private SysUploadTask logIdentifierTask;


}
