package com.ruoyi.bussiness.model.vo;

import lombok.Data;

import java.util.List;

/**
 * 模型转换成功率报表数据VO
 *
 * <AUTHOR>
 */
@Data
public class ReportConversionRateVO {

    /**
     * 数据名称
     */
    private String name;

    /**
     * 转换总数
     */
    private Integer total;

    /**
     * 转换结果数据列表
     */
    private List<ConversionItem> data;

    @Data
    public static class ConversionItem {
        /**
         * 转换结果名称（成功/失败）
         */
        private String name;

        /**
         * 转换次数
         */
        private Integer value;
    }
}
