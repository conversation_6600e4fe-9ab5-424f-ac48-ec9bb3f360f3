package com.ruoyi.bussiness.model.param;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ToString
@Accessors(chain = true)
public class BatchInitTaskParam {

    /**
     * 文件列表
     */
    @NotEmpty(message = "文件列表不能为空")
    @Valid
    private List<InitTaskParam> files;

    /**
     * 文件类型（可选，用于区分不同类型的文件，如：avatar-头像，article-文章图片等）
     */
    private String fileType;
} 