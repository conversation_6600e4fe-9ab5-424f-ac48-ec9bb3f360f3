package com.ruoyi.bussiness.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import java.util.List;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * AI任务主机信息表
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_ai_task_host")
public class SysAiTaskHost {

    /**
     * 主机ID
     */
    @TableId(type = IdType.AUTO)
    private Long hostId;

    /**
     * 主机名称
     */
    private String hostName;

    /**
     * 主机IP
     */
    private String hostIp;

    /**
     * SSH端口
     */
    private Integer sshPort;

    /**
     * SSH用户名
     */
    private String sshUsername;

    /**
     * SSH密码
     */
    private String sshPassword;

    /**
     * 可执行文件路径
     */
    private String executablePath;

    /**
     * 主机状态（0空闲 1执行中 2离线）
     */
    private String status;

    /**
     * CPU使用率
     */
    private Double cpuUsage;

    /**
     * 内存使用率
     */
    private Double memoryUsage;

    /**
     * 磁盘使用率
     */
    private Double diskUsage;

    /**
     * 备注
     */
    private String remark;

    /**
     * 最大同时执行任务数(单机)
     */
    private Integer maxQueueSize;

    /** 创建者 */
    private String createBy;

    //创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    //更新者
    private String updateBy;
    //更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 当前主机已分配的用户列表
     * 存储为JSON字符串，记录当前主机上有哪些用户在用
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> userList;
} 