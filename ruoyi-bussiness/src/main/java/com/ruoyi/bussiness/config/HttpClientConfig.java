package com.ruoyi.bussiness.config;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.http.SdkHttpClient;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 自定义HttpClient配置类
 * 提供高性能、可配置的HTTP客户端实例
 * 
 * <AUTHOR>
 */
@Configuration
public class HttpClientConfig {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientConfig.class);

    // 连接池配置
    private static final int MAX_TOTAL_CONNECTIONS = 300;
    private static final int MAX_CONNECTIONS_PER_ROUTE = 50;
    private static final int CONNECTION_TIMEOUT = 5000; // 5秒
    private static final int SOCKET_TIMEOUT = 30000; // 30秒
    private static final int CONNECTION_REQUEST_TIMEOUT = 3000; // 3秒
    private static final int VALIDATE_AFTER_INACTIVITY = 2000; // 2秒

    /**
     * 创建自定义的Apache HttpClient连接池管理器
     */
    @Bean(name = "httpClientConnectionManager")
    public PoolingHttpClientConnectionManager httpClientConnectionManager() {
        try {
            // 创建SSL上下文，信任所有证书（生产环境建议配置具体的证书验证）
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial(null, (certificate, authType) -> true)
                    .build();

            // 创建SSL连接工厂
            SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(
                    sslContext,
                    (hostname, session) -> true // 跳过主机名验证（生产环境建议启用）
            );

            // 注册连接协议
            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", sslConnectionSocketFactory)
                    .build();

            // 创建连接池管理器
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
            connectionManager.setMaxTotal(MAX_TOTAL_CONNECTIONS);
            connectionManager.setDefaultMaxPerRoute(MAX_CONNECTIONS_PER_ROUTE);
            connectionManager.setValidateAfterInactivity(VALIDATE_AFTER_INACTIVITY);

            logger.info("HttpClient连接池初始化完成 - 最大连接数: {}, 每路由最大连接数: {}", 
                       MAX_TOTAL_CONNECTIONS, MAX_CONNECTIONS_PER_ROUTE);

            return connectionManager;
        } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
            logger.error("创建HttpClient连接池管理器失败", e);
            throw new RuntimeException("HttpClient配置初始化失败", e);
        }
    }

    /**
     * 创建自定义的Apache HttpClient实例
     */
    @Bean(name = "customHttpClient")
    @Primary
    public CloseableHttpClient customHttpClient(PoolingHttpClientConnectionManager connectionManager) {
        // 请求配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECTION_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT)
                .build();

        // 创建HttpClient
        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManagerShared(true) // 允许连接管理器被多个客户端共享
                .evictExpiredConnections() // 自动清理过期连接
                .evictIdleConnections(5, TimeUnit.MINUTES) // 清理空闲连接
                .setRetryHandler((exception, executionCount, context) -> {
                    // 自定义重试策略：最多重试3次
                    if (executionCount >= 3) {
                        return false;
                    }
                    // 对于连接异常进行重试
                    return exception instanceof java.net.ConnectException ||
                           exception instanceof java.net.SocketTimeoutException;
                })
                .build();

        logger.info("自定义HttpClient创建完成 - 连接超时: {}ms, Socket超时: {}ms", 
                   CONNECTION_TIMEOUT, SOCKET_TIMEOUT);

        return httpClient;
    }

    /**
     * 为AWS SDK创建专用的SdkHttpClient
     * 注意：AWS SDK v2 的 ApacheHttpClient.builder().build() 返回的是 SdkHttpClient 类型
     * 需要使用 AWS SDK 自己的连接池配置
     */
    @Bean(name = "customApacheHttpClient")
    public SdkHttpClient customApacheHttpClient() {
        return ApacheHttpClient.builder()
                .connectionTimeout(Duration.ofMillis(CONNECTION_TIMEOUT))
                .socketTimeout(Duration.ofMillis(SOCKET_TIMEOUT))
                .connectionAcquisitionTimeout(Duration.ofMillis(CONNECTION_REQUEST_TIMEOUT))
                .maxConnections(MAX_TOTAL_CONNECTIONS)
                .connectionMaxIdleTime(Duration.ofMinutes(5))
                .connectionTimeToLive(Duration.ofMinutes(10))
                .useIdleConnectionReaper(true) // 启用空闲连接回收器
                .build();
    }
}
