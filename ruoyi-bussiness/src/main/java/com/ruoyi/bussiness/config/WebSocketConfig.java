package com.ruoyi.bussiness.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 *
 * <AUTHOR>
 */
@Configuration
public class WebSocketConfig {

    /**
     * ServerEndpointExporter 只在Web应用环境中创建
     * 避免在测试环境中因为缺少ServerContainer而导致的启动失败
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}