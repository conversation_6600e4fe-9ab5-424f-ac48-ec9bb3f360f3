package com.ruoyi.bussiness.config;

import com.ruoyi.bussiness.config.properties.MinioProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Qualifier;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

import java.net.URI;
import java.time.Duration;

import javax.annotation.Resource;

@Configuration
public class AmazonS3Config {

    @Resource
    private MinioProperties minioProperties;

    @Bean(name = "amazonS3Client")
    public S3Client amazonS3Client(@Qualifier("customApacheHttpClient") software.amazon.awssdk.http.SdkHttpClient customHttpClient) {
        // 创建基本凭证
        AwsBasicCredentials credentials = AwsBasicCredentials.create(
                minioProperties.getAccessKey(),
                minioProperties.getAccessSecret()
        );

        // 配置S3客户端
        S3Configuration s3Configuration = S3Configuration.builder()
                .pathStyleAccessEnabled(true) // 启用路径样式访问
                .build();

        // 创建并返回S3客户端，使用自定义的HttpClient
        return S3Client.builder()
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.US_EAST_1)
                .endpointOverride(URI.create(minioProperties.getEndpoint()))
                .serviceConfiguration(s3Configuration)
                .httpClient(customHttpClient) // 使用注入的自定义HttpClient
                .build();
    }

    @Bean(name = "s3Presigner")
    public S3Presigner s3Presigner(@Qualifier("customApacheHttpClient") software.amazon.awssdk.http.SdkHttpClient customHttpClient) {
        // 创建基本凭证
        AwsBasicCredentials credentials = AwsBasicCredentials.create(
                minioProperties.getAccessKey(),
                minioProperties.getAccessSecret()
        );

        // 创建并返回S3预签名客户端，用于生成预签名URL
        // 注意：S3Presigner 不支持直接设置 httpClient
        return S3Presigner.builder()
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.US_EAST_1) // 使用与V1版本相同的区域
                .endpointOverride(URI.create(minioProperties.getEndpoint()))
                .serviceConfiguration(S3Configuration.builder()
                        .pathStyleAccessEnabled(true)
                        .build())
                .build();
    }
}
