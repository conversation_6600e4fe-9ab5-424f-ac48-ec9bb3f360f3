package com.ruoyi.bussiness.listener;

import com.ruoyi.bussiness.event.TransferToolUpdateEvent;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.enums.FileTypeEnum;
import com.ruoyi.bussiness.service.ISysAiTaskHostService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.bussiness.utils.SshUtil;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;

/**
 * 转换工具更新监听器，当转换工具更新时，将当前转换工具上传到所有主机
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TransferToolUpdateListener {

    @Autowired
    private ISysAiTaskHostService sysAiTaskHostService;

    @Autowired
    private SysUploadTaskService sysUploadTaskService;


    @Autowired
    private ISysConfigService sysConfigService;


    private static final long FILE_DOWNLOAD_EXPIRES = 3600L;

    @EventListener
    @Async
    public void handleTransferToolUpdateEvent(TransferToolUpdateEvent event) {
        SysUploadTask tool = event.getTask();
        log.info("收到转换工具更新事件，开始推送工具到所有主机: {}", tool.getFileName());

        try {
            // 获取所有空闲主机
            List<SysAiTaskHost> hosts = sysAiTaskHostService.listHostIdle();
            if (hosts.isEmpty()) {
                log.warn("没有配置任何主机，跳过转换工具推送");
                return;
            }

            // 创建临时目录
            Path tempDir = Files.createTempDirectory("transfer_tool_update_");
            try {
                // 下载工具文件
                String downloadUrl = sysUploadTaskService.genPreSignDownloadUrl(tool.getFileIdentifier(), FILE_DOWNLOAD_EXPIRES, false, FileTypeEnum.MODEL_TRANSFER_TOOL.getCode());
                Path localPath = tempDir.resolve(tool.getFileName());
                FileUtils.copyURLToFile(new URL(downloadUrl), localPath.toFile());

                // 推送到所有主机
                for (SysAiTaskHost host : hosts) {
                    try {
                        uploadToolToHost(host, tool, localPath);
                    } catch (Exception e) {
                        log.error("推送转换工具到主机 {}({}) 失败", host.getHostName(), host.getHostIp(), e);
                    }
                }
            } finally {
                // 清理临时目录
                FileUtils.deleteDirectory(tempDir.toFile());
            }
        } catch (Exception e) {
            log.error("处理转换工具更新事件失败", e);
        }
    }
    /**
     * 上传工具到指定主机
     */
    private void uploadToolToHost(SysAiTaskHost host, SysUploadTask tool, Path localPath) {
        // 检查主机的可执行文件路径
        String executablePath = host.getExecutablePath();
        if (!StringUtils.hasText(executablePath)) {
            log.error("主机 {}({}) 的可执行文件路径未配置，跳过工具上传", host.getHostName(), host.getHostIp());
            return;
        }

        try {
            String remotePath = executablePath + "/" + tool.getFileName();

            // 上传文件
            boolean success = SshUtil.uploadFile(host, localPath.toString(), remotePath);

            if (success) {
                // 设置执行权限
                SshUtil.executeCommand(host, "chmod +x " + remotePath, null);

                // 在executablePath目录下执行解压命令
                String unzipCmd = String.format("cd %s && unzip -o %s", executablePath, tool.getFileName());
                if (!SshUtil.executeCommand(host, unzipCmd, null)) {
                    log.error("解压zip文件失败: {}", remotePath);
                    return;
                }
                log.info("成功解压zip文件: {}", remotePath);

                // 解压tar.gz文件
                String tarCmd = String.format("cd %s && tar -xzf generator_*.tar.gz", executablePath);
                if (!SshUtil.executeCommand(host, tarCmd, null)) {
                    log.error("解压tar.gz文件失败");
                    return;
                }
                log.info("成功解压tar.gz文件");

                // 设置可执行权限
                String toolParams = sysConfigService.selectConfigByKey(tool.getFileIdentifier());
                if (Objects.isNull(toolParams) || toolParams.isEmpty()) {
                    throw new RuntimeException("未找到工具执行指令的参数配置");
                }
                //使用;分割字符串  （第一个参数为执行文件的路径，第二个参数为执行文件的参数 配置）
                String[] toolParamsArray = toolParams.split(";");
                String toolPath = toolParamsArray[0];
//                String chmodCmd = String.format("cd %s && chmod +x CDNN_CONVERT/exe/LFNetworkGenerator", executablePath);
                String chmodCmd = String.format("cd %s && chmod +x " + toolPath, executablePath);
                if (!SshUtil.executeCommand(host, chmodCmd, null)) {
                    log.error("设置可执行权限失败");
                    return;
                }

                log.info("成功推送转换工具到主机 {}({})", host.getHostName(), host.getHostIp());
            } else {
                log.error("推送转换工具到主机 {}({}) 失败", host.getHostName(), host.getHostIp());
            }
        } catch (Exception e) {
            log.error("处理主机 {}({}) 时发生错误", host.getHostName(), host.getHostIp(), e);
        }
    }
}
