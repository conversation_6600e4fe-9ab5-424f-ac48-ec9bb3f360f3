package com.ruoyi.bussiness.listener;

import com.ruoyi.bussiness.event.HostAddedEvent;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.enums.FileTypeEnum;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.bussiness.utils.SshUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 主机新增事件监听器
 * 在新增主机时，同步上传转换工具到该主机
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HostAddedEventListener {

    @Autowired
    private SysUploadTaskService sysUploadTaskService;

    private static final String TOOL_FILE_TYPE = "model-transfer-tool";
    private static final long FILE_DOWNLOAD_EXPIRES = 3600L;

    @EventListener
    public void handleHostAddedEvent(HostAddedEvent event) {
        SysAiTaskHost host = event.getHost();
        try {
            // 检查主机的可执行文件路径
            String executablePath = host.getExecutablePath();
            if (!StringUtils.hasText(executablePath)) {
                log.error("主机 {}({}) 的可执行文件路径未配置，跳过工具上传", host.getHostName(), host.getHostIp());
                return;
            }

            // 获取最新的转换工具文件
            SysUploadTask tool = sysUploadTaskService.getByFileType(TOOL_FILE_TYPE);
            if (tool == null) {
                log.warn("未找到转换工具文件，跳过工具上传");
                return;
            }

            // 创建临时目录
            Path tempDir = Files.createTempDirectory("transfer_tool_init_");
            try {
                // 删除executablePath目录下的所有内容
                SshUtil.executeCommand(host, "rm -rf " + executablePath + "/*", null);
                log.info("主机 {}({}) 的目录 {} 下所有内容已被删除", host.getHostName(), host.getHostIp(), executablePath);

                String remotePath = executablePath + "/" + tool.getFileName();
                // 下载工具文件
                String downloadUrl = sysUploadTaskService.genPreSignDownloadUrl(tool.getFileIdentifier(), FILE_DOWNLOAD_EXPIRES, false, FileTypeEnum.MODEL_TRANSFER_TOOL.getCode());
                Path localPath = tempDir.resolve(tool.getFileName());
                FileUtils.copyURLToFile(new URL(downloadUrl), localPath.toFile());

                // 上传文件
                boolean success = SshUtil.uploadFile(host, localPath.toString(), remotePath);

                if (success) {
                    // 设置执行权限
                    SshUtil.executeCommand(host, "chmod +x " + remotePath, null);
                    // 在executablePath目录下执行解压命令
                    String unzipCmd = String.format("cd %s && unzip -o %s", executablePath, tool.getFileName());
                    if (!SshUtil.executeCommand(host, unzipCmd, null)) {
                        log.error("解压zip文件失败: {}", remotePath);
                    }
                    // 解压tar.gz文件
                    String tarCmd = String.format("cd %s && tar -xzf generator_*.tar.gz", executablePath);
                    if (!SshUtil.executeCommand(host, tarCmd, null)) {
                        log.error("解压tar.gz文件失败");
                    }
                    // 创建mobilenet目录
                    String mobilenetPath = executablePath + "/mobilenet";
                    if (!SshUtil.executeCommand(host, "test -d " + mobilenetPath, null)) {
                        if (!SshUtil.executeCommand(host, "mkdir -p " + mobilenetPath, null)) {
                            log.error("创建mobilenet目录失败");
                        }
                    }
                    // 前端点击执行后，执行模型转换指令  ./CDNN_CONVERT/exe/LFNetworkGenerator --network ./mobilenet/deploy.pb --input ../mobilenet/ -p xm6 -h nprs -d 512 --profiler long -c -t 8bit --FreqAndAXI 1000 256
                    log.info("成功上传转换工具到主机 {}({})", host.getHostName(), host.getHostIp());
                } else {
                    log.error("上传转换工具到新增主机 {}({}) 失败", host.getHostName(), host.getHostIp());
                }
            } finally {
                // 清理临时目录
                FileUtils.deleteDirectory(tempDir.toFile());
            }
        } catch (Exception e) {
            log.error("处理新增主机事件时发生错误，主机：{}({})", host.getHostName(), host.getHostIp(), e);
        }
    }
} 