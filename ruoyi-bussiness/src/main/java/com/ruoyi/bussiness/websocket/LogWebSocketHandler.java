package com.ruoyi.bussiness.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket日志处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/ai/websocket/log/{taskId}")
public class LogWebSocketHandler {

    /**
     * 用于存储taskId和session的映射关系
     */
    private static final ConcurrentHashMap<String, Session> SESSION_MAP = new ConcurrentHashMap<>();

    @OnOpen
    public void onOpen(Session session, @PathParam("taskId") String taskId) {
        SESSION_MAP.put(taskId, session);
        log.info("WebSocket连接建立成功，taskId: {}", taskId);
    }

    @OnClose
    public void onClose(@PathParam("taskId") String taskId) {
        SESSION_MAP.remove(taskId);
        log.info("WebSocket连接关闭，taskId: {}", taskId);
    }

    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误：{}", error.getMessage());
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到消息：{}", message);
    }

    /**
     * 发送消息
     *
     * @param taskId 任务ID
     * @param message 消息内容
     */
    public void sendMessage(String taskId, String message) {
        Session session = SESSION_MAP.get(taskId);
        if (session != null && session.isOpen()) {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("发送消息失败：{}", e.getMessage());
            }
        }
    }
} 