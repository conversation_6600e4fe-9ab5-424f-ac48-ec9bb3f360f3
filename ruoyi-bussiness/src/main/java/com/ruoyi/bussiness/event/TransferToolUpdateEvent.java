package com.ruoyi.bussiness.event;

import com.ruoyi.bussiness.model.entity.SysUploadTask;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 转换工具更新推送事件，将最新的转换工具推送到所有主机
 *
 * <AUTHOR>
 */
@Getter
public class TransferToolUpdateEvent extends ApplicationEvent {

    private final SysUploadTask task;

    public TransferToolUpdateEvent(Object source, SysUploadTask task) {
        super(source);
        this.task = task;
    }
}