package com.ruoyi.bussiness.event;

import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 主机新增事件
 */
@Getter
public class HostAddedEvent extends ApplicationEvent {
    
    private final SysAiTaskHost host;

    public HostAddedEvent(Object source, SysAiTaskHost host) {
        super(source);
        this.host = host;
    }
} 