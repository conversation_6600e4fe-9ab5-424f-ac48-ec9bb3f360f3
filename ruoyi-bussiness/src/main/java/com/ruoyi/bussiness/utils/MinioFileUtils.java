package com.ruoyi.bussiness.utils;

import com.ruoyi.bussiness.model.dto.TaskInfoDTO;
import com.ruoyi.bussiness.model.constants.MinioConstant;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件工具类
 */
@Slf4j
public class MinioFileUtils {

    /**
     * 计算文件的MD5值
     *
     * @param file 文件
     * @return MD5值
     */
    public static String calculateMD5(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            return DigestUtils.md5Hex(fis);
        }
    }

    /**
     * 使用预签名URL上传文件
     *
     * @param preSignedUrl 预签名URL
     * @param file 要上传的文件
     */
    public static void uploadToPresignedUrl(String preSignedUrl, File file) throws IOException {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(preSignedUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("PUT");
            
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = connection.getOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
            
            int responseCode = connection.getResponseCode();
            if (responseCode < 200 || responseCode >= 300) {
                throw new IOException("上传失败，响应码：" + responseCode);
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 上传大文件（需要分片）
     *
     * @param filePath 文件路径
     * @param taskInfo 任务信息
     * @param uploadTaskService 上传服务
     */
    public static void uploadLargeFile(String filePath, TaskInfoDTO taskInfo, SysUploadTaskService uploadTaskService) throws IOException {
        File file = new File(filePath);
        long fileSize = file.length();
        int chunkSize = MinioConstant.DEFAULT_CHUNK_SIZE;
        int chunkNum = (int) Math.ceil((double) fileSize / chunkSize);
        
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            for (int i = 1; i <= chunkNum; i++) {
                // 计算当前分片大小
                long start = (i - 1L) * chunkSize;
                long curChunkSize = Math.min(chunkSize, fileSize - start);
                
                // 读取分片数据
                byte[] buffer = new byte[(int) curChunkSize];
                raf.seek(start);
                raf.read(buffer);
                
                // 获取上传URL
                Map<String, String> params = new HashMap<>();
                params.put("partNumber", String.valueOf(i));
                params.put("uploadId", taskInfo.getTaskRecord().getUploadId());
                
                String uploadUrl = uploadTaskService.genPreSignUploadUrl(
                    taskInfo.getTaskRecord().getBucketName(),
                    taskInfo.getTaskRecord().getObjectKey(),
                    params
                );
                
                // 上传分片
                uploadChunk(uploadUrl, buffer);
            }
            
            // 合并分片
            uploadTaskService.merge(taskInfo.getTaskRecord().getFileIdentifier(), null);
        }
    }

    /**
     * 上传分片数据
     */
    private static void uploadChunk(String preSignedUrl, byte[] data) throws IOException {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(preSignedUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("PUT");
            
            try (OutputStream os = connection.getOutputStream()) {
                os.write(data);
                os.flush();
            }
            
            int responseCode = connection.getResponseCode();
            if (responseCode < 200 || responseCode >= 300) {
                throw new IOException("分片上传失败，响应码：" + responseCode);
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
} 