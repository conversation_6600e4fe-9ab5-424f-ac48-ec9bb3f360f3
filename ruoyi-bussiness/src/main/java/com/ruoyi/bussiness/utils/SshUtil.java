package com.ruoyi.bussiness.utils;

import com.jcraft.jsch.*;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.function.Consumer;

/**
 * SSH工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SshUtil {

    // 默认分片大小：10MB
    private static final int DEFAULT_CHUNK_SIZE = 10 * 1024 * 1024;
    // 默认线程池大小
    private static final int DEFAULT_THREAD_POOL_SIZE = 5;
    // 线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(DEFAULT_THREAD_POOL_SIZE);

    /**
     * 执行远程命令
     *
     * @param host 主机信息
     * @param command 命令
     * @param logConsumer 日志消费者
     * @return 执行结果
     */
    public static boolean executeCommand(SysAiTaskHost host, String command, Consumer<String> logConsumer) {
        Session session = null;
        ChannelExec channel = null;
        try {
            session = createSession(host);
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);

            // 获取标准输出和错误输出
            InputStream in = channel.getInputStream();
            InputStream err = channel.getErrStream();
            channel.connect();

            // 创建两个线程分别处理标准输出和错误输出
            BufferedReader stdReader = new BufferedReader(new InputStreamReader(in));
            BufferedReader errReader = new BufferedReader(new InputStreamReader(err));

            // 标记是否有错误输出
            boolean hasError = false;

            // 读取标准输出
            String line;
            while ((line = stdReader.readLine()) != null) {
                if (logConsumer != null) {
                    logConsumer.accept(line);
                }
                log.info(line);
            }

            // 读取错误输出
            while ((line = errReader.readLine()) != null) {
                hasError = true;
                if (logConsumer != null) {
                    logConsumer.accept("ERROR: " + line);
                }
                log.error(line);
            }

            // 等待命令执行完成
            while (!channel.isClosed()) {
                Thread.sleep(100);
            }

            // 如果存在错误输出，直接返回false
            if (hasError) {
                return false;
            }

            int exitStatus = channel.getExitStatus();
            return exitStatus == 0;
        } catch (Exception e) {
            log.error("执行远程命令失败: {}", command, e);
            return false;
        } finally {
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }

    /**
     * 上传文件到远程服务器（多线程分片上传）
     *
     * @param host 主机信息
     * @param localPath 本地文件路径
     * @param remotePath 远程文件路径
     * @return 是否成功
     */
    public static boolean uploadFile(SysAiTaskHost host, String localPath, String remotePath) {
        File localFile = new File(localPath);
        if (!localFile.exists() || !localFile.isFile()) {
            log.error("本地文件不存在：{}", localPath);
            return false;
        }

        // 如果文件小于10MB，使用普通上传
        if (localFile.length() < DEFAULT_CHUNK_SIZE) {
            return uploadFileNormal(host, localPath, remotePath);
        }

        // 创建临时目录
        String tempDir = remotePath + "_chunks";
        log.info("创建临时目录: {}", tempDir);
        // 对路径进行转义，处理中文和特殊字符
        String escapedTempDir = escapeShellPath(tempDir);
        boolean dirCreated = executeCommand(host, "mkdir -p " + escapedTempDir, null);
        if (!dirCreated) {
            log.error("创建临时目录失败: {}", tempDir);
            return false;
        }

        // 验证目录是否真的创建成功
        boolean dirExists = executeCommand(host, "test -d " + escapedTempDir, null);
        if (!dirExists) {
            log.error("临时目录验证失败，目录不存在: {}", tempDir);
            return false;
        }
        log.info("临时目录创建并验证成功: {}", tempDir);
        try {
            // 计算分片数量
            long fileSize = localFile.length();
            int chunks = (int) Math.ceil((double) fileSize / DEFAULT_CHUNK_SIZE);
            List<Future<Boolean>> futures = new ArrayList<>();

            // 提交分片上传任务
            for (int i = 0; i < chunks; i++) {
                final int chunkIndex = i;
                Future<Boolean> future = executorService.submit(() -> {
                    String chunkPath = tempDir + "/chunk_" + chunkIndex;
                    return uploadChunk(host, localFile, chunkPath, chunkIndex, DEFAULT_CHUNK_SIZE);
                });
                futures.add(future);
            }

            // 等待所有分片上传完成
            boolean allSuccess = true;
            for (Future<Boolean> future : futures) {
                try {
                    if (!future.get(30, TimeUnit.MINUTES)) {
                        allSuccess = false;
                        break;
                    }
                } catch (Exception e) {
                    log.error("分片上传失败", e);
                    allSuccess = false;
                    break;
                }
            }

            if (!allSuccess) {
                // 清理临时文件
                executeCommand(host, "rm -rf " + tempDir, null);
                return false;
            }

            // 合并文件 - 按正确顺序合并分片
            StringBuilder mergeCommand = new StringBuilder("cat");
            for (int i = 0; i < chunks; i++) {
                mergeCommand.append(" ").append(escapeShellPath(tempDir + "/chunk_" + i));
            }
            mergeCommand.append(" > ").append(escapeShellPath(remotePath));

            boolean mergeSuccess = executeCommand(host, mergeCommand.toString(), null);
            if (!mergeSuccess) {
                log.error("合并文件失败：{}，临时文件保留在：{}", remotePath, tempDir);
                return false;
            }

            // 合并成功后删除临时文件
            if (!executeCommand(host, "rm -rf " + escapedTempDir, null)) {
                log.warn("清理临时文件失败：{}", tempDir);
            }

            return true;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            // 出现异常时保留临时文件以便排查问题
            log.error("临时文件目录：{}", tempDir);
            return false;
        }
    }

    /**
     * 普通方式上传文件（不分片）
     */
    private static boolean uploadFileNormal(SysAiTaskHost host, String localPath, String remotePath) {
        Session session = null;
        ChannelSftp channel = null;
        try {
            session = createSession(host);
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            // 创建远程目录
            String remoteDir = remotePath.substring(0, remotePath.lastIndexOf("/"));
            try {
                channel.mkdir(remoteDir);
            } catch (SftpException e) {
                // 目录已存在，忽略异常
            }

            // 上传文件
            channel.put(localPath, remotePath);
            return true;
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return false;
        } finally {
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }

    /**
     * 上传文件分片
     */
    private static boolean uploadChunk(SysAiTaskHost host, File localFile, String chunkPath, int chunkIndex, int chunkSize) {
        Session session = null;
        ChannelSftp channel = null;
        try {
            session = createSession(host);
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            // 计算分片范围
            long start = (long) chunkIndex * chunkSize;
            long end = Math.min(start + chunkSize, localFile.length());
            int length = (int) (end - start);

            // 读取分片数据
            try (RandomAccessFile raf = new RandomAccessFile(localFile, "r")) {
                raf.seek(start);
                byte[] buffer = new byte[length];
                raf.read(buffer);

                // 上传分片
                try (ByteArrayInputStream bis = new ByteArrayInputStream(buffer)) {
                    log.debug("上传分片: {} (大小: {} bytes)", chunkPath, length);
                    channel.put(bis, chunkPath);
                    log.debug("分片上传成功: {}", chunkPath);
                }
            }

            return true;
        } catch (Exception e) {
            log.error("上传分片失败：chunk_{}, 目标路径: {}, 错误信息: {}", chunkIndex, chunkPath, e.getMessage(), e);
            return false;
        } finally {
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }

    /**
     * 获取主机资源使用情况
     *
     * @param host 主机信息
     * @return [CPU使用率, 内存使用率, 磁盘使用率]
     */
    public static double[] getHostResource(SysAiTaskHost host) {
        double[] resources = new double[3];
        
        // 获取CPU使用率
        String cpuCommand = "top -bn1 | grep 'Cpu(s)' | awk '{print $2}'";
        executeCommand(host, cpuCommand, line -> {
            try {
                resources[0] = Double.parseDouble(line);
            } catch (NumberFormatException e) {
                resources[0] = 0.0;
            }
        });

        // 获取内存使用率
        String memCommand = "free | grep Mem | awk '{print $3/$2 * 100.0}'";
        executeCommand(host, memCommand, line -> {
            try {
                resources[1] = Double.parseDouble(line);
            } catch (NumberFormatException e) {
                resources[1] = 0.0;
            }
        });

        // 获取磁盘使用率
        String diskCommand = "df -h / | tail -1 | awk '{print $5}' | sed 's/%//'";
        executeCommand(host, diskCommand, line -> {
            try {
                resources[2] = Double.parseDouble(line);
            } catch (NumberFormatException e) {
                resources[2] = 0.0;
            }
        });

        return resources;
    }

    /**
     * 测试SSH连接
     *
     * @param host 主机信息
     * @return 是否连接成功
     */
    public static boolean testConnection(SysAiTaskHost host) {
        Session session = null;
        try {
            session = createSession(host);
            return true;
        } catch (Exception e) {
            log.error("测试SSH连接失败：{}({})", host.getHostName(), host.getHostIp(), e);
            return false;
        } finally {
            if (session != null) {
                session.disconnect();
            }
        }
    }

    /**
     * 转义Shell路径，处理中文和特殊字符
     * @param path 原始路径
     * @return 转义后的路径
     */
    private static String escapeShellPath(String path) {
        if (path == null || path.isEmpty()) {
            return path;
        }
        // 使用单引号包围路径，并处理路径中的单引号
        // 将单引号替换为 '\''（结束当前引号，添加转义的单引号，开始新引号）
        String escapedPath = path.replace("'", "'\\''");
        return "'" + escapedPath + "'";
    }

    /**
     * 创建SSH会话
     */
    private static Session createSession(SysAiTaskHost host) throws JSchException {
        JSch jsch = new JSch();
        Session session = jsch.getSession(host.getSshUsername(), host.getHostIp(), host.getSshPort());
        session.setPassword(host.getSshPassword());

        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);

        session.connect(300000 * 4);
        return session;
    }

    /**
     * 从远程服务器下载文件
     *
     * @param host 主机信息
     * @param remotePath 远程文件路径
     * @param localPath 本地保存路径
     * @return 是否成功
     */
    public static boolean downloadFile(SysAiTaskHost host, String remotePath, String localPath) {
        Session session = null;
        ChannelSftp channel = null;
        try {
            session = createSession(host);
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            // 确保本地目录存在
            File localFile = new File(localPath);
            localFile.getParentFile().mkdirs();

            // 下载文件
            channel.get(remotePath, localPath);
            return true;
        } catch (Exception e) {
            log.error("下载文件失败: {} -> {}", remotePath, localPath, e);
            return false;
        } finally {
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }
} 