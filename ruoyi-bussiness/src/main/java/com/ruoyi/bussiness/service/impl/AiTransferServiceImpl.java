package com.ruoyi.bussiness.service.impl;

import com.ruoyi.bussiness.enums.TransferStatusEnum;
import com.ruoyi.bussiness.model.constants.MinioConstant;
import com.ruoyi.bussiness.model.dto.AiTransferRequest;
import com.ruoyi.bussiness.model.dto.TaskInfoDTO;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import com.ruoyi.bussiness.model.entity.SysAiTransferRecord;
import com.ruoyi.bussiness.model.enums.FileTypeEnum;
import com.ruoyi.bussiness.model.param.InitTaskParam;
import com.ruoyi.bussiness.service.IAiTransferService;
import com.ruoyi.bussiness.service.ISysAiTaskHostService;
import com.ruoyi.bussiness.service.ISysAiTransferRecordService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.bussiness.utils.MinioFileUtils;
import com.ruoyi.bussiness.utils.SshUtil;
import com.ruoyi.bussiness.websocket.LogWebSocketHandler;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * 模型转换服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AiTransferServiceImpl implements IAiTransferService {

    @Autowired
    private ISysAiTaskHostService sysAiTaskHostService;

    @Autowired
    private ISysAiTransferRecordService sysAiTransferLogService;

    @Autowired
    private LogWebSocketHandler logWebSocketHandler;

    @Autowired
    private SysUploadTaskService sysUploadTaskService;

    @Autowired
    private ISysConfigService sysConfigService;

    /** 文件下载过期时间（秒） */
    private static final long FILE_DOWNLOAD_EXPIRES = 3600L;
    /** 日志文件名格式 */
    private static final String LOG_FILE_NAME_FORMAT = "transfer_log_%s.log";
    /** 日志时间戳格式 */
    private static final DateTimeFormatter LOG_TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /** MinIO日志文件标识符后缀 */
    private static final String LOG_IDENTIFIER_SUFFIX = "_log";
    /** 执行主机的mobilenet目录路径格式 */
    private static final String MOBILENET_DIR_FORMAT = "%s/mobilenet/%s";
    /** lfQdata文件扩展名 */
    private static final String LFQDATA_EXTENSION = ".lfQdata";
    /** 环境变量设置命令模板 */
    private static final String ENV_SETUP_COMMAND = "source /etc/profile && export LD_LIBRARY_PATH=%s/CDNN_CONVERT/Dll/LINUX:$LD_LIBRARY_PATH && cd %s/CDNN_CONVERT/exe/ && %s";
    /** 模型转换器基础命令 */
//    private static final String BASE_GENERATOR_COMMAND = "%s/CDNN_CONVERT/exe/LFNetworkGenerator --network %s --input %s -p xm6 -h nprs -d 512 --profiler long -c -t 8bit --FreqAndAXI 1000 256";
    private static final String BASE_GENERATOR_COMMAND = "%s/%s --network %s --input %s %s";

    /** 任务队列 */
    private final BlockingQueue<TransferTask> taskQueue = new LinkedBlockingQueue<>();
    
    /** 任务处理线程是否运行中 */
    private volatile boolean isRunning = true;
    
    /** 任务执行线程池 */
    private ThreadPoolExecutor taskExecutor;
    
    /** 任务执行结果映射 */
    private final ConcurrentHashMap<String, Future<?>> taskFutures = new ConcurrentHashMap<>();

    /**
     * 初始化任务处理相关资源
     */
    @PostConstruct
    public void init() {
        // 创建任务执行线程池
        taskExecutor = new ThreadPoolExecutor(
            5,                      // 核心线程数
            20,                     // 最大线程数
            60L,                    // 空闲线程存活时间
            TimeUnit.SECONDS,       // 时间单位
            new LinkedBlockingQueue<>(), // 任务队列
            r -> {
                Thread thread = new Thread(r);
                thread.setName("transfer-task-executor-" + thread.getId());
                return thread;
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
        
        // 启动任务分发线程
        Thread dispatcherThread = new Thread(this::dispatchTasks);
        dispatcherThread.setName("transfer-task-dispatcher");
        dispatcherThread.setDaemon(true);
        dispatcherThread.start();
        
        log.info("模型转换任务处理系统已初始化，核心线程数：{}，最大线程数：{}", 
                taskExecutor.getCorePoolSize(), taskExecutor.getMaximumPoolSize());
    }
    
    /**
     * 销毁资源
     */
    @PreDestroy
    public void destroy() {
        isRunning = false;
        if (taskExecutor != null) {
            taskExecutor.shutdown();
            try {
                if (!taskExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    taskExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                taskExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("模型转换任务处理系统已关闭");
    }
    
    /**
     * 任务分发处理
     */
    private void dispatchTasks() {
        while (isRunning) {
            try {
                // 从队列中获取任务
                TransferTask task = taskQueue.take();
                log.info("开始分发模型转换任务: {}", task.taskId);
                
                // 获取可用主机
                SysAiTaskHost host = sysAiTaskHostService.getAvailableHost();
                if (host == null) {
                    handleNoAvailableHost(task);
                    continue;
                }
                
                // 检查主机队列是否已满
                int currentUserCount = host.getUserList() == null ? 0 : host.getUserList().size();
                if (host.getMaxQueueSize() != null && currentUserCount >= host.getMaxQueueSize()) {
                    handleHostQueueFull(task, host);
                    continue;
                }
                
                // 提交任务到线程池执行
                Future<?> future = taskExecutor.submit(() -> {
                    String taskUniqueId = task.username + task.taskId;
                    long startTime = System.currentTimeMillis();
                    
                    try {
                        // 将当前用户添加到主机的userList中
                        sysAiTaskHostService.addUserToHost(host, taskUniqueId);
                        
                        // 执行转换任务
                        executeTransferTask(host, task.transferLog, task.taskId, taskUniqueId,
                                task.modelIdentifier, task.configIdentifier, task.imageIdentifier, startTime, task.toolIdentifier);
                    } catch (Exception e) {
                        log.error("执行转换任务时发生异常: {}", task.taskId, e);
                        handleTaskException(task, e);
                    } finally {
                        // 执行完成后，将用户从主机的userList中移除
                        sysAiTaskHostService.removeUserFromHost(host, taskUniqueId);
                        // 从任务映射中移除
                        taskFutures.remove(task.taskId);
                    }
                });
                
                // 将任务future保存到映射中
                taskFutures.put(task.taskId, future);
                
            } catch (InterruptedException e) {
                log.error("任务分发线程被中断", e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("任务分发过程中发生异常", e);
            }
        }
    }
    
    /**
     * 处理没有可用主机的情况
     */
    private void handleNoAvailableHost(TransferTask task) {
        log.error("没有可用的执行主机，任务: {} 无法执行", task.taskId);
        logWebSocketHandler.sendMessage(task.taskId, "没有可用的执行主机，请稍后重试");
        task.transferLog.setRemark("没有可用的执行主机");
        sysAiTransferLogService.updateLog(task.transferLog);
    }
    
    /**
     * 处理主机队列已满的情况
     */
    private void handleHostQueueFull(TransferTask task, SysAiTaskHost host) {
        log.info("主机 {}({}) 队列已满，任务: {} 将重新排队", host.getHostName(), host.getHostIp(), task.taskId);
        logWebSocketHandler.sendMessage(task.taskId, "执行主机队列已满，任务重新排队等待...");
        taskQueue.offer(task);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 处理任务执行异常
     */
    private void handleTaskException(TransferTask task, Exception e) {
        logWebSocketHandler.sendMessage(task.taskId, "执行异常：" + e.getMessage());
        task.transferLog.setRemark("执行异常：" + e.getMessage());
        task.transferLog.setStatus(TransferStatusEnum.FAILED.getCode());
        sysAiTransferLogService.updateLog(task.transferLog);
    }

    @Override
    public String executeTransfer(AiTransferRequest request) {
        // 获取参数
        String modelIdentifier = request.getModelIdentifier();
        String configIdentifier = request.getConfigIdentifier();
        List<String> imageIdentifier = request.getImageIdentifier();
        String taskId = request.getTaskId();
        String toolIdentifier = request.getToolIdentifier();

        // 获取当前用户
        String currentUser = SecurityUtils.getUsername();

        // 在独立事务中创建转换日志记录，确保无论后续操作是否成功都能保存日志
        SysAiTransferRecord transferLog = createAndSaveTransferRecord(currentUser, taskId, modelIdentifier, configIdentifier, imageIdentifier);

        // 创建任务并添加到队列
        TransferTask task = new TransferTask(taskId, modelIdentifier, configIdentifier, imageIdentifier, currentUser, transferLog, toolIdentifier);
        taskQueue.offer(task);

        log.info("任务 {} 已添加到队列，当前队列长度: {}", taskId, taskQueue.size());
        logWebSocketHandler.sendMessage(taskId, "任务已加入队列，等待处理...");

        return taskId;
    }

    /**
     * 在独立事务中创建并保存转换记录
     * 使用 REQUIRES_NEW 传播级别确保无论外部事务如何，此操作都能独立提交
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public SysAiTransferRecord createAndSaveTransferRecord(String currentUser, String taskId, String modelIdentifier, String configIdentifier, List<String> imageIdentifier) {
        SysAiTransferRecord transferLog = createTransferRecord(currentUser, taskId, modelIdentifier, configIdentifier, imageIdentifier);
        sysAiTransferLogService.insertLog(transferLog);
        log.info("转换日志记录已创建并保存，任务ID: {}", taskId);
        return transferLog;
    }

    /**
     * 创建转换记录
     */
    private SysAiTransferRecord createTransferRecord(String currentUser, String taskId, String modelIdentifier, String configIdentifier, List<String> imageIdentifier) {
        // 获取登录地址，参考AsyncFactory中recordLogininfor的实现
        String ip = IpUtils.getIpAddr();
        String loginLocation = AddressUtils.getRealAddressByIP(ip);

        return SysAiTransferRecord.builder()
                .username(currentUser)
                .taskId(taskId)
                .modelIdentifier(modelIdentifier)
                .configIdentifier(configIdentifier)
                .imageIdentifiers(imageIdentifier)
                // 后面设置的taskId为上传文件的identifier，便于后续查找
                .transferModelIdentifier(taskId)
                .optLocation(loginLocation)
                .createBy(currentUser)
                .updateBy(currentUser)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                // 初始状态为失败
                .status(TransferStatusEnum.EXECUTING.getCode())
                .build();
    }

    /**
     * 执行转换任务
     */
    private void executeTransferTask(SysAiTaskHost host, SysAiTransferRecord transferLog, String taskId, String taskUniqueId,
                                     String modelIdentifier, String configIdentifier, List<String> imageIdentifier, long startTime, String toolIdentifier) {
        Path tempDir = null;
        File logFile = null;
        try {
            // 创建临时目录
            tempDir = Files.createTempDirectory("aitransfer_");
            // 创建日志文件路径
            String logFileName = String.format(LOG_FILE_NAME_FORMAT, taskId);
            Path logFilePath = tempDir.resolve(logFileName);
            logFile = logFilePath.toFile();

            // 下载所有文件到临时目录
            String modelPath = downloadFile(modelIdentifier, tempDir, FileTypeEnum.MODEL.getCode());
            String configPath = downloadFile(configIdentifier, tempDir, FileTypeEnum.CONFIG.getCode());
            List<String> imagePaths = downloadFiles(imageIdentifier, tempDir, FileTypeEnum.IMAGE.getCode());
            String toolPath = null;
            if (StringUtils.hasText(toolIdentifier)) {
                toolPath = downloadFile(toolIdentifier, tempDir, FileTypeEnum.MODEL_TRANSFER_TOOL.getCode());
            }

            // 清理远程工作目录
            boolean bResult = cleanRemoteWorkDir(host, taskUniqueId);
            if (!bResult) {
                throw new RuntimeException("清理远程工作目录失败");
            }

            // 推送文件到远程服务器
            String modelFileName = Paths.get(modelPath).getFileName().toString();
            String configFileName = Paths.get(configPath).getFileName().toString();
            String remoteModelPath = uploadFile(host, modelPath, modelFileName, taskUniqueId);
            String remoteConfigPath = uploadFile(host, configPath, configFileName, taskUniqueId);
            List<String> remoteImagePaths = uploadFiles(host, imagePaths, "", taskUniqueId);

            // 如果有工具文件，也推送到远程服务器
            String remoteToolPath = null;
            if (toolPath != null) {
                String toolFileName = Paths.get(toolPath).getFileName().toString();
                remoteToolPath = uploadFile(host, toolPath, toolFileName, taskUniqueId);
                // 给工具文件设置可执行权限
                if (remoteToolPath != null) {
                    boolean chmodToolResult = SshUtil.executeCommand(host, "chmod +x " + remoteToolPath, null);
                    if (!chmodToolResult) {
                        log.warn("赋予工具文件可执行权限失败：{}", remoteToolPath);
                    }
                }
            }
            
            // 进入到host.getExecutablePath()并创建用户目录
            String userDir = String.format(MOBILENET_DIR_FORMAT, host.getExecutablePath(), taskUniqueId);
            boolean mkResult = SshUtil.executeCommand(host, "mkdir -p " + userDir, null);
            if (!mkResult) {
                throw new RuntimeException("创建用户目录失败：" + userDir);
            }

            // 将上传的模型文件赋予可执行的权限
            boolean chmodhResult = SshUtil.executeCommand(host, "chmod +x " + remoteModelPath, null);
            if (!chmodhResult) {
                throw new RuntimeException("赋予模型文件可执行权限失败：" + remoteModelPath);
            }

            // 构建执行命令
            String command = buildCommand(host.getExecutablePath(), remoteModelPath, remoteConfigPath, taskUniqueId, toolIdentifier);
            // 执行命令并处理日志
            String fullCommand = String.format(ENV_SETUP_COMMAND, host.getExecutablePath(), host.getExecutablePath(), command);
            File finalLogFile = logFile;
            boolean success = SshUtil.executeCommand(host, "bash -c '" + fullCommand + "'", logger -> {
                logWebSocketHandler.sendMessage(taskId, logger);
                appendToLogFile(finalLogFile, logger);
            });

            // 如果命令执行成功，上传.lfQdata文件到MinIO
            if (success) {
                uploadModelToMinio(host, taskId, taskUniqueId);
                // 更新转换日志状态为成功
                transferLog.setStatus(TransferStatusEnum.SUCCESS.getCode());
                transferLog.setRemark("模型转换执行成功");
                logWebSocketHandler.sendMessage(taskId, "模型转换执行成功");
            } else {
                logWebSocketHandler.sendMessage(taskId, "模型转换执行失败");
                transferLog.setStatus(TransferStatusEnum.FAILED.getCode());
                transferLog.setRemark("模型转换执行失败");
            }
            
            // 计算耗时（秒）
            long endTime = System.currentTimeMillis();
            transferLog.setCosts((endTime - startTime) / 1000);
            
        } catch (Exception e) {
            transferLog.setRemark("执行异常：" + e.getMessage());
            transferLog.setStatus(TransferStatusEnum.FAILED.getCode());
            throw new RuntimeException("执行转换任务失败;" + e.getMessage());
        } finally {
            try {
                // 将本地临时日志文件上传到MinIO
                if (logFile != null && logFile.exists()) {
                    String logIdentifier = uploadLogFileToMinio(logFile, taskId);
                    if (logIdentifier != null) {
                        transferLog.setLogIdentifier(logIdentifier);
                    }
                }
                
                // 更新转换日志
                sysAiTransferLogService.updateLog(transferLog);
                
                // 清理临时目录
                if (tempDir != null) {
                    FileUtils.deleteDirectory(tempDir.toFile());
                }
            } catch (Exception e) {
                log.error("清理资源失败", e);
            }
        }
    }

    /**
     * 上传转换后模型文件到MinIO
     */
    private void uploadModelToMinio(SysAiTaskHost host, String taskId, String taskUniqueId) {
        try {
            // 获取远程服务器上的.lfQdata文件
            String remoteDir = String.format(MOBILENET_DIR_FORMAT, host.getExecutablePath(), taskUniqueId);
            String findCommand = "find " + remoteDir + " -name '*" + LFQDATA_EXTENSION + "'";
            List<String> lfQdataFiles = new ArrayList<>();

            SshUtil.executeCommand(host, findCommand, log -> {
                if (log != null && log.trim().endsWith(LFQDATA_EXTENSION)) {
                    lfQdataFiles.add(log.trim());
                }
            });

            // 为每个找到的文件创建上传任务
            for (String remoteFile : lfQdataFiles) {
                uploadSingleModelFile(remoteFile, host, taskId);
            }
        } catch (Exception e) {
            log.error("上传文件到MinIO失败", e);
            logWebSocketHandler.sendMessage(taskId, "上传文件到MinIO失败: " + e.getMessage());
        }
    }
    
    /**
     * 上传单个模型文件
     */
    private void uploadSingleModelFile(String remoteFile, SysAiTaskHost host, String taskId) {
        // 获取文件名
        String fileName = remoteFile.substring(remoteFile.lastIndexOf('/') + 1);
        
        // 创建临时目录用于存放下载的文件
        String tempDirMion = System.getProperty("java.io.tmpdir");
        String localFilePath = tempDirMion + File.separator + fileName;
        File localFile = new File(localFilePath);
        
        try {
            // 从远程服务器下载文件到本地临时目录
            if (SshUtil.downloadFile(host, remoteFile, localFilePath)) {
                // 初始化上传任务
                InitTaskParam initTaskParam = new InitTaskParam();
                initTaskParam.setIdentifier(taskId);
                initTaskParam.setFileName(fileName);
                initTaskParam.setTotalSize(localFile.length());
                initTaskParam.setChunkSize((long) MinioConstant.DEFAULT_CHUNK_SIZE);
                initTaskParam.setFileType(FileTypeEnum.TRANSFER_MODEL.getCode());

                // 创建上传任务
                TaskInfoDTO taskInfo = sysUploadTaskService.initTask(initTaskParam);

                // 上传文件
                if (initTaskParam.getTotalSize() <= MinioConstant.DEFAULT_CHUNK_SIZE) {
                    uploadSingleChunk(localFile, taskInfo);
                } else {
                    MinioFileUtils.uploadLargeFile(localFilePath, taskInfo, sysUploadTaskService);
                    // 大文件上传已经在uploadLargeFile方法中调用了merge，不需要再调用
                    logWebSocketHandler.sendMessage(taskId, "文件 " + fileName + " 已成功上传到MinIO");
                    return;
                }
                
                // 合并分片，使用正确的文件标识符
                sysUploadTaskService.merge(taskId, null);
                
                logWebSocketHandler.sendMessage(taskId, "文件 " + fileName + " 已成功上传到MinIO");
            }
        } catch (Exception e) {
            log.error("处理模型文件失败: {}", fileName, e);
            logWebSocketHandler.sendMessage(taskId, "处理模型文件失败: " + e.getMessage());
        } finally {
            // 删除临时文件
            try {
                Files.deleteIfExists(Paths.get(localFilePath));
            } catch (IOException e) {
                log.warn("删除临时文件失败: {}", localFilePath, e);
            }
        }
    }

    /**
     * 下载单个文件
     */
    private String downloadFile(String identifier, Path tempDir, String fileType) throws IOException {
        String downloadUrl = sysUploadTaskService.genPreSignDownloadUrl(identifier, FILE_DOWNLOAD_EXPIRES, false, fileType);
        String fileName = sysUploadTaskService.getByIdentifier(identifier).getFileName();
        Path filePath = tempDir.resolve(fileName);
        FileUtils.copyURLToFile(new URL(downloadUrl), filePath.toFile());
        return filePath.toString();
    }

    /**
     * 下载多个文件
     */
    private List<String> downloadFiles(List<String> identifiers, Path tempDir, String fileType) throws IOException {
        return identifiers.stream()
                .map(identifier -> {
                    try {
                        return downloadFile(identifier, tempDir, fileType);
                    } catch (IOException e) {
                        throw new RuntimeException("下载文件失败：" + e.getMessage(), e);
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 清理远程工作目录
     */
    private boolean cleanRemoteWorkDir(SysAiTaskHost host, String taskUniqueId) {
        // 清理mobilenet目录下的所有内容
        String mobilenetPath = String.format(MOBILENET_DIR_FORMAT, host.getExecutablePath(), taskUniqueId);
        boolean bResult = SshUtil.executeCommand(host, "rm -rf " + mobilenetPath + "/* && mkdir -p " + mobilenetPath, null);
        log.info("已清理主机 {}({}) 的mobilenet目录内容", host.getHostName(), host.getHostIp());
        return bResult;
    }
    
    /**
     * 上传单个文件到远程服务器
     */
    private String uploadFile(SysAiTaskHost host, String localPath, String remoteFileName, String taskUniqueId) {
        String remotePath = String.format(MOBILENET_DIR_FORMAT, host.getExecutablePath(), taskUniqueId) + "/" + remoteFileName;
        boolean success = SshUtil.uploadFile(host, localPath, remotePath);
        if (!success) {
            throw new RuntimeException("上传文件失败：" + localPath);
        }
        return remotePath;
    }

    /**
     * 上传多个文件到远程服务器
     */
    private List<String> uploadFiles(SysAiTaskHost host, List<String> localPaths, String remoteFilePrefix, String taskUniqueId) {
        return localPaths.stream()
                .map(localPath -> {
                    String fileName = Paths.get(localPath).getFileName().toString();
                    return uploadFile(host, localPath, remoteFilePrefix + fileName, taskUniqueId);
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建执行命令
     */
    private String buildCommand(String executablePath, String modelPath, String configPath, String taskUniqueId, String toolIdentifier) {
        //获取执行指令拼接信息
        String toolParams = sysConfigService.selectConfigByKey(toolIdentifier);
        if (Objects.isNull(toolParams) || toolParams.isEmpty()) {
            throw new RuntimeException("未找到工具执行指令的参数配置");
        }
        //使用;分割字符串  （第一个参数为执行文件的路径，第二个参数为执行文件的参数 配置）
        String[] toolParamsArray = toolParams.split(";");
        String toolPath = toolParamsArray[0];
        String toolArgs = toolParamsArray[1];

        String inputDir = String.format(MOBILENET_DIR_FORMAT, executablePath, taskUniqueId);
        return String.format(BASE_GENERATOR_COMMAND, executablePath, toolPath, modelPath, inputDir, toolArgs);
    }

    /**
     * 将日志追加到本地文件
     */
    private void appendToLogFile(File logFile, String logMessage) {
        try (PrintWriter out = new PrintWriter(new BufferedWriter(new FileWriter(logFile, true)))) {
            String timestamp = LocalDateTime.now().format(LOG_TIMESTAMP_FORMAT);
            out.println(String.format("[%s] %s", timestamp, logMessage));
        } catch (IOException e) {
            log.error("写入日志文件失败: {}", logFile.getName(), e);
        }
    }

    /**
     * 上传日志文件到MinIO
     * @return 日志文件标识符
     */
    private String uploadLogFileToMinio(File logFile, String taskId) {
        if (!logFile.exists() || logFile.length() == 0) {
            log.warn("日志文件不存在或为空: {}", logFile.getName());
            return null;
        }

        String identifier = taskId + LOG_IDENTIFIER_SUFFIX;
        try {
            TaskInfoDTO taskInfo = initializeUploadTask(logFile, identifier);
            uploadFileToMinio(logFile, taskInfo, identifier);
            log.info("日志文件 {} 已成功上传到MinIO", logFile.getName());
            return identifier;
        } catch (Exception e) {
            log.error("上传日志文件到MinIO失败: {}", logFile.getName(), e);
            return null;
        }
    }

    /**
     * 初始化MinIO上传任务
     */
    private TaskInfoDTO initializeUploadTask(File file, String identifier) {
        InitTaskParam initTaskParam = new InitTaskParam();
        initTaskParam.setIdentifier(identifier);
        initTaskParam.setFileName(file.getName());
        initTaskParam.setTotalSize(file.length());
        initTaskParam.setChunkSize((long) MinioConstant.DEFAULT_CHUNK_SIZE);
        initTaskParam.setFileType(FileTypeEnum.TRANSFER_MODEL_LOG.getCode());

        return sysUploadTaskService.initTask(initTaskParam);
    }

    /**
     * 执行文件上传到MinIO
     */
    private void uploadFileToMinio(File file, TaskInfoDTO taskInfo, String identifier) throws IOException {
        if (file.length() <= MinioConstant.DEFAULT_CHUNK_SIZE) {
            uploadSingleChunk(file, taskInfo);
            // 确保分片上传完成后再合并
            try {
                Thread.sleep(500); // 添加短暂延迟，确保分片上传完成
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        } else {
            MinioFileUtils.uploadLargeFile(file.getAbsolutePath(), taskInfo, sysUploadTaskService);
            // 大文件上传已经在uploadLargeFile方法中调用了merge，不需要再次调用
            return;
        }
        sysUploadTaskService.merge(identifier,null);
    }

    /**
     * 上传单个分片到MinIO
     */
    private void uploadSingleChunk(File file, TaskInfoDTO taskInfo) throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put("partNumber", "1");
        params.put("uploadId", taskInfo.getTaskRecord().getUploadId());

        String uploadUrl = sysUploadTaskService.genPreSignUploadUrl(
            taskInfo.getTaskRecord().getBucketName(),
            taskInfo.getTaskRecord().getObjectKey(),
            params
        );

        MinioFileUtils.uploadToPresignedUrl(uploadUrl, file);
    }
    /**
     * 任务对象，包含执行转换所需的所有参数
     */
    private static class TransferTask {
        private final String taskId;
        private final String modelIdentifier;
        private final String configIdentifier;
        private final List<String> imageIdentifier;
        private final String username;
        private final SysAiTransferRecord transferLog;
        private final String toolIdentifier;


        public TransferTask(String taskId, String modelIdentifier, String configIdentifier,
                            List<String> imageIdentifier, String username, SysAiTransferRecord transferLog, String toolIdentifier) {
            this.taskId = taskId;
            this.modelIdentifier = modelIdentifier;
            this.configIdentifier = configIdentifier;
            this.imageIdentifier = imageIdentifier;
            this.username = username;
            this.transferLog = transferLog;
            this.toolIdentifier = toolIdentifier;
        }
    }
}
