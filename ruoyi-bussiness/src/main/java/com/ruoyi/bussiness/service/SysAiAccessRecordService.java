package com.ruoyi.bussiness.service;

import com.ruoyi.bussiness.model.entity.SysAiAccessRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【sys_ai_access_record】的数据库操作Service
* @createDate 2025-06-26 09:56:53
*/
public interface SysAiAccessRecordService extends IService<SysAiAccessRecord> {

    /**
     * 保存或者更新如果存在 accessType类型的数据则更新accessCount + 1；如果不存在则新增一条数据并且设置accessCount为1
     * @param sysAiAccessRecord
     * @return
     */
    int saveOrUpdateCustom(SysAiAccessRecord sysAiAccessRecord);

}
