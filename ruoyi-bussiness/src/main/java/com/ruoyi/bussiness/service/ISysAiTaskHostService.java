package com.ruoyi.bussiness.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;

import java.util.List;

/**
 * AI任务主机Service接口
 *
 * <AUTHOR>
 */
public interface ISysAiTaskHostService extends IService<SysAiTaskHost> {

    /**
     * 获取可用的主机
     *
     * @return 可用的主机信息
     */
    SysAiTaskHost getAvailableHost();

    /**
     * 更新主机状态
     *
     * @param hostId 主机ID
     * @param status 状态
     * @return 更新结果
     */
    boolean updateHostStatus(Long hostId, String status);

    /**
     * 更新主机资源使用情况
     *
     * @param hostId 主机ID
     * @param cpuUsage CPU使用率
     * @param memoryUsage 内存使用率
     * @param diskUsage 磁盘使用率
     * @return 更新结果
     */
    boolean updateHostResource(Long hostId, Double cpuUsage, Double memoryUsage, Double diskUsage);

    /**
     * 查询
     * @param host  主机任务对象
     * @return
     */
    List<SysAiTaskHost> listQuery(SysAiTaskHost host);

    /**
     * 新增逻辑
     * @param sysAiTaskHost
     * @return
     */
    int saveLogic(SysAiTaskHost sysAiTaskHost);

    /**
     * 上传可执行文件逻辑
     * @param sysAiTaskHost
     * @return
     */
    int uploadExecutable(SysAiTaskHost sysAiTaskHost);

    /**
     * 查询空闲主机
     * @return
     */
    List<SysAiTaskHost> listHostIdle();

    /**
     * 将当前用户添加到主机的userList中
     *
     * @param host 主机
     * @return 更新后的主机信息
     */
    SysAiTaskHost addUserToHost(SysAiTaskHost host, String currentUser);

    /**
     * 将当前用户从主机的userList中移除
     *
     * @param host        主机
     * @param currentUser
     * @return 更新后的主机信息
     */
    SysAiTaskHost removeUserFromHost(SysAiTaskHost host, String currentUser);

}