package com.ruoyi.bussiness.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.bussiness.model.entity.SysLfnnVersion;

import java.util.List;

/**
 * LFNN版本Service接口
 *
 * <AUTHOR>
 */
public interface ISysLfnnVersionService extends IService<SysLfnnVersion> {
    /**
     * 查询LFNN版本列表
     *
     * @param lfnnVersion LFNN版本信息
     * @return LFNN版本集合
     */
    List<SysLfnnVersion> listPage(SysLfnnVersion lfnnVersion);

    /**
     * 保存自定义LFNN版本
     *
     * @param lfnnVersion LFNN版本信息
     * @return 是否保存成功
     */
    boolean saveCustom(SysLfnnVersion lfnnVersion);

    /**
     * 根据ID列表批量删除LFNN版本
     *
     * @param list LFNN版本ID列表
     * @return 是否删除成功
     */
    boolean removeByIdsCustom(List<Long> list);

    /**
     * 更新LFNN版本信息
     * 同时更新对应的上传任务标识
     *
     * @param lfnnVersion LFNN版本信息
     * @return 是否更新成功
     */
    boolean updateByIdCustom(SysLfnnVersion lfnnVersion);

    /**
     * 获取下载链接
     *
     * @param identifier LFNN版本标识符
     * @return 下载链接
     */
    String getDownloadUrl(String identifier);
}
