package com.ruoyi.bussiness.service;

import com.ruoyi.bussiness.model.vo.*;

/**
 * 报表服务接口
 *
 * <AUTHOR>
 */
public interface IReportService {

    /**
     * 获取模型转换地区分布数据
     *
     * @param dataType 数据类型: conversion(模型转换次数)或download(SDK下载次数)
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 地区分布数据
     */
    ReportRegionDataVO getRegionDistribution(String dataType, String startTime, String endTime);

    /**
     * 获取平台使用趋势数据
     *
     * @param timeUnit 时间单位：day(日)、month(月)、year(年)
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 趋势数据
     */
    ReportTrendDataVO getTrendData(String timeUnit, String startTime, String endTime);

    /**
     * 获取各模块访问分布数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 模块访问分布数据
     */
    ReportModuleDataVO getModuleDistribution(String startTime, String endTime);

    /**
     * 获取用户类型分布数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户类型分布数据
     */
    ReportUserTypeDataVO getUserTypeDistribution(String startTime, String endTime);

    /**
     * 获取模型转换成功率数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 转换成功率数据
     */
    ReportConversionRateVO getConversionRate(String startTime, String endTime);

    /**
     * 获取基础统计数据（注册用户总数、用户操作总次数）
     *
     * @param startTime 开始时间（用于操作次数统计）
     * @param endTime 结束时间（用于操作次数统计）
     * @return 基础统计数据
     */
    ReportBasicStatsVO getBasicStats(String startTime, String endTime);
}
