package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.model.entity.SysAiTransferRecord;
import com.ruoyi.bussiness.model.entity.SysDownloadRecord;
import com.ruoyi.bussiness.service.SysDownloadRecordService;
import com.ruoyi.bussiness.mapper.SysDownloadRecordMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_download_record】的数据库操作Service实现
* @createDate 2025-06-04 14:42:05
*/
@Service
public class SysDownloadRecordServiceImpl extends ServiceImpl<SysDownloadRecordMapper, SysDownloadRecord> implements SysDownloadRecordService{

    @Override
    public List<SysDownloadRecord> listPage(SysDownloadRecord downloadRecord) {
        //通过下载类型、下载文件名、下载用户筛选
        LambdaQueryWrapper<SysDownloadRecord> queryWrapper = new LambdaQueryWrapper<>();
        // 下载类型条件
        queryWrapper.eq(StringUtils.isNotBlank(downloadRecord.getDownloadType()),
                SysDownloadRecord::getDownloadType, downloadRecord.getDownloadType());
        // 下载文件名模糊查询
        queryWrapper.like(StringUtils.isNotBlank(downloadRecord.getDownloadFileName()),
                SysDownloadRecord::getDownloadFileName, downloadRecord.getDownloadFileName());
        // 下载用户条件
        queryWrapper.eq(StringUtils.isNotBlank(downloadRecord.getUsername()),
                SysDownloadRecord::getUsername, downloadRecord.getUsername());

        // 根据创建时间查询 在startTime和endTime之间
        queryWrapper.ge(downloadRecord.getStartTime() != null, SysDownloadRecord::getCreateTime, downloadRecord.getStartTime());
        queryWrapper.le(downloadRecord.getEndTime() != null, SysDownloadRecord::getCreateTime, downloadRecord.getEndTime());
        return this.list(queryWrapper);
    }
}

