package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.mapper.SysLfnnVersionMapper;
import com.ruoyi.bussiness.model.entity.SysLfnnVersion;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.service.ISysLfnnVersionService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * LFNN版本Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysLfnnVersionServiceImpl extends ServiceImpl<SysLfnnVersionMapper, SysLfnnVersion> implements ISysLfnnVersionService {

    @Autowired
    private SysUploadTaskService uploadTaskService;


    @Override
    public List<SysLfnnVersion> listPage(SysLfnnVersion lfnnVersion) {
        LambdaQueryWrapper<SysLfnnVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(Objects.nonNull(lfnnVersion.getLfnnVersion()), SysLfnnVersion::getLfnnVersion, lfnnVersion.getLfnnVersion());
        queryWrapper.eq(Objects.nonNull(lfnnVersion.getCreateBy()), SysLfnnVersion::getCreateBy, lfnnVersion.getCreateBy());
        queryWrapper.eq(Objects.nonNull(lfnnVersion.getChipType()), SysLfnnVersion::getChipType, lfnnVersion.getChipType());
        queryWrapper.orderByDesc(SysLfnnVersion::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public boolean saveCustom(SysLfnnVersion lfnnVersion) {
        return super.save(lfnnVersion);
    }

    @Override
    public boolean removeByIdsCustom(List<Long> list) {
        // 需要将对应的上传任务也删除
        for (Long versionId : list) {
            SysLfnnVersion lfnnVersion = getById(versionId);
            if (lfnnVersion != null && lfnnVersion.getLfnnIdentifier() != null) {
                SysUploadTask uploadTask = uploadTaskService.getByIdentifier(lfnnVersion.getLfnnIdentifier());
                if (uploadTask != null) {
                    uploadTaskService.deleteTask(uploadTask.getId());
                }
            }
        }
        return super.removeByIds(list);
    }

    @Override
    public boolean updateByIdCustom(SysLfnnVersion lfnnVersion) {
        // 检查LFNN标识符是否有变更，如果有变更则需要更新对应的上传任务标识
        SysLfnnVersion sysLfnnVersion = getById(lfnnVersion.getId());
        if (sysLfnnVersion != null && sysLfnnVersion.getLfnnIdentifier() != null
                && !lfnnVersion.getLfnnIdentifier().equals(sysLfnnVersion.getLfnnIdentifier())) {
            // 删除旧的上传任务
            uploadTaskService.deleteTask(uploadTaskService.getByIdentifier(sysLfnnVersion.getLfnnIdentifier()).getId());
        }
        return updateById(lfnnVersion);
    }

    @Override
    public String getDownloadUrl(String identifier) {
        String url = uploadTaskService.genPreSignDownloadUrl(identifier, 60000L, true, null);
        //更新下载次数
        LambdaQueryWrapper<SysLfnnVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysLfnnVersion::getLfnnIdentifier, identifier);
        SysLfnnVersion sysLfnnVersion = getOne(queryWrapper);
        sysLfnnVersion.setDownloadCount(sysLfnnVersion.getDownloadCount() + 1);
        updateById(sysLfnnVersion);
        return url;
    }
}
