package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.model.entity.SysAiAccessRecord;
import com.ruoyi.bussiness.service.SysAiAccessRecordService;
import com.ruoyi.bussiness.mapper.SysAiAccessRecordMapper;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【sys_ai_access_record】的数据库操作Service实现
* @createDate 2025-06-26 09:56:53
*/
@Service
public class SysAiAccessRecordServiceImpl extends ServiceImpl<SysAiAccessRecordMapper, SysAiAccessRecord>
    implements SysAiAccessRecordService{

    @Override
    public int saveOrUpdateCustom(SysAiAccessRecord sysAiAccessRecord) {
        // 保存或者更新如果存在 accessType类型的数据则更新accessCount + 1；如果不存在则新增一条数据并且设置accessCount为1
        SysAiAccessRecord existRecord = lambdaQuery()
                .eq(SysAiAccessRecord::getAccessType, sysAiAccessRecord.getAccessType())
                .one();
        String username = SecurityUtils.getUsername();

        if (existRecord != null) {
            // 存在记录，更新访问次数 +1
            existRecord.setAccessCount(existRecord.getAccessCount() + 1);
            // 更新最后访问时间
            existRecord.setUpdateTime(LocalDateTime.now());
            existRecord.setUpdateBy(username);
            return baseMapper.updateById(existRecord);
        } else {
            // 不存在记录，新增一条并设置访问次数为1
            sysAiAccessRecord.setAccessCount(1);
            sysAiAccessRecord.setCreateTime(LocalDateTime.now());
            sysAiAccessRecord.setUpdateBy(username);
            sysAiAccessRecord.setCreateBy(username);
            sysAiAccessRecord.setUpdateTime(LocalDateTime.now());
            return baseMapper.insert(sysAiAccessRecord);
        }
    }
}




