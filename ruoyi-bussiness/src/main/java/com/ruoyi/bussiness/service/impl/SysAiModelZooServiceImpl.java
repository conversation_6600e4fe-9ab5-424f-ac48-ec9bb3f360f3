package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.mapper.SysAiModelZooMapper;
import com.ruoyi.bussiness.model.entity.SysAiModelZoo;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.service.SysAiModelZooService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * AI模型服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysAiModelZooServiceImpl extends ServiceImpl<SysAiModelZooMapper, SysAiModelZoo> implements SysAiModelZooService {

    @Autowired
    private SysUploadTaskService uploadTaskService;

    @Override
    public List<SysAiModelZoo> selectModelList(SysAiModelZoo model) {
        LambdaQueryWrapper<SysAiModelZoo> wrapper = new LambdaQueryWrapper<>();
        // 根据模型名称模糊查询
        wrapper.like(StringUtils.hasText(model.getModelName()), SysAiModelZoo::getModelName, model.getModelName());
        // 根据模型类型精确匹配
        wrapper.eq(StringUtils.hasText(model.getModelType()), SysAiModelZoo::getModelType, model.getModelType());
        wrapper.eq(StringUtils.hasText(model.getChipType()), SysAiModelZoo::getChipType, model.getChipType());
        // 默认按创建时间降序排序
        wrapper.orderByAsc(SysAiModelZoo::getCreateTime);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIdsCustom(List<Long> list) {
        //需要将对应的示例文件也删除
        for (Long modelZooId : list) {
            SysAiModelZoo model = getById(modelZooId);
            if (Objects.nonNull(model.getCodeIdentififer()) && !model.getCodeIdentififer().isEmpty()) {
                SysUploadTask sysUploadTask = uploadTaskService.getByIdentifier(model.getCodeIdentififer());
                if (Objects.nonNull(sysUploadTask) && Objects.nonNull(sysUploadTask.getId())) {
                    uploadTaskService.deleteTask(sysUploadTask.getId());
                }
            }
        }
        return this.removeBatchByIds(list);
    }

    @Override
    public boolean updateByIdCustom(SysAiModelZoo model) {
        //检查codeIdentififer是否为空，如果不为空则需要更新对应的上传任务标识，删除旧的上传任务
        SysAiModelZoo sysAiModelZoo = this.getById(model.getId());
        if (StringUtils.hasText(model.getCodeIdentififer()) && !model.getCodeIdentififer().equals(sysAiModelZoo.getCodeIdentififer())) {
            // 删除旧的上传任务
            uploadTaskService.deleteTask(uploadTaskService.getByIdentifier(sysAiModelZoo.getCodeIdentififer()).getId());
        }
        return this.updateById(model);
    }
}
