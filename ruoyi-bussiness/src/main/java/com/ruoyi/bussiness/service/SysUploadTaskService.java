package com.ruoyi.bussiness.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.bussiness.model.dto.TaskInfoDTO;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.param.InitTaskParam;
import com.ruoyi.bussiness.model.param.BatchInitTaskParam;
import com.ruoyi.common.core.page.TableDataInfo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 分片上传-分片任务记录(SysUploadTask)表服务接口
 *
 * @since 2025-05-09 17:47:30
 */
public interface SysUploadTaskService extends IService<SysUploadTask> {

    /**
     * 根据md5标识获取分片上传任务
      * @param identifier
     * @return
     */
    SysUploadTask getByIdentifier (String identifier);

    /**
     * 根据md5标识、用户和文件类型获取分片上传任务
     * @param identifier 文件标识符
     * @param createBy 创建者
     * @param fileType 文件类型
     * @return
     */
    SysUploadTask getByIdentifierAndUserAndType(String identifier, String createBy, String fileType);

    /**
     * 初始化一个任务
     */
    TaskInfoDTO initTask (InitTaskParam param);

    /**
     * 获取文件地址
     * @param bucket
     * @param objectKey
     * @return
     */
    String getPath (String bucket, String objectKey);

    /**
     * 获取上传进度
     * @param identifier 文件标识符
     * @param fileType 文件类型(可选)
     * @return
     */
    TaskInfoDTO getTaskInfo (String identifier, String fileType);

    /**
     * 生成预签名上传url
     * @param bucket 桶名
     * @param objectKey 对象的key
     * @param params 额外的参数
     * @return
     */
    String genPreSignUploadUrl (String bucket, String objectKey, Map<String, String> params);

    /**
     * 合并分片
     * @param identifier
     */
    void merge (String identifier, String fileType);

    /**
     * 生成预签名下载url
     * @param identifier 文件标识
     * @param expires 过期时间（秒）
     * @param isGenerateRecord 是否生成下载记录
     * @param fileType 文件类型
     * @return 预签名的下载URL
     */
    String genPreSignDownloadUrl(String identifier, Long expires, boolean isGenerateRecord, String fileType);

    /**
     * 批量初始化上传任务
     * @param param 批量初始化参数
     * @return 批量任务信息
     */
    List<TaskInfoDTO> batchInitTask(BatchInitTaskParam param);
    
    /**
     * 分页查询上传任务列表
     * 
     * @param task 查询参数
     * @return 分页数据
     */
    List<SysUploadTask> queryPage(SysUploadTask task);
    
    /**
     * 获取上传任务详情
     * 
     * @param id 任务ID
     * @return 上传任务详情
     */
    SysUploadTask getTaskDetail(Long id);
    
    /**
     * 更新上传任务
     * 
     * @param task 上传任务信息
     * @return 是否成功
     */
    boolean updateTask(SysUploadTask task);
    
    /**
     * 删除上传任务
     * 
     * @param id 任务ID
     * @return 是否成功
     */
    boolean deleteTask(Long id);
    
    /**
     * 批量删除上传任务
     * 
     * @param ids 任务ID数组
     * @return 是否成功
     */
    boolean deleteBatch(Long[] ids);

    /**
     * 根据文件类型获取最新的文件
     *
     * @param fileType 文件类型
     * @return 最新的文件
     */
    SysUploadTask getByFileType(String fileType);

    /**
     * 批量下载文件并打包成zip
     *
     * @param identifiers 文件标识符列表
     * @param response HTTP响应
     * @throws Exception 下载过程中的异常
     */
    void batchDownloadFiles(List<String> identifiers, HttpServletResponse response) throws Exception;

    /**
     * 清理超时的上传任务
     */
    void cleanupTimeoutUploadTasks();
}
