package com.ruoyi.bussiness.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.bussiness.model.entity.SysAiModelZoo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_ai_model_zoo】的数据库操作Service
* @createDate 2025-05-28 15:27:34
*/
public interface SysAiModelZooService extends IService<SysAiModelZoo> {

    /**
     * 查询模型列表
     * @param model 模型查询条件
     * @return 模型列表
     */
    List<SysAiModelZoo> selectModelList(SysAiModelZoo model);

    /**
     * 批量删除模型
     * @param list 模型ID列表
     * @return 删除结果
     */
    boolean removeBatchByIdsCustom(List<Long> list);

    /**
     * 更新模型信息 同步更新模型示例文件，如果存在则会将原有删除
     * @param model 模型实体
     * @return 更新结果
     */
    boolean updateByIdCustom(SysAiModelZoo model);
}
