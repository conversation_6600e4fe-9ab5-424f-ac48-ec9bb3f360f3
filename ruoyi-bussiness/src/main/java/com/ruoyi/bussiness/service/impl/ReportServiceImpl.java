package com.ruoyi.bussiness.service.impl;

import com.ruoyi.bussiness.mapper.ReportMapper;
import com.ruoyi.bussiness.model.vo.*;
import com.ruoyi.bussiness.service.IReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ReportServiceImpl implements IReportService {

    @Autowired
    private ReportMapper reportMapper;

    @Override
    public ReportRegionDataVO getRegionDistribution(String dataType, String startTime, String endTime) {
        List<Map<String, Object>> regionData;

        // 根据数据类型选择对应的查询方法
        if ("conversion".equals(dataType)) {
            regionData = reportMapper.selectConversionRegionDistribution(startTime, endTime);
        } else if ("download".equals(dataType)) {
            regionData = reportMapper.selectSdkDownloadRegionDistribution(startTime, endTime);
        } else {
            throw new IllegalArgumentException("不支持的数据类型: " + dataType + "，仅支持 'conversion' 或 'download'");
        }

        // 转换Map数据为VO对象
        List<ReportRegionDataVO.RegionItem> regions = regionData.stream()
                .map(map -> {
                    ReportRegionDataVO.RegionItem item = new ReportRegionDataVO.RegionItem();
                    item.setName(String.valueOf(map.get("name")));
                    item.setValue(((Number) map.get("value")).intValue());
                    return item;
                })
                .collect(Collectors.toList());

        ReportRegionDataVO result = new ReportRegionDataVO();
        result.setDataType(dataType);
        result.setRegions(regions);

        // 计算最小值和最大值
        if (!regions.isEmpty()) {
            OptionalInt min = regions.stream().mapToInt(ReportRegionDataVO.RegionItem::getValue).min();
            OptionalInt max = regions.stream().mapToInt(ReportRegionDataVO.RegionItem::getValue).max();

            ReportRegionDataVO.VisualMap visualMap = new ReportRegionDataVO.VisualMap();
            visualMap.setMin(min.orElse(0));
            visualMap.setMax(max.orElse(1000));
            result.setVisualMap(visualMap);
        } else {
            ReportRegionDataVO.VisualMap visualMap = new ReportRegionDataVO.VisualMap();
            visualMap.setMin(0);
            visualMap.setMax(1000);
            result.setVisualMap(visualMap);
        }

        return result;
    }

    @Override
    public ReportTrendDataVO getTrendData(String timeUnit, String startTime, String endTime) {
        List<Map<String, Object>> accessTrend = reportMapper.selectAccessTrend(timeUnit, startTime, endTime);
        List<Map<String, Object>> conversionTrend = reportMapper.selectConversionTrend(timeUnit, startTime, endTime);
        
        ReportTrendDataVO result = new ReportTrendDataVO();
        result.setTimeUnit(timeUnit);
        
        // 生成时间标签
        List<String> timeLabels = generateTimeLabels(timeUnit, startTime, endTime);
        result.setTimeLabels(timeLabels);
        
        // 构建系列数据
        List<ReportTrendDataVO.SeriesItem> series = new ArrayList<>();
        
        // 访问次数系列
        ReportTrendDataVO.SeriesItem accessSeries = new ReportTrendDataVO.SeriesItem();
        accessSeries.setName("访问次数");
        accessSeries.setData(buildSeriesData(accessTrend, timeLabels, timeUnit));
        series.add(accessSeries);
        
        // 模型转换次数系列
        ReportTrendDataVO.SeriesItem conversionSeries = new ReportTrendDataVO.SeriesItem();
        conversionSeries.setName("模型转换次数");
        conversionSeries.setData(buildSeriesData(conversionTrend, timeLabels, timeUnit));
        series.add(conversionSeries);
        
        result.setSeries(series);
        return result;
    }

    @Override
    public ReportModuleDataVO getModuleDistribution(String startTime, String endTime) {
        List<Map<String, Object>> moduleData = reportMapper.selectModuleDistribution(startTime, endTime);

        // 转换Map数据为VO对象
        List<ReportModuleDataVO.ModuleItem> modules = moduleData.stream()
                .map(map -> {
                    ReportModuleDataVO.ModuleItem item = new ReportModuleDataVO.ModuleItem();
                    item.setName(String.valueOf(map.get("name")));
                    item.setValue(((Number) map.get("value")).intValue());
                    return item;
                })
                .collect(Collectors.toList());

        ReportModuleDataVO result = new ReportModuleDataVO();
        result.setName("模块访问");
        result.setData(modules);

        return result;
    }

    @Override
    public ReportUserTypeDataVO getUserTypeDistribution(String startTime, String endTime) {
        List<Map<String, Object>> userTypeData = reportMapper.selectUserTypeDistribution(startTime, endTime);

        // 转换Map数据为VO对象
        List<ReportUserTypeDataVO.UserTypeItem> userTypes = userTypeData.stream()
                .map(map -> {
                    ReportUserTypeDataVO.UserTypeItem item = new ReportUserTypeDataVO.UserTypeItem();
                    item.setName(String.valueOf(map.get("name")));
                    item.setValue(((Number) map.get("value")).intValue());
                    return item;
                })
                .collect(Collectors.toList());

        ReportUserTypeDataVO result = new ReportUserTypeDataVO();
        result.setName("用户类型");
        result.setData(userTypes);

        return result;
    }

    @Override
    public ReportConversionRateVO getConversionRate(String startTime, String endTime) {
        List<Map<String, Object>> conversionData = reportMapper.selectConversionRate(startTime, endTime);

        // 转换Map数据为VO对象
        List<ReportConversionRateVO.ConversionItem> conversions = conversionData.stream()
                .map(map -> {
                    ReportConversionRateVO.ConversionItem item = new ReportConversionRateVO.ConversionItem();
                    item.setName(String.valueOf(map.get("name")));
                    item.setValue(((Number) map.get("value")).intValue());
                    return item;
                })
                .collect(Collectors.toList());

        // 计算成功和失败的总和
        int total = conversions.stream()
                .mapToInt(ReportConversionRateVO.ConversionItem::getValue)
                .sum();

        ReportConversionRateVO result = new ReportConversionRateVO();
        result.setName("转换结果");
        result.setData(conversions);
        // 设置总数
        result.setTotal(total);

        return result;
    }

    @Override
    public ReportBasicStatsVO getBasicStats(String startTime, String endTime) {
        // 查询注册用户总数
        Integer totalUsers = reportMapper.selectTotalUsers();

        // 查询用户操作总次数
        Integer totalVisits = reportMapper.selectTotalLogins(startTime, endTime);

        // 查询SDK下载次数
        Integer sdkDownloadCount = reportMapper.selectSdkDownloadCount();

        // 查询示例训练工程下载次数
        Integer trainEngineerDownloadCount = reportMapper.selectTrainEngineerDownloadCount();

        // 查询原始模型数
        Integer originalModelCount = reportMapper.selectOriginalModelCount();

        ReportBasicStatsVO result = new ReportBasicStatsVO();
        result.setTotalUsers(totalUsers != null ? totalUsers : 0);
        result.setTotalVisits(totalVisits != null ? totalVisits : 0);
        result.setSdkDownloadCount(sdkDownloadCount != null ? sdkDownloadCount : 0);
        result.setTrainEngineerDownloadCount(trainEngineerDownloadCount != null ? trainEngineerDownloadCount : 0);
        result.setOriginalModelCount(originalModelCount != null ? originalModelCount : 0);

        return result;
    }

    /**
     * 生成时间标签
     */
    private List<String> generateTimeLabels(String timeUnit, String startTime, String endTime) {
        List<String> labels = new ArrayList<>();

        switch (timeUnit) {
            case "day":
                // 生成日期标签，格式为 yyyy-MM-dd
                if (startTime != null && endTime != null) {
                    LocalDate start = LocalDate.parse(startTime);
                    LocalDate end = LocalDate.parse(endTime);
                    LocalDate current = start;
                    while (!current.isAfter(end)) {
                        labels.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        current = current.plusDays(1);
                    }
                } else {
                    // 默认生成最近30天
                    LocalDate end = LocalDate.now();
                    LocalDate start = end.minusDays(29);
                    LocalDate current = start;
                    while (!current.isAfter(end)) {
                        labels.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        current = current.plusDays(1);
                    }
                }
                break;
            case "month":
                // 生成月标签，格式为 yyyy-MM
                if (startTime != null && endTime != null) {
                    LocalDate start = LocalDate.parse(startTime);
                    LocalDate end = LocalDate.parse(endTime);
                    LocalDate current = start;
                    while (!current.isAfter(end)) {
                        labels.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                        current = current.plusMonths(1);
                    }
                } else {
                    // 默认生成最近12个月
                    LocalDate current = LocalDate.now().withDayOfMonth(1).minusMonths(11);
                    LocalDate end = LocalDate.now().withDayOfMonth(1);
                    while (!current.isAfter(end)) {
                        labels.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                        current = current.plusMonths(1);
                    }
                }
                break;
            case "year":
                // 生成年标签，格式为 yyyy
                if (startTime != null && endTime != null) {
                    LocalDate start = LocalDate.parse(startTime);
                    LocalDate end = LocalDate.parse(endTime);
                    LocalDate current = start;
                    while (!current.isAfter(end)) {
                        labels.add(current.format(DateTimeFormatter.ofPattern("yyyy")));
                        current = current.plusYears(1);
                    }
                } else {
                    // 默认生成当前年往前6年的数据（共6年）
                    LocalDate current = LocalDate.now().minusYears(5); // 往前5年，加上当前年共6年
                    LocalDate end = LocalDate.now();
                    while (!current.isAfter(end)) {
                        labels.add(current.format(DateTimeFormatter.ofPattern("yyyy")));
                        current = current.plusYears(1);
                    }
                }
                break;
        }

        return labels;
    }

    /**
     * 构建系列数据
     */
    private List<Integer> buildSeriesData(List<Map<String, Object>> trendData, List<String> timeLabels, String timeUnit) {
        Map<String, Integer> dataMap = trendData.stream()
                .collect(Collectors.toMap(
                        item -> String.valueOf(item.get("time_label")),
                        item -> ((Number) item.get("count")).intValue(),
                        (existing, replacement) -> existing
                ));
        
        return timeLabels.stream()
                .map(label -> dataMap.getOrDefault(label, 0))
                .collect(Collectors.toList());
    }
}
