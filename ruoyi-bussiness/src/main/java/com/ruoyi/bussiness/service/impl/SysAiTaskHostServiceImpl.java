package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.event.HostAddedEvent;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import com.ruoyi.bussiness.enums.HostStatusEnum;
import com.ruoyi.bussiness.mapper.SysAiTaskHostMapper;
import com.ruoyi.bussiness.service.ISysAiTaskHostService;
import com.ruoyi.bussiness.utils.SshUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;

/**
 * AI任务主机Service实现类
 *
 * <AUTHOR>
 */
@Service
public class SysAiTaskHostServiceImpl extends ServiceImpl<SysAiTaskHostMapper, SysAiTaskHost> implements ISysAiTaskHostService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Override
    public SysAiTaskHost getAvailableHost() {
        // 查询所有状态为IDLE的主机
        LambdaQueryWrapper<SysAiTaskHost> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysAiTaskHost::getStatus, HostStatusEnum.IDLE.getCode());
        List<SysAiTaskHost> idleHosts = this.list(wrapper);
        if (idleHosts == null || idleHosts.isEmpty()) {
            return null;
        }
        // 按userList数量升序排序，优先选择userList最少的主机
        idleHosts.sort((h1, h2) -> {
            int size1 = (h1.getUserList() == null) ? 0 : h1.getUserList().size();
            int size2 = (h2.getUserList() == null) ? 0 : h2.getUserList().size();
            return Integer.compare(size1, size2);
        });
        // 返回userList最少的主机
        return idleHosts.get(0);
    }

    @Override
    public boolean updateHostStatus(Long hostId, String status) {
        return baseMapper.updateHostStatus(hostId, status) > 0;
    }

    @Override
    public boolean updateHostResource(Long hostId, Double cpuUsage, Double memoryUsage, Double diskUsage) {
        return baseMapper.updateHostResource(hostId, cpuUsage, memoryUsage, diskUsage) > 0;
    }

    @Override
    public List<SysAiTaskHost> listQuery(SysAiTaskHost host) {
        LambdaQueryWrapper<SysAiTaskHost> wrapper = new LambdaQueryWrapper<>();
        if (host != null) {
            if (host.getHostId() != null) {
                wrapper.eq(SysAiTaskHost::getHostId, host.getHostId());
            }
            if (StringUtils.isNotBlank(host.getHostName())) {
                wrapper.like(SysAiTaskHost::getHostName, host.getHostName());
            }
            if (StringUtils.isNotBlank(host.getHostIp())) {
                wrapper.eq(SysAiTaskHost::getHostIp, host.getHostIp());
            }
            if (StringUtils.isNotBlank(host.getStatus())) {
                wrapper.eq(SysAiTaskHost::getStatus, host.getStatus());
            }
            if (host.getCpuUsage() != null) {
                wrapper.le(SysAiTaskHost::getCpuUsage, host.getCpuUsage());
            }
            if (host.getMemoryUsage() != null) {
                wrapper.le(SysAiTaskHost::getMemoryUsage, host.getMemoryUsage());
            }
        }
        
        // 获取主机列表
        List<SysAiTaskHost> hostList = this.list(wrapper);
        
//        // 更新每个主机的实时资源使用数据
//        for (SysAiTaskHost sysAiTaskHost : hostList) {
//            try {
//                // 测试SSH连接
//                boolean isConnected = SshUtil.testConnection(sysAiTaskHost);
//                if (!isConnected) {
//                    // 连接失败，将主机状态设置为离线
//                    updateHostStatus(sysAiTaskHost.getHostId(), HostStatusEnum.OFFLINE.getCode());
//                    sysAiTaskHost.setStatus(HostStatusEnum.OFFLINE.getCode());
//                    continue;
//                }
//
//                // 获取实时资源使用数据
//                double[] resources = SshUtil.getHostResource(sysAiTaskHost);
//
//                // 更新主机资源使用情况
//                updateHostResource(sysAiTaskHost.getHostId(), resources[0], resources[1], resources[2]);
//
//                // 更新当前对象的资源使用数据
//                sysAiTaskHost.setCpuUsage(resources[0]);
//                sysAiTaskHost.setMemoryUsage(resources[1]);
//                sysAiTaskHost.setDiskUsage(resources[2]);
//            } catch (Exception e) {
//                // 如果获取资源数据失败，将主机状态设置为离线
//                updateHostStatus(sysAiTaskHost.getHostId(), HostStatusEnum.OFFLINE.getCode());
//                sysAiTaskHost.setStatus(HostStatusEnum.OFFLINE.getCode());
//            }
//        }
        
        return hostList;
    }

    @Override
    public int saveLogic(SysAiTaskHost sysAiTaskHost) {
        boolean save = save(sysAiTaskHost);
        if (save) {
            // 发布主机新增事件
            eventPublisher.publishEvent(new HostAddedEvent(this, sysAiTaskHost));
            //将状态更新为空闲
            updateHostStatus(sysAiTaskHost.getHostId(), HostStatusEnum.IDLE.getCode());
            return 1;
        }
        return 0;
    }

    @Override
    public int uploadExecutable(SysAiTaskHost sysAiTaskHost) {
        eventPublisher.publishEvent(new HostAddedEvent(this, sysAiTaskHost));
        return 1;
    }

    @Override
    public List<SysAiTaskHost> listHostIdle() {
        LambdaQueryWrapper<SysAiTaskHost> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysAiTaskHost::getStatus, HostStatusEnum.IDLE.getCode());
        return list(wrapper);
    }

    @Override
    public SysAiTaskHost addUserToHost(SysAiTaskHost host, String currentUser) {
        
        // 获取并初始化userList
        List<String> userList = host.getUserList();
        if (userList == null) {
            userList = new ArrayList<>();
        }
        
        // 如果用户不在列表中，添加用户并更新
        if (!userList.contains(currentUser)) {
            userList.add(currentUser);
            host.setUserList(userList);
            this.updateById(host);
        }
        
        return host;
    }

    @Override
    public SysAiTaskHost removeUserFromHost(SysAiTaskHost host, String currentUser) {
        // 获取userList
        List<String> userList = host.getUserList();
        if (userList != null) {
            // 从列表中移除当前用户
            userList.remove(currentUser);
            host.setUserList(userList);
            this.updateById(host);
        }
        return host;
    }
} 