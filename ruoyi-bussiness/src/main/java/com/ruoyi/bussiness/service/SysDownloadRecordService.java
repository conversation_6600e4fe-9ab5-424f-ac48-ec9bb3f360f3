package com.ruoyi.bussiness.service;

import com.ruoyi.bussiness.model.entity.SysDownloadRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_download_record】的数据库操作Service
* @createDate 2025-06-04 14:42:05
*/
public interface SysDownloadRecordService extends IService<SysDownloadRecord> {

    /**
     * 分页查询下载记录
     *
     * @param downloadRecord 查询条件
     * @return 下载记录列表
     */
    List<SysDownloadRecord> listPage(SysDownloadRecord downloadRecord);
}
