package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.model.entity.SysAiModelZoo;
import com.ruoyi.bussiness.model.entity.SysAiTrainEngineer;
import com.ruoyi.bussiness.model.entity.SysLfnnVersion;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.enums.FileTypeEnum;
import com.ruoyi.bussiness.service.SysAiTrainEngineerService;
import com.ruoyi.bussiness.mapper.SysAiTrainEngineerMapper;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【sys_ai_train_engineer】的数据库操作Service实现
* @createDate 2025-06-11 10:24:48
*/
@Service
public class SysAiTrainEngineerServiceImpl extends ServiceImpl<SysAiTrainEngineerMapper, SysAiTrainEngineer>
    implements SysAiTrainEngineerService{

    @Autowired
    private SysUploadTaskService uploadTaskService;


    @Override
    public List<SysAiTrainEngineer> listPage(SysAiTrainEngineer sysAiTrainEngineer) {
        LambdaQueryWrapper<SysAiTrainEngineer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(sysAiTrainEngineer.getCreateBy()), SysAiTrainEngineer::getCreateBy, sysAiTrainEngineer.getCreateBy());
        queryWrapper.like(Objects.nonNull(sysAiTrainEngineer.getModelName()), SysAiTrainEngineer::getModelName, sysAiTrainEngineer.getModelName());
        queryWrapper.eq(Objects.nonNull(sysAiTrainEngineer.getModelType()), SysAiTrainEngineer::getModelType, sysAiTrainEngineer.getModelType());
        queryWrapper.eq(Objects.nonNull(sysAiTrainEngineer.getChipType()), SysAiTrainEngineer::getChipType, sysAiTrainEngineer.getChipType());
        queryWrapper.orderByAsc(SysAiTrainEngineer::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public boolean saveCustom(SysAiTrainEngineer sysAiTrainEngineer) {
        return super.save(sysAiTrainEngineer);
    }

    @Override
    public boolean updateByIdCustom(SysAiTrainEngineer sysAiTrainEngineer) {
        // 检查LFNN标识符是否有变更，如果有变更则需要更新对应的上传任务标识
        SysAiTrainEngineer aiTrainEngineer = getById(sysAiTrainEngineer.getId());
        if (aiTrainEngineer != null && aiTrainEngineer.getFileIdentifier() != null
                && !sysAiTrainEngineer.getFileIdentifier().equals(aiTrainEngineer.getFileIdentifier())) {
            // 删除旧的上传任务
            uploadTaskService.deleteTask(uploadTaskService.getByIdentifier(aiTrainEngineer.getFileIdentifier()).getId());
        }
        return updateById(sysAiTrainEngineer);
    }

    @Override
    public boolean removeByIdsCustom(List<Long> list) {
        // 需要将对应的上传任务也删除
        for (Long versionId : list) {
            SysAiTrainEngineer sysAiTrainEngineer = getById(versionId);
            if (sysAiTrainEngineer != null && sysAiTrainEngineer.getFileIdentifier() != null) {
                SysUploadTask uploadTask = uploadTaskService.getByIdentifier(sysAiTrainEngineer.getFileIdentifier());
                if (uploadTask != null) {
                    uploadTaskService.deleteTask(uploadTask.getId());
                }
            }
        }
        return super.removeByIds(list);
    }

    @Override
    public String getDownloadUrl(String identifier) {
        String url = uploadTaskService.genPreSignDownloadUrl(identifier, 60000L, true, FileTypeEnum.TRAIN_ENGINEER.getCode());
        //更新下载次数
        LambdaQueryWrapper<SysAiTrainEngineer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysAiTrainEngineer::getFileIdentifier, identifier);
        SysAiTrainEngineer sysAiTrainEngineer = getOne(queryWrapper);
        sysAiTrainEngineer.setDownloadCount(sysAiTrainEngineer.getDownloadCount() + 1);
        updateById(sysAiTrainEngineer);
        return url;
    }
}




