package com.ruoyi.bussiness.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ruoyi.bussiness.mapper.SysAiTransferRecordMapper;
import com.ruoyi.bussiness.model.entity.SysAiTransferRecord;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.vo.SysAiTransferRecordListVo;
import com.ruoyi.bussiness.service.ISysAiTransferRecordService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * AI模型转换日志Service实现
 *
 * <AUTHOR>
 */
@Service
public class SysAiTransferRecordServiceImpl extends ServiceImpl<SysAiTransferRecordMapper, SysAiTransferRecord> implements ISysAiTransferRecordService {

    @Autowired
    private SysUploadTaskService sysUploadTaskService;


    @Override
    public List<SysAiTransferRecordListVo> selectLogList(SysAiTransferRecord log) {
        LambdaQueryWrapper<SysAiTransferRecord> wrapper = new LambdaQueryWrapper<>();
        // 根据用户名查询
        wrapper.eq(Objects.nonNull(log.getUsername()) , SysAiTransferRecord::getUsername, log.getUsername());
        // 根据状态查询
        wrapper.eq(Objects.nonNull(log.getStatus()), SysAiTransferRecord::getStatus, log.getStatus());
        // 根据创建时间查询 在startTime和endTime之间
        wrapper.ge(Objects.nonNull(log.getStartTime()), SysAiTransferRecord::getCreateTime, log.getStartTime());
        wrapper.le(Objects.nonNull(log.getEndTime()), SysAiTransferRecord::getCreateTime, log.getEndTime());
        // 按创建时间降序排序
        wrapper.orderByAsc(SysAiTransferRecord::getCreateTime);
        //处理若依中分页内容在使用autoresultmap后分页失效问题  此处没有开启分页可以去除
        PageHelper.clearPage();
        List<SysAiTransferRecord> list = this.list(wrapper);
        List<SysAiTransferRecordListVo> sysAiTransferRecordListVos = BeanUtil.copyToList(list, SysAiTransferRecordListVo.class);
        for (SysAiTransferRecordListVo sysAiTransferRecordListVo : sysAiTransferRecordListVos) {
            sysAiTransferRecordListVo.setConfigIdentifierTask(sysUploadTaskService.getByIdentifier(sysAiTransferRecordListVo.getConfigIdentifier()));
            sysAiTransferRecordListVo.setLogIdentifierTask(sysUploadTaskService.getByIdentifier(sysAiTransferRecordListVo.getLogIdentifier()));
            sysAiTransferRecordListVo.setModelIdentifierTask(sysUploadTaskService.getByIdentifier(sysAiTransferRecordListVo.getModelIdentifier()));
            sysAiTransferRecordListVo.setTransferModelIdentifierTask(sysUploadTaskService.getByIdentifier(sysAiTransferRecordListVo.getTransferModelIdentifier()));
            List<SysUploadTask> imagesTasks = new ArrayList<>();
            for (String imageIdentifier : sysAiTransferRecordListVo.getImageIdentifiers()) {
                SysUploadTask sysUploadTask = sysUploadTaskService.getByIdentifier(imageIdentifier);
                if (sysUploadTask != null) {
                    imagesTasks.add(sysUploadTask);
                }
            }
            sysAiTransferRecordListVo.setImageIdentifiersTasks(imagesTasks);
        }
        return sysAiTransferRecordListVos;
    }

    @Override
    public int insertLog(SysAiTransferRecord log) {
        // 设置创建者
        log.setCreateBy(SecurityUtils.getUsername());
        return this.save(log) ? 1 : 0;
    }

    @Override
    public int updateLog(SysAiTransferRecord log) {
        // 设置更新者
        return this.updateById(log) ? 1 : 0;
    }

    @Override
    public int deleteLogByIds(Long[] logIds) {
        return this.removeBatchByIds(Arrays.asList(logIds)) ? logIds.length : 0;
    }

    @Override
    public int deleteLogById(Long logId) {
        return this.removeById(logId) ? 1 : 0;
    }

    @Override
    public SysAiTransferRecord transferStatus(String taskId) {
        LambdaQueryWrapper<SysAiTransferRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysAiTransferRecord::getTaskId, taskId);
        return this.getOne(wrapper);
    }
}

