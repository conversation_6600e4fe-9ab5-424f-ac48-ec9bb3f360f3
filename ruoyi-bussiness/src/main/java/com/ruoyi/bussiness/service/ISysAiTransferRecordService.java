package com.ruoyi.bussiness.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.bussiness.model.entity.SysAiTransferRecord;
import com.ruoyi.bussiness.model.vo.SysAiTransferRecordListVo;

import java.util.List;

/**
 * AI模型转换日志Service接口
 *
 * <AUTHOR>
 */
public interface ISysAiTransferRecordService extends IService<SysAiTransferRecord> {

    /**
     * 查询转换日志列表
     *
     * @param log 转换日志
     * @return 转换日志集合
     */
    List<SysAiTransferRecordListVo> selectLogList(SysAiTransferRecord log);

    /**
     * 新增转换日志
     *
     * @param log 转换日志
     * @return 结果
     */
    int insertLog(SysAiTransferRecord log);

    /**
     * 修改转换日志
     *
     * @param log 转换日志
     * @return 结果
     */
    int updateLog(SysAiTransferRecord log);

    /**
     * 批量删除转换日志
     *
     * @param logIds 需要删除的转换日志ID数组
     * @return 结果
     */
    int deleteLogByIds(Long[] logIds);

    /**
     * 删除转换日志信息
     *
     * @param logId 转换日志ID
     * @return 结果
     */
    int deleteLogById(Long logId);

    /**
     * 根据任务ID查询转换状态
     *
     * @param taskId 任务ID
     * @return 转换状态
     */
    SysAiTransferRecord transferStatus(String taskId);
}