package com.ruoyi.bussiness.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.mapper.SysUploadTaskTypeMapper;
import com.ruoyi.bussiness.model.entity.SysUploadTaskType;
import com.ruoyi.bussiness.service.SysUploadTaskTypeService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【sys_upload_task_type】的数据库操作Service实现
 * @createDate 2025-05-15 11:07:32
 */
@Service
public class SysUploadTaskTypeServiceImpl extends ServiceImpl<SysUploadTaskTypeMapper, SysUploadTaskType>
        implements SysUploadTaskTypeService {

    @Override
    public List<SysUploadTaskType> listQuery(SysUploadTaskType uploadTaskType) {
        LambdaQueryWrapper<SysUploadTaskType> queryWrapper = new LambdaQueryWrapper<>();
        if (uploadTaskType != null) {
            if (uploadTaskType.getId() != null) {
                queryWrapper.eq(SysUploadTaskType::getId, uploadTaskType.getId());
            }
            if (StringUtils.isNotBlank(uploadTaskType.getFileTypeName())) {
                queryWrapper.like(SysUploadTaskType::getFileTypeName, uploadTaskType.getFileTypeName());
            }
            if (StringUtils.isNotBlank(uploadTaskType.getFileTypeIdentifier())) {
                queryWrapper.eq(SysUploadTaskType::getFileTypeIdentifier, uploadTaskType.getFileTypeIdentifier());
            }
        }
        return this.list(queryWrapper);
    }
}




