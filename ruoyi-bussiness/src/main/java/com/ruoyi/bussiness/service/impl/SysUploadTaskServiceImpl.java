package com.ruoyi.bussiness.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.bussiness.config.properties.MinioProperties;
import com.ruoyi.bussiness.event.TransferToolUpdateEvent;
import com.ruoyi.bussiness.mapper.SysUploadTaskMapper;
import com.ruoyi.bussiness.model.constants.MinioConstant;
import com.ruoyi.bussiness.model.dto.CustomPartSummary;
import com.ruoyi.bussiness.model.dto.TaskInfoDTO;
import com.ruoyi.bussiness.model.dto.TaskRecordDTO;
import com.ruoyi.bussiness.model.entity.SysDownloadRecord;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.enums.FileTypeEnum;
import com.ruoyi.bussiness.model.param.BatchInitTaskParam;
import com.ruoyi.bussiness.model.param.InitTaskParam;
import com.ruoyi.bussiness.service.SysDownloadRecordService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.URL;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分片上传-分片任务记录(SysUploadTask)表服务实现类
 *
 * @since 2025-05-09 17:47:31
 */
@Service("sysUploadTaskService")
@Slf4j
public class SysUploadTaskServiceImpl extends ServiceImpl<SysUploadTaskMapper, SysUploadTask> implements SysUploadTaskService {

    @Resource
    private S3Client amazonS3;

    @Resource
    private S3Presigner s3Presigner;

    @Resource
    private MinioProperties minioProperties;

    @Resource
    private SysUploadTaskMapper sysUploadTaskMapper;

    @Resource
    private SysDownloadRecordService sysDownloadRecordService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public SysUploadTask getByIdentifier(String identifier) {
        if (StringUtils.isEmpty(identifier)) {
            return null;
        }

        LambdaQueryWrapper<SysUploadTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUploadTask::getFileIdentifier, identifier);
        List<SysUploadTask> list = this.list(wrapper);
        return this.getOne(wrapper);
    }

    @Override
    public SysUploadTask getByIdentifierAndUserAndType(String identifier, String createBy, String fileType) {
        if (StringUtils.isEmpty(identifier)) {
            return null;
        }

        LambdaQueryWrapper<SysUploadTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUploadTask::getFileIdentifier, identifier)
               .eq(Objects.nonNull(createBy), SysUploadTask::getCreateBy, createBy)
               .eq(SysUploadTask::getFileType, fileType != null ? fileType : "other");
        return this.getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskInfoDTO initTask(InitTaskParam param) {
        String fileName = param.getFileName();
        String fileIdentifier = param.getIdentifier();
        String fileType = param.getFileType();
        String currentUser = param.getCreateBy() != null ? param.getCreateBy() : getCurrentUsername();

        // 先检查当前用户是否已上传过相同MD5和文件类型的文件
        SysUploadTask existingTask = getByIdentifierAndUserAndType(fileIdentifier, currentUser, fileType);
        if (existingTask != null) {
            log.info("用户 {} 的文件已存在，MD5: {}, 文件类型: {}, 原文件名: {}, 当前文件名: {}",
                    currentUser, fileIdentifier, fileType, existingTask.getFileName(), fileName);

            // 检查文件是否已完成上传（通过检查文件是否存在于存储中）
            boolean isFileCompleted = checkFileCompleted(existingTask);

            if (isFileCompleted) {
                // 文件已完成上传，更新batchNo和fileName，确保在文件选择器中能被识别为最新批次并显示最新文件名
                String newBatchNo = Long.toString(System.currentTimeMillis());
                String oldFileName = existingTask.getFileName(); // 保存旧文件名用于日志
                existingTask.setBatchNo(newBatchNo);
                existingTask.setFileName(fileName); // 更新为当前上传的文件名
                existingTask.setUpdateTime(LocalDateTime.now());
                existingTask.setUpdateBy(currentUser);

                // 更新数据库中的记录
                sysUploadTaskMapper.updateById(existingTask);

                log.info("文件已完成上传，已更新文件名从 {} 到 {}，batchNo为: {}",
                        oldFileName, fileName, newBatchNo);

                // 返回已完成的任务信息
                TaskInfoDTO taskInfo = new TaskInfoDTO();
                taskInfo.setFinished(true);
                taskInfo.setTaskRecord(TaskRecordDTO.convertFromEntity(existingTask));
                taskInfo.setPath(getPath(existingTask.getBucketName(), existingTask.getObjectKey()));

                return taskInfo;
            } else {
                // 文件正在上传中，检查上传任务的最后更新时间
                LocalDateTime lastUpdateTime = existingTask.getUpdateTime();
                LocalDateTime now = LocalDateTime.now();
                long minutesSinceLastUpdate = java.time.Duration.between(lastUpdateTime, now).toMinutes();

                // 如果超过30分钟没有更新，认为上传任务已失效，允许重新上传
                if (minutesSinceLastUpdate > 30) {
                    log.warn("文件上传任务超时，允许重新上传: identifier={}, 最后更新时间={}",
                            fileIdentifier, lastUpdateTime);
                    // 删除旧的上传任务记录
                    sysUploadTaskMapper.deleteById(existingTask.getId());
                } else {
                    // 文件正在上传中，返回错误信息
                    log.warn("文件正在上传中，请稍等: identifier={}, 最后更新时间={}",
                            fileIdentifier, lastUpdateTime);
                    throw new RuntimeException("文件正在上传中，请稍等！如果长时间未完成，请稍后重试。");
                }
            }
        }

        // 如果文件不存在，创建新的上传任务
        String batchNo = Long.toString(System.currentTimeMillis());
        Date currentDate = new Date();
        String bucketName = minioProperties.getBucket();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());

        // 根据文件类型构建存储路径
        String basePath = fileType != null ? fileType : "common";
        String key = StrUtil.format("{}/{}/{}.{}",
                basePath,
                DateUtil.format(currentDate, "YYYY-MM-dd"),
                IdUtil.randomUUID(),
                suffix);

        String contentType = MediaTypeFactory.getMediaType(key).orElse(MediaType.APPLICATION_OCTET_STREAM).toString();

        // 创建分片上传请求
        CreateMultipartUploadRequest createMultipartUploadRequest = CreateMultipartUploadRequest.builder()
                .bucket(bucketName)
                .key(key)
                .contentType(contentType)
                .build();

        // 初始化分片上传
        CreateMultipartUploadResponse createMultipartUploadResponse = amazonS3.createMultipartUpload(createMultipartUploadRequest);
        String uploadId = createMultipartUploadResponse.uploadId();

        SysUploadTask task = new SysUploadTask();
        int chunkNum = (int) Math.ceil(param.getTotalSize() * 1.0 / param.getChunkSize());
        task.setBucketName(minioProperties.getBucket())
                .setBatchNo(batchNo)
                .setChunkNum(chunkNum)
                .setChunkSize(param.getChunkSize())
                .setTotalSize(param.getTotalSize())
                .setFileIdentifier(fileIdentifier)
                .setFileName(fileName)
                .setObjectKey(key)
                .setFileType(fileType)
                .setUploadId(uploadId)
                .setCreateBy(param.getCreateBy() != null ? param.getCreateBy() : getCurrentUsername())
                .setUpdateBy(param.getUpdateBy() != null ? param.getUpdateBy() : getCurrentUsername())
                .setUpdateTime(LocalDateTime.now())
                .setCreateTime(LocalDateTime.now());
        sysUploadTaskMapper.insert(task);
        return new TaskInfoDTO().setFinished(false).setTaskRecord(TaskRecordDTO.convertFromEntity(task)).setPath(getPath(bucketName, key));
    }

    @Override
    public String getPath(String bucket, String objectKey) {
        return StrUtil.format("{}/{}/{}", minioProperties.getEndpoint(), bucket, objectKey);
    }

    @Override
    public TaskInfoDTO getTaskInfo(String identifier, String fileType) {
        SysUploadTask task;

        // 如果提供了文件类型，使用精确查询
        if (StringUtils.isNotEmpty(fileType)) {
            String currentUser = getCurrentUsername();
            task = getByIdentifierAndUserAndType(identifier, currentUser, fileType);
        } else {
            // 向后兼容，使用原有的查询方式
            task = getByIdentifier(identifier);
        }

        if (task == null) {
            return null;
        }

        // 使用新的检查方法来确定文件状态
        boolean isCompleted = checkFileCompleted(task);
        TaskInfoDTO result = new TaskInfoDTO()
                .setFinished(isCompleted)
                .setTaskRecord(TaskRecordDTO.convertFromEntity(task))
                .setPath(getPath(task.getBucketName(), task.getObjectKey()));

        if (!isCompleted) {
            try {
                // 文件未完成，获取已上传的分片信息
                ListPartsRequest listPartsRequest = ListPartsRequest.builder()
                        .bucket(task.getBucketName())
                        .key(task.getObjectKey())
                        .uploadId(task.getUploadId())
                        .build();
                ListPartsResponse listPartsResponse = amazonS3.listParts(listPartsRequest);

                List<CustomPartSummary> exitPartList = listPartsResponse.parts().stream()
                        .map(part -> {
                            CustomPartSummary partSummary = new CustomPartSummary();
                            partSummary.setPartNumber(part.partNumber());
                            partSummary.setETag(part.eTag());
                            partSummary.setSize(part.size());
                            return partSummary;
                        })
                        .collect(Collectors.toList());

                result.getTaskRecord().setExitPartList(exitPartList);

                // 计算上传进度
                long uploadedSize = exitPartList.stream().mapToLong(CustomPartSummary::getSize).sum();
                double progress = task.getTotalSize() > 0 ? (double) uploadedSize / task.getTotalSize() * 100 : 0;

                log.debug("文件上传进度: identifier={}, fileType={}, 已上传={}bytes, 总大小={}bytes, 进度={}%",
                         identifier, fileType, uploadedSize, task.getTotalSize(), String.format("%.2f", progress));

            } catch (Exception e) {
                log.error("获取上传进度时发生错误: identifier={}, fileType={}", identifier, fileType, e);
                // 如果获取分片信息失败，检查是否是因为上传任务超时
                LocalDateTime lastUpdateTime = task.getUpdateTime();
                LocalDateTime now = LocalDateTime.now();
                long minutesSinceLastUpdate = java.time.Duration.between(lastUpdateTime, now).toMinutes();

                if (minutesSinceLastUpdate > 30) {
                    log.warn("上传任务可能已超时: identifier={}, fileType={}, 最后更新时间={}", identifier, fileType, lastUpdateTime);
                }
            }
        }

        return result;
    }

    @Override
    public String genPreSignUploadUrl(String bucket, String objectKey, Map<String, String> params) {
        // 检查是否为分片上传
        if (params != null && params.containsKey("partNumber") && params.containsKey("uploadId")) {
            // 分片上传的预签名URL
            String partNumberStr = params.get("partNumber");
            String uploadId = params.get("uploadId");

            try {
                int partNumber = Integer.parseInt(partNumberStr);

                // 创建分片上传的预签名URL请求
                software.amazon.awssdk.services.s3.presigner.model.UploadPartPresignRequest presignRequest =
                    software.amazon.awssdk.services.s3.presigner.model.UploadPartPresignRequest.builder()
                        .signatureDuration(Duration.ofMillis(MinioConstant.PRE_SIGN_URL_EXPIRE))
                        .uploadPartRequest(UploadPartRequest.builder()
                                .bucket(bucket)
                                .key(objectKey)
                                .uploadId(uploadId)
                                .partNumber(partNumber)
                                .build())
                        .build();

                // 生成预签名URL
                URL preSignedUrl = s3Presigner.presignUploadPart(presignRequest).url();
                log.debug("生成分片上传预签名URL: bucket={}, key={}, uploadId={}, partNumber={}",
                         bucket, objectKey, uploadId, partNumber);
                return preSignedUrl.toString();

            } catch (NumberFormatException e) {
                log.error("分片号格式错误: {}", partNumberStr, e);
                throw new RuntimeException("分片号格式错误: " + partNumberStr);
            }
        } else {
            // 普通文件上传的预签名URL
            PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofMillis(MinioConstant.PRE_SIGN_URL_EXPIRE))
                    .putObjectRequest(PutObjectRequest.builder()
                            .bucket(bucket)
                            .key(objectKey)
                            .build())
                    .build();

            // 生成预签名URL
            URL preSignedUrl = s3Presigner.presignPutObject(presignRequest).url();
            log.debug("生成普通文件上传预签名URL: bucket={}, key={}", bucket, objectKey);
            return preSignedUrl.toString();
        }
    }

    @Override
    public void merge(String identifier, String fileType) {
        SysUploadTask task;
        if(Objects.nonNull(fileType)) {
            task = getByIdentifierAndUserAndType(identifier, getCurrentUsername(), fileType);
        }else {
            task = getByIdentifier(identifier);
        }

        if (task == null) {
            throw new RuntimeException("分片任务不存在");
        }

        log.info("开始合并分片文件: identifier={}, fileName={}, totalSize={}, chunkNum={}",
                identifier, task.getFileName(), task.getTotalSize(), task.getChunkNum());

        // 获取已上传的分片
        ListPartsRequest listPartsRequest = ListPartsRequest.builder()
                .bucket(task.getBucketName())
                .key(task.getObjectKey())
                .uploadId(task.getUploadId())
                .build();
        ListPartsResponse listPartsResponse = amazonS3.listParts(listPartsRequest);
        List<Part> parts = listPartsResponse.parts();

        // 检查分片列表是否为空
        if (parts == null || parts.isEmpty()) {
            log.warn("文件 {} 没有可合并的分片，跳过合并操作", identifier);
            return;
        }
        // 验证分片完整性
        log.info("已上传分片数量: {}, 预期分片数量: {}", parts.size(), task.getChunkNum());

        // 计算已上传分片的总大小
        long uploadedSize = parts.stream().mapToLong(Part::size).sum();
        log.info("已上传分片总大小: {} bytes, 原文件大小: {} bytes", uploadedSize, task.getTotalSize());

        // 检查分片连续性
        for (int i = 0; i < parts.size(); i++) {
            Part part = parts.get(i);
            int expectedPartNumber = i + 1;
            if (part.partNumber() != expectedPartNumber) {
                log.error("分片不连续: 期望分片号 {}, 实际分片号 {}", expectedPartNumber, part.partNumber());
                throw new RuntimeException("分片不连续，无法合并文件");
            }
            log.debug("分片 {} - 大小: {} bytes, ETag: {}", part.partNumber(), part.size(), part.eTag());
        }

        // 完成分片上传
        CompleteMultipartUploadRequest completeMultipartUploadRequest = CompleteMultipartUploadRequest.builder()
                .bucket(task.getBucketName())
                .key(task.getObjectKey())
                .uploadId(task.getUploadId())
                .multipartUpload(CompletedMultipartUpload.builder()
                        .parts(parts.stream()
                                .map(part -> CompletedPart.builder()
                                        .partNumber(part.partNumber())
                                        .eTag(part.eTag())
                                        .build())
                                .collect(Collectors.toList()))
                        .build())
                .build();

        try {
            CompleteMultipartUploadResponse result = amazonS3.completeMultipartUpload(completeMultipartUploadRequest);
            log.info("分片合并成功: identifier={}, fileName={}, location={}",
                    identifier, task.getFileName(), result.location());

            // 验证合并后的文件大小
            try {
                HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                        .bucket(task.getBucketName())
                        .key(task.getObjectKey())
                        .build();
                HeadObjectResponse headObjectResponse = amazonS3.headObject(headObjectRequest);
                long finalFileSize = headObjectResponse.contentLength();
                log.info("合并后文件大小: {} bytes, 原文件大小: {} bytes", finalFileSize, task.getTotalSize());

                if (finalFileSize != task.getTotalSize()) {
                    log.error("文件大小不匹配! 合并后: {} bytes, 预期: {} bytes", finalFileSize, task.getTotalSize());
                }
            } catch (Exception e) {
                log.warn("无法验证合并后的文件大小", e);
            }

            //合并完成后判断该文件是否模型转换工具，如果是需要将该文件推送到所有主机
            if (task.getFileType().equals(FileTypeEnum.MODEL_TRANSFER_TOOL.getCode())) {
                eventPublisher.publishEvent(new TransferToolUpdateEvent(this, task));
            }


        } catch (Exception e) {
            log.error("分片合并失败: identifier={}, fileName={}", identifier, task.getFileName(), e);
            throw new RuntimeException("分片合并失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String genPreSignDownloadUrl(String identifier, Long expires, boolean isGenerateRecord, String fileType) {
        // 获取文件信息
        SysUploadTask task;
        if(Objects.nonNull(fileType)) {
            task = getByIdentifierAndUserAndType(identifier, getCurrentUsername2(), fileType);
        }else {
            task = getByIdentifier(identifier);
        }

        if (task == null) {
            throw new RuntimeException("文件不存在");
        }

        try {
            // 检查文件是否存在
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(task.getBucketName())
                    .key(task.getObjectKey())
                    .build();
            amazonS3.headObject(headObjectRequest);
        } catch (NoSuchKeyException e) {
            throw new RuntimeException("文件不存在或已被删除");
        }

        // 创建预签名下载URL请求
        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                .signatureDuration(Duration.ofSeconds(expires))
                .getObjectRequest(GetObjectRequest.builder()
                        .bucket(task.getBucketName())
                        .key(task.getObjectKey())
                        .responseContentDisposition("attachment; filename=\"" + task.getFileName() + "\"")
                        .build())
                .build();
        
        // 生成预签名URL
        URL url = s3Presigner.presignGetObject(presignRequest).url();

        if (isGenerateRecord) {
            // 获取操作地址，参考AsyncFactory中recordLogininfor的实现
            String ip = IpUtils.getIpAddr();
            String optLocation = AddressUtils.getRealAddressByIP(ip);

            // 记录下载信息
            SysDownloadRecord downloadRecord = new SysDownloadRecord();
            downloadRecord.setDownloadFileName(task.getFileName());
            downloadRecord.setDownloadType(task.getFileType());
            downloadRecord.setDownloadTime(LocalDateTime.now());
            downloadRecord.setUsername(getCurrentUsername());
            downloadRecord.setOptLocation(optLocation);
            downloadRecord.setCreateBy(getCurrentUsername());
            downloadRecord.setCreateTime(LocalDateTime.now());
            downloadRecord.setUpdateBy(getCurrentUsername());
            downloadRecord.setUpdateTime(LocalDateTime.now());
            // 保存下载记录
            sysDownloadRecordService.save(downloadRecord);
        }
        return url.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TaskInfoDTO> batchInitTask(BatchInitTaskParam param) {
        //创建批次号，没上传一次图片则为一个批次
        String batchNo = Long.toString(System.currentTimeMillis());
        String currentUser = getCurrentUsername();

        return param.getFiles().stream().map(file -> {
            // 为每个文件设置额外的类型信息
            String fileName = file.getFileName();
            String fileIdentifier = file.getIdentifier();
            // 优先使用文件自身的类型，如未指定则使用批量参数的类型
            String fileType = file.getFileType() != null ? file.getFileType() : param.getFileType();

            // 先检查当前用户是否已上传过相同MD5和文件类型的文件
            SysUploadTask existingTask = getByIdentifierAndUserAndType(fileIdentifier, currentUser, fileType);
            if (existingTask != null) {
                log.info("批量上传：用户 {} 的文件已存在，MD5: {}, 文件类型: {}, 原文件名: {}, 当前文件名: {}",
                        currentUser, fileIdentifier, fileType, existingTask.getFileName(), fileName);

                // 更新batchNo和fileName，确保在文件选择器中能被识别为最新批次并显示最新文件名
                String oldFileName = existingTask.getFileName(); // 保存旧文件名用于日志
                existingTask.setBatchNo(batchNo);
                existingTask.setFileName(fileName); // 更新为当前上传的文件名
                existingTask.setUpdateTime(LocalDateTime.now());
                existingTask.setUpdateBy(currentUser);

                // 更新数据库中的记录
                sysUploadTaskMapper.updateById(existingTask);

                log.info("批量上传：已更新文件名从 {} 到 {}，batchNo为: {}",
                        oldFileName, fileName, batchNo);

                // 如果当前用户的文件已存在，直接返回已完成的任务信息
                TaskInfoDTO taskInfo = new TaskInfoDTO();
                taskInfo.setFinished(true);
                taskInfo.setTaskRecord(TaskRecordDTO.convertFromEntity(existingTask));
                taskInfo.setPath(getPath(existingTask.getBucketName(), existingTask.getObjectKey()));

                return taskInfo;
            }

            // 初始化每个文件的上传任务
            Date currentDate = new Date();
            String bucketName = minioProperties.getBucket();
            String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            
            String basePath = fileType != null ? fileType : "common";
            String key = StrUtil.format("{}/{}/{}.{}",
                    basePath,
                    DateUtil.format(currentDate, "YYYY-MM-dd"),
                    IdUtil.randomUUID(),
                    suffix);

            String contentType = MediaTypeFactory.getMediaType(key).orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
            
            // 创建分片上传请求
            CreateMultipartUploadRequest createMultipartUploadRequest = CreateMultipartUploadRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .build();
            
            // 初始化分片上传
            CreateMultipartUploadResponse createMultipartUploadResponse = amazonS3.createMultipartUpload(createMultipartUploadRequest);
            String uploadId = createMultipartUploadResponse.uploadId();

            // 创建任务记录
            SysUploadTask task = new SysUploadTask();
            int chunkNum = (int) Math.ceil(file.getTotalSize() * 1.0 / file.getChunkSize());
            task.setBucketName(bucketName)
                    .setBatchNo(batchNo)
                    .setChunkNum(chunkNum)
                    .setChunkSize(file.getChunkSize())
                    .setTotalSize(file.getTotalSize())
                    .setFileIdentifier(fileIdentifier)
                    .setFileName(fileName)
                    .setObjectKey(key)
                    .setFileType(fileType)
                    .setUploadId(uploadId)
                    .setCreateBy(currentUser)
                    .setUpdateBy(currentUser)
                    .setUpdateTime(LocalDateTime.now())
                    .setCreateTime(LocalDateTime.now());
            sysUploadTaskMapper.insert(task);
            
            // 返回任务信息
            return new TaskInfoDTO().setFinished(false).setTaskRecord(TaskRecordDTO.convertFromEntity(task)).setPath(getPath(bucketName, key));
        }).collect(Collectors.toList());
    }
    
    /**
     * 分页查询上传任务列表
     *
     * @param task 查询参数
     * @return 分页数据
     */
    @Override
    public List<SysUploadTask> queryPage(SysUploadTask task) {
        LambdaQueryWrapper<SysUploadTask> queryWrapper = new LambdaQueryWrapper<>();
        // 构建查询条件
        if (StringUtils.isNotEmpty(task.getFileName())) {
            queryWrapper.like(SysUploadTask::getFileName, task.getFileName());
        }
        if (StringUtils.isNotEmpty(task.getFileType())) {
            queryWrapper.eq(SysUploadTask::getFileType, task.getFileType());
        }
        if (StringUtils.isNotEmpty(task.getFileIdentifier())) {
            queryWrapper.eq(SysUploadTask::getFileIdentifier, task.getFileIdentifier());
        }
        if (StringUtils.isNotEmpty(task.getBatchNo())) {
            queryWrapper.like(SysUploadTask::getBatchNo, task.getBatchNo());
        }
        if (StringUtils.isNotEmpty(task.getCreateBy())) {
            queryWrapper.like(SysUploadTask::getCreateBy, task.getCreateBy());
        }
        // 判断排序方向，默认为降序
        boolean isAsc = Objects.nonNull(task.getIsAsc()) && "asc".equalsIgnoreCase(task.getIsAsc());

        //处理模型转换处默认选中最新的创建内容
        if (FileTypeEnum.MODEL.getCode().equals(task.getFileType())) {
            if (Objects.nonNull(task.getSortBy()) && "createTime".equals(task.getSortBy())) {
                queryWrapper.orderBy(true, isAsc, SysUploadTask::getCreateTime);
            } else if (Objects.nonNull(task.getSortBy()) && "batchNo".equals(task.getSortBy())) {
                queryWrapper.orderBy(true, isAsc, SysUploadTask::getBatchNo);
            }
        }
        //处理配置文件转换处默认选中最新的创建内容
        if (FileTypeEnum.CONFIG.getCode().equals(task.getFileType())) {
            if (Objects.nonNull(task.getSortBy()) && "createTime".equals(task.getSortBy())) {
                queryWrapper.orderBy(true, isAsc, SysUploadTask::getCreateTime);
            } else if (Objects.nonNull(task.getSortBy()) && "batchNo".equals(task.getSortBy())) {
                queryWrapper.orderBy(true, isAsc, SysUploadTask::getBatchNo);
            }
        }
        //处理图片转换处默认选中（按照批次分组，然后按照批次号降序排序返回）最后一个批次的内容
        if (FileTypeEnum.IMAGE.getCode().equals(task.getFileType())) {
            queryWrapper.orderBy(Objects.nonNull(task.getSortBy()) && "batchNo".equals(task.getSortBy()), false,SysUploadTask::getBatchNo);
        }
        //模型转换工具
        if (FileTypeEnum.MODEL_TRANSFER_TOOL.getCode().equals(task.getFileType())) {
            queryWrapper.orderByDesc(SysUploadTask::getCreateTime);
        }
        return sysUploadTaskMapper.selectList(queryWrapper);
    }

    /**
     * 获取上传任务详情
     *
     * @param id 任务ID
     * @return 上传任务详情
     */
    @Override
    public SysUploadTask getTaskDetail(Long id) {
        return sysUploadTaskMapper.selectById(id);
    }

    /**
     * 更新上传任务
     *
     * @param task 上传任务信息
     * @return 是否成功
     */
    @Override
    public boolean updateTask(SysUploadTask task) {
        return sysUploadTaskMapper.updateById(task) > 0;
    }

    /**
     * 删除上传任务
     *
     * @param id 任务ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTask(Long id) {
        SysUploadTask task = this.getById(id);
        if (task != null) {
            try {
                // 检查文件是否存在
                HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                        .bucket(task.getBucketName())
                        .key(task.getObjectKey())
                        .build();
                amazonS3.headObject(headObjectRequest);
                
                // 删除对象
                DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                        .bucket(task.getBucketName())
                        .key(task.getObjectKey())
                        .build();
                amazonS3.deleteObject(deleteObjectRequest);
            } catch (NoSuchKeyException e) {
                // 文件不存在，忽略错误
                log.warn("文件不存在，无需删除: {}", task.getObjectKey());
            }
            return this.removeById(id);
        }
        return false;
    }

    /**
     * 批量删除上传任务
     *
     * @param ids 任务ID数组
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(Long[] ids) {
        for (Long id : ids) {
            SysUploadTask task = this.getById(id);
            if (task != null) {
                try {
                    // 检查文件是否存在
                    HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                            .bucket(task.getBucketName())
                            .key(task.getObjectKey())
                            .build();
                    amazonS3.headObject(headObjectRequest);
                    
                    // 删除对象
                    DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                            .bucket(task.getBucketName())
                            .key(task.getObjectKey())
                            .build();
                    amazonS3.deleteObject(deleteObjectRequest);
                } catch (NoSuchKeyException e) {
                    // 文件不存在，忽略错误
                    log.warn("文件不存在，无需删除: {}", task.getObjectKey());
                }
            }
        }
        return this.removeByIds(Arrays.asList(ids));
    }

    @Override
    public SysUploadTask getByFileType(String fileType) {
        return sysUploadTaskMapper.selectOne(
            new LambdaQueryWrapper<SysUploadTask>()
                .eq(SysUploadTask::getFileType, fileType)
                .orderByDesc(SysUploadTask::getCreateTime)
                .last("LIMIT 1")
        );
    }

    @Override
    public void batchDownloadFiles(List<String> identifiers, HttpServletResponse response) throws Exception {
        // 创建临时目录
        String tempDir = System.getProperty("java.io.tmpdir") + File.separator + "download_" + System.currentTimeMillis();
        File dir = new File(tempDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        try {
            // 下载每个文件到临时目录
            for (String identifier : identifiers) {
                SysUploadTask task = getByIdentifier(identifier);
                if (task == null) {
                    continue;
                }

                try {
                    // 检查文件是否存在
                    HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                            .bucket(task.getBucketName())
                            .key(task.getObjectKey())
                            .build();
                    amazonS3.headObject(headObjectRequest);
                    
                    // 下载文件
                    File file = new File(tempDir + File.separator + task.getFileName());
                    GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                            .bucket(task.getBucketName())
                            .key(task.getObjectKey())
                            .build();
                    amazonS3.getObject(getObjectRequest, ResponseTransformer.toFile(file));
                } catch (NoSuchKeyException e) {
                    log.warn("文件不存在，无法下载: {}", task.getObjectKey());
                }
            }

        }catch (Exception e) {
            log.error("批量下载文件失败", e);
            throw e;
        }
    }

    /**
     * 检查文件是否已完成上传
     * @param task 上传任务
     * @return true-已完成，false-未完成或正在上传中
     */
    private boolean checkFileCompleted(SysUploadTask task) {
        try {
            // 检查文件是否存在于存储中
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(task.getBucketName())
                    .key(task.getObjectKey())
                    .build();
            HeadObjectResponse headObjectResponse = amazonS3.headObject(headObjectRequest);

            // 如果文件存在且大小匹配，说明上传已完成
            long actualSize = headObjectResponse.contentLength();
            long expectedSize = task.getTotalSize();

            if (actualSize == expectedSize) {
                log.debug("文件上传已完成: identifier={}, 文件大小={}bytes",
                         task.getFileIdentifier(), actualSize);
                return true;
            } else {
                log.warn("文件大小不匹配: identifier={}, 实际大小={}bytes, 期望大小={}bytes",
                        task.getFileIdentifier(), actualSize, expectedSize);
                return false;
            }
        } catch (NoSuchKeyException e) {
            // 文件不存在，说明还在上传中或上传失败
            log.debug("文件不存在，可能正在上传中: identifier={}", task.getFileIdentifier());
            return false;
        } catch (Exception e) {
            log.error("检查文件完成状态时发生错误: identifier={}", task.getFileIdentifier(), e);
            return false;
        }
    }

    /**
     * 清理超时的上传任务
     * 定期清理超过指定时间未更新的上传任务，释放资源
     */
    public void cleanupTimeoutUploadTasks() {
        try {
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusHours(2); // 2小时超时

            LambdaQueryWrapper<SysUploadTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.lt(SysUploadTask::getUpdateTime, timeoutThreshold);

            List<SysUploadTask> timeoutTasks = this.list(queryWrapper);

            for (SysUploadTask task : timeoutTasks) {
                // 检查文件是否已完成，如果未完成则清理
                if (!checkFileCompleted(task)) {
                    try {
                        // 取消分片上传
                        AbortMultipartUploadRequest abortRequest = AbortMultipartUploadRequest.builder()
                                .bucket(task.getBucketName())
                                .key(task.getObjectKey())
                                .uploadId(task.getUploadId())
                                .build();
                        amazonS3.abortMultipartUpload(abortRequest);

                        // 删除数据库记录
                        this.removeById(task.getId());

                        log.info("已清理超时上传任务: identifier={}, fileName={}, 最后更新时间={}",
                                task.getFileIdentifier(), task.getFileName(), task.getUpdateTime());
                    } catch (Exception e) {
                        log.error("清理超时上传任务失败: identifier={}", task.getFileIdentifier(), e);
                    }
                }
            }

            if (!timeoutTasks.isEmpty()) {
                log.info("清理超时上传任务完成，共处理{}个任务", timeoutTasks.size());
            }
        } catch (Exception e) {
            log.error("清理超时上传任务时发生错误", e);
        }
    }

    /**
     * 获取当前用户名，如果获取失败则返回默认用户名system
     * @return 用户名
     */
    private String getCurrentUsername() {
        try {
            String username = SecurityUtils.getUsername();
            return StringUtils.isNotEmpty(username) ? username : "system";
        } catch (Exception e) {
            return "system";
        }
    }


    /**
     * 获取当前用户名，提供给文件上传调用，如果没有获取到那么就直接返回null
     * @return 用户名
     */
    private String getCurrentUsername2() {
        try {
            String username = SecurityUtils.getUsername();
            return StringUtils.isNotEmpty(username) ? username : null;
        } catch (Exception e) {
            return null;
        }
    }
}
