package com.ruoyi.bussiness.service;

import com.ruoyi.bussiness.model.entity.SysAiTrainEngineer;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_ai_train_engineer】的数据库操作Service
* @createDate 2025-06-11 10:24:48
*/
public interface SysAiTrainEngineerService extends IService<SysAiTrainEngineer> {

    /**
     * 分页查询训练工程列表
     *
     * @param sysAiTrainEngineer 查询条件
     * @return 训练工程列表
     */
    List<SysAiTrainEngineer> listPage(SysAiTrainEngineer sysAiTrainEngineer);

    /**
     * 保存自定义训练工程
     *
     * @param sysAiTrainEngineer 训练工程实体
     * @return 是否保存成功
     */
    boolean saveCustom(SysAiTrainEngineer sysAiTrainEngineer);

    /**
     * 更新自定义训练工程
     *
     * @param sysAiTrainEngineer 训练工程实体
     * @return 是否更新成功
     */
    boolean updateByIdCustom(SysAiTrainEngineer sysAiTrainEngineer);

    /**
     * 根据ID列表删除自定义训练工程
     *
     * @param list ID列表
     * @return 是否删除成功
     */
    boolean removeByIdsCustom(List<Long> list);

    /**
     * 获取下载链接
     *
     * @param identifier 训练工程标识符
     * @return 下载链接
     */
    String getDownloadUrl(String identifier);
}
