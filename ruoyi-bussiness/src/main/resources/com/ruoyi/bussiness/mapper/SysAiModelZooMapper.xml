<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.SysAiModelZooMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.bussiness.model.entity.SysAiModelZoo">
            <id property="id" column="id" />
            <result property="modelType" column="model_type" />
            <result property="modelName" column="model_name" />
            <result property="codeIdentififer" column="code_identififer" />
            <result property="deductivePerformance" column="deductive_performance" />
            <result property="dedcutiveAccuracy" column="dedcutive_accuracy" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,model_type,model_name,code_identififer,deductive_performance,dedcutive_accuracy,
        create_by,create_time,update_by,update_time
    </sql>
</mapper>
