<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.ReportMapper">

    <!-- 查询模型转换地区分布数据 - 使用表本身的opt_location字段 -->
    <select id="selectConversionRegionDistribution" resultType="map">
        SELECT
            CASE
                WHEN t.opt_location LIKE '%北京%' THEN '北京市'
                WHEN t.opt_location LIKE '%上海%' THEN '上海市'
                WHEN t.opt_location LIKE '%天津%' THEN '天津市'
                WHEN t.opt_location LIKE '%重庆%' THEN '重庆省'
                WHEN t.opt_location LIKE '%河北%' THEN '河北省'
                WHEN t.opt_location LIKE '%山西%' THEN '山西省'
                WHEN t.opt_location LIKE '%辽宁%' THEN '辽宁省'
                WHEN t.opt_location LIKE '%吉林%' THEN '吉林省'
                WHEN t.opt_location LIKE '%黑龙江%' THEN '黑龙江省'
                WHEN t.opt_location LIKE '%江苏%' THEN '江苏省'
                WHEN t.opt_location LIKE '%浙江%' THEN '浙江省'
                WHEN t.opt_location LIKE '%安徽%' THEN '安徽省'
                WHEN t.opt_location LIKE '%福建%' THEN '福建省'
                WHEN t.opt_location LIKE '%江西%' THEN '江西省'
                WHEN t.opt_location LIKE '%山东%' THEN '山东省'
                WHEN t.opt_location LIKE '%河南%' THEN '河南省'
                WHEN t.opt_location LIKE '%湖北%' THEN '湖北省'
                WHEN t.opt_location LIKE '%湖南%' THEN '湖南省'
                WHEN t.opt_location LIKE '%广东%' THEN '广东省'
                WHEN t.opt_location LIKE '%海南%' THEN '海南省'
                WHEN t.opt_location LIKE '%四川%' THEN '四川省'
                WHEN t.opt_location LIKE '%贵州%' THEN '贵州省'
                WHEN t.opt_location LIKE '%云南%' THEN '云南省'
                WHEN t.opt_location LIKE '%陕西%' THEN '陕西省'
                WHEN t.opt_location LIKE '%甘肃%' THEN '甘肃省'
                WHEN t.opt_location LIKE '%青海%' THEN '青海省'
                WHEN t.opt_location LIKE '%台湾%' THEN '台湾省'
                WHEN t.opt_location LIKE '%内蒙古%' THEN '内蒙古自治区'
                WHEN t.opt_location LIKE '%广西%' THEN '广西壮族自治区'
                WHEN t.opt_location LIKE '%西藏%' THEN '西藏自治区'
                WHEN t.opt_location LIKE '%宁夏%' THEN '宁夏回族自治区'
                WHEN t.opt_location LIKE '%新疆%' THEN '新疆维吾尔自治区'
                WHEN t.opt_location LIKE '%香港%' THEN '香港特别行政区'
                WHEN t.opt_location LIKE '%澳门%' THEN '澳门特别行政区'
                ELSE '其他'
            END as name,
            COUNT(t.id) as value
        FROM sys_ai_transfer_record t
        WHERE t.opt_location IS NOT NULL AND t.opt_location != ''
        <if test="startTime != null and startTime != ''">
            AND DATE(t.create_time) &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(t.create_time) &lt;= #{endTime}
        </if>
        GROUP BY name
        ORDER BY value DESC
    </select>

    <!-- 查询SDK下载地区分布数据 - 使用表本身的opt_location字段 -->
    <select id="selectSdkDownloadRegionDistribution" resultType="map">
        SELECT
            CASE
                WHEN d.opt_location LIKE '%北京%' THEN '北京市'
                WHEN d.opt_location LIKE '%上海%' THEN '上海市'
                WHEN d.opt_location LIKE '%天津%' THEN '天津市'
                WHEN d.opt_location LIKE '%重庆%' THEN '重庆省'
                WHEN d.opt_location LIKE '%河北%' THEN '河北省'
                WHEN d.opt_location LIKE '%山西%' THEN '山西省'
                WHEN d.opt_location LIKE '%辽宁%' THEN '辽宁省'
                WHEN d.opt_location LIKE '%吉林%' THEN '吉林省'
                WHEN d.opt_location LIKE '%黑龙江%' THEN '黑龙江省'
                WHEN d.opt_location LIKE '%江苏%' THEN '江苏省'
                WHEN d.opt_location LIKE '%浙江%' THEN '浙江省'
                WHEN d.opt_location LIKE '%安徽%' THEN '安徽省'
                WHEN d.opt_location LIKE '%福建%' THEN '福建省'
                WHEN d.opt_location LIKE '%江西%' THEN '江西省'
                WHEN d.opt_location LIKE '%山东%' THEN '山东省'
                WHEN d.opt_location LIKE '%河南%' THEN '河南省'
                WHEN d.opt_location LIKE '%湖北%' THEN '湖北省'
                WHEN d.opt_location LIKE '%湖南%' THEN '湖南省'
                WHEN d.opt_location LIKE '%广东%' THEN '广东省'
                WHEN d.opt_location LIKE '%海南%' THEN '海南省'
                WHEN d.opt_location LIKE '%四川%' THEN '四川省'
                WHEN d.opt_location LIKE '%贵州%' THEN '贵州省'
                WHEN d.opt_location LIKE '%云南%' THEN '云南省'
                WHEN d.opt_location LIKE '%陕西%' THEN '陕西省'
                WHEN d.opt_location LIKE '%甘肃%' THEN '甘肃省'
                WHEN d.opt_location LIKE '%青海%' THEN '青海省'
                WHEN d.opt_location LIKE '%台湾%' THEN '台湾省'
                WHEN d.opt_location LIKE '%内蒙古%' THEN '内蒙古自治区'
                WHEN d.opt_location LIKE '%广西%' THEN '广西壮族自治区'
                WHEN d.opt_location LIKE '%西藏%' THEN '西藏自治区'
                WHEN d.opt_location LIKE '%宁夏%' THEN '宁夏回族自治区'
                WHEN d.opt_location LIKE '%新疆%' THEN '新疆维吾尔自治区'
                WHEN d.opt_location LIKE '%香港%' THEN '香港特别行政区'
                WHEN d.opt_location LIKE '%澳门%' THEN '澳门特别行政区'
                ELSE '其他'
            END as name,
            COUNT(d.id) as value
        FROM sys_download_record d
        WHERE d.opt_location IS NOT NULL AND d.opt_location != ''
            AND d.download_type = 'lfnn-version'
        <if test="startTime != null and startTime != ''">
            AND DATE(d.create_time) &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(d.create_time) &lt;= #{endTime}
        </if>
        GROUP BY name
        ORDER BY value DESC
    </select>



    <!-- 查询平台使用趋势数据 - 访问次数 -->
    <select id="selectAccessTrend" resultType="map">
        <choose>
            <when test="timeUnit == 'day'">
                <!-- 日：展示最近30天的每天趋势，如果有时间范围参数则使用参数范围 -->
                SELECT
                    DATE_FORMAT(login_time, '%Y-%m-%d') as time_label,
                    COUNT(*) as count
                FROM sys_logininfor
                WHERE status = '0' and msg = '登录成功'
                <choose>
                    <when test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                        <!-- 如果有明确的时间范围，使用传入的时间范围 -->
                        AND login_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
                        AND login_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
                    </when>
                    <otherwise>
                        <!-- 默认展示最近30天的每天趋势 -->
                        AND login_time &gt;= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                        AND login_time &lt;= CONCAT(CURDATE(), ' 23:59:59')
                    </otherwise>
                </choose>
                GROUP BY DATE(login_time), DATE_FORMAT(login_time, '%Y-%m-%d')
                ORDER BY DATE(login_time)
            </when>
            <when test="timeUnit == 'year'">
                <!-- 年：展示当前年的所有数据，如果有时间范围参数则使用参数范围 -->
                SELECT
                    DATE_FORMAT(login_time, '%Y') as time_label,
                    COUNT(*) as count
                FROM sys_logininfor
                WHERE status = '0' and msg = '登录成功'
                <choose>
                    <when test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                        <!-- 如果有明确的时间范围，使用传入的时间范围 -->
                        AND login_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
                        AND login_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
                    </when>
                    <otherwise>
                        <!-- 默认展示当前年往前6年的数据 -->
                        AND YEAR(login_time)  &gt;= YEAR(CURDATE()) - 5
                        AND YEAR(login_time)  &lt;= YEAR(CURDATE())
                    </otherwise>
                </choose>
                GROUP BY DATE_FORMAT(login_time, '%Y')
                ORDER BY DATE_FORMAT(login_time, '%Y')
            </when>
            <otherwise>
                <!-- 月：当前月前12个月的数据，如果有时间范围参数则使用参数范围 -->
                SELECT
                    DATE_FORMAT(login_time, '%Y-%m') as time_label,
                    COUNT(*) as count
                FROM sys_logininfor
                WHERE status = '0' and msg = '登录成功'
                <choose>
                    <when test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                        AND DATE(login_time) &gt;= STR_TO_DATE(CONCAT(#{startTime}, '-01'), '%Y-%m-%d')
                        AND DATE(login_time) &lt;= LAST_DAY(STR_TO_DATE(CONCAT(#{endTime}, '-01'), '%Y-%m-%d'))
                    </when>
                    <when test="startTime != null and startTime != ''">
                        AND DATE(login_time) &gt;= STR_TO_DATE(CONCAT(#{startTime}, '-01'), '%Y-%m-%d')
                        AND DATE(login_time) &lt;= CURDATE()
                    </when>
                    <when test="endTime != null and endTime != ''">
                        AND DATE(login_time) &gt;= DATE_SUB(STR_TO_DATE(CONCAT(#{endTime}, '-01'), '%Y-%m-%d'), INTERVAL 11 MONTH)
                        AND DATE(login_time) &lt;= LAST_DAY(STR_TO_DATE(CONCAT(#{endTime}, '-01'), '%Y-%m-%d'))
                    </when>
                    <otherwise>
                        AND DATE(login_time) &gt;= DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 11 MONTH)
                        AND DATE(login_time) &lt;= CURDATE()
                    </otherwise>
                </choose>
                GROUP BY DATE_FORMAT(login_time, '%Y-%m')
                ORDER BY DATE_FORMAT(login_time, '%Y-%m')
            </otherwise>
        </choose>
    </select>

    <!-- 查询平台使用趋势数据 - 模型转换次数 -->
    <select id="selectConversionTrend" resultType="map">
        <choose>
            <when test="timeUnit == 'day'">
                SELECT
                    DATE_FORMAT(create_time, '%Y-%m-%d') as time_label,
                    COUNT(*) as count
                FROM sys_ai_transfer_record
                WHERE 1=1
                <if test="startTime != null and startTime != ''">
                    AND DATE(create_time) &gt;= #{startTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    AND DATE(create_time) &lt;= #{endTime}
                </if>
                GROUP BY DATE(create_time), DATE_FORMAT(create_time, '%Y-%m-%d')
                ORDER BY DATE(create_time)
            </when>
            <when test="timeUnit == 'year'">
                <!-- 年：展示当前年的所有数据，如果有时间范围参数则使用参数范围 -->
                SELECT
                    DATE_FORMAT(create_time, '%Y') as time_label,
                    COUNT(*) as count
                FROM sys_ai_transfer_record
                WHERE 1=1
                <choose>
                    <when test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                        <!-- 如果有明确的时间范围，使用传入的时间范围 -->
                        AND DATE(create_time) &gt;= #{startTime}
                        AND DATE(create_time) &lt;= #{endTime}
                    </when>
                    <otherwise>
                        <!-- 默认展示当前年往前6年的数据 -->
                        AND YEAR(create_time)  &gt;= YEAR(CURDATE()) - 5
                        AND YEAR(create_time)  &lt;= YEAR(CURDATE())
                    </otherwise>
                </choose>
                GROUP BY DATE_FORMAT(create_time, '%Y')
                ORDER BY DATE_FORMAT(create_time, '%Y')
            </when>
            <otherwise>
                <!-- 月：当前月前12个月的数据，返回yyyy-MM格式的标签 -->
                SELECT
                    DATE_FORMAT(create_time, '%Y-%m') as time_label,
                    COUNT(*) as count
                FROM sys_ai_transfer_record
                WHERE 1=1
                <choose>
                    <when test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                        AND DATE(create_time) &gt;= STR_TO_DATE(CONCAT(#{startTime}, '-01'), '%Y-%m-%d')
                        AND DATE(create_time) &lt;= LAST_DAY(STR_TO_DATE(CONCAT(#{endTime}, '-01'), '%Y-%m-%d'))
                    </when>
                    <when test="startTime != null and startTime != ''">
                        AND DATE(create_time) &gt;= STR_TO_DATE(CONCAT(#{startTime}, '-01'), '%Y-%m-%d')
                        AND DATE(create_time) &lt;= CURDATE()
                    </when>
                    <when test="endTime != null and endTime != ''">
                        AND DATE(create_time) &gt;= DATE_SUB(STR_TO_DATE(CONCAT(#{endTime}, '-01'), '%Y-%m-%d'), INTERVAL 11 MONTH)
                        AND DATE(create_time) &lt;= LAST_DAY(STR_TO_DATE(CONCAT(#{endTime}, '-01'), '%Y-%m-%d'))
                    </when>
                    <otherwise>
                        AND DATE(create_time) &gt;= DATE_SUB(DATE_FORMAT(CURDATE(), '%Y-%m-01'), INTERVAL 11 MONTH)
                        AND DATE(create_time) &lt;= CURDATE()
                    </otherwise>
                </choose>
                GROUP BY DATE_FORMAT(create_time, '%Y-%m')
                ORDER BY DATE_FORMAT(create_time, '%Y-%m')
            </otherwise>
        </choose>
    </select>

    <!-- 查询各模块访问分布数据 -->
    <!-- 查询各模块访问分布数据 -->
    <select id="selectModuleDistribution" resultType="map">
        SELECT
            CASE
                WHEN access_type = 'modelConversion' THEN '模型转换'
                WHEN access_type = 'document' THEN '文档'
                WHEN access_type = 'lfnn' THEN 'SDK下载'
                WHEN access_type = 'modelZoo' THEN 'Model Zoo'
                WHEN access_type = 'trainEngineer' THEN '示例训练工程'
                ELSE '其它'
            END as name,
            SUM(access_count) as value
        FROM sys_ai_access_record
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND DATE(create_time) &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(create_time) &lt;= #{endTime}
        </if>
        GROUP BY access_type
        ORDER BY value DESC
    </select>

    <!-- 查询用户类型分布数据 -->
    <select id="selectUserTypeDistribution" resultType="map">
        SELECT
            CASE
                WHEN user_type = 'personal' THEN '个人'
                WHEN user_type = 'university' THEN '高校'
                WHEN user_type = 'company' THEN '公司'
                WHEN user_type = 'business_evaluation' THEN '商业评估'
                WHEN user_type = 'business_contract' THEN '商业合同'
                WHEN user_type = 'admin' THEN '管理员'
                ELSE '其他'
            END as name,
            COUNT(*) as value
        FROM sys_user
        WHERE del_flag = '0'
        <if test="startTime != null and startTime != ''">
            AND DATE(create_time) &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(create_time) &lt;= #{endTime}
        </if>
        GROUP BY name
        ORDER BY value DESC
    </select>

    <!-- 查询模型转换成功率数据 -->
    <select id="selectConversionRate" resultType="map">
        SELECT
            CASE
                WHEN status = '1' THEN '成功'
                ELSE '失败'
            END as name,
            COUNT(*) as value
        FROM sys_ai_transfer_record
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND DATE(create_time) &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(create_time) &lt;= #{endTime}
        </if>
        GROUP BY name
        ORDER BY value DESC
    </select>

    <!-- 查询注册用户总数 -->
    <select id="selectTotalUsers" resultType="int">
        SELECT COUNT(*)
        FROM sys_user
        WHERE del_flag = '0'
    </select>

    <!-- 查询用户操作总次数 -->
    <select id="selectTotalVisits" resultType="int">
        SELECT COUNT(*)
        FROM sys_oper_log
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND DATE(oper_time) &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(oper_time) &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询用户登录总次数 只记录登录成功-->
    <select id="selectTotalLogins" resultType="int">
        SELECT COUNT(*)
        FROM sys_logininfor
        WHERE status = '0' and msg = '登录成功'
        <if test="startTime != null and startTime != ''">
            AND DATE(login_time) &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(login_time) &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询SDK下载次数总和 -->
    <select id="selectSdkDownloadCount" resultType="int">
        SELECT COALESCE(SUM(download_count), 0)
        FROM sys_lfnn_version
    </select>

    <!-- 查询示例训练工程下载次数总和 -->
    <select id="selectTrainEngineerDownloadCount" resultType="int">
        SELECT COALESCE(SUM(download_count), 0)
        FROM sys_ai_train_engineer
    </select>

    <!-- 查询原始模型数（文件类型为model的文件数量） -->
    <select id="selectOriginalModelCount" resultType="int">
        SELECT COUNT(*)
        FROM sys_upload_task
        WHERE file_type = 'model'
    </select>

</mapper>
