<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.SysUploadTaskTypeMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.bussiness.model.entity.SysUploadTaskType">
            <id property="id" column="id" />
            <result property="fileTypeName" column="file_type_name" />
            <result property="fileTypeIdentifier" column="file_type_identifier" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,file_type_name,file_type_identifier,create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
