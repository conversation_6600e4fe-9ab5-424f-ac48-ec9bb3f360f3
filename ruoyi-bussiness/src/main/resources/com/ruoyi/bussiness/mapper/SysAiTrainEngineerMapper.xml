<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.SysAiTrainEngineerMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.bussiness.model.entity.SysAiTrainEngineer">
            <id property="id" column="id" />
            <result property="modelType" column="model_type" />
            <result property="modelName" column="model_name" />
            <result property="modelDsc" column="model_dsc" />
            <result property="fileIdentifier" column="file_identifier" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,model_type,model_name,model_dsc,file_identifier,create_by,
        create_time,update_by,update_time
    </sql>
</mapper>
