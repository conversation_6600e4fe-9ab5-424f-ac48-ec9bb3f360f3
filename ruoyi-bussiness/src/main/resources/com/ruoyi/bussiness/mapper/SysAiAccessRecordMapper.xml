<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.SysAiAccessRecordMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.bussiness.model.entity.SysAiAccessRecord">
            <id property="id" column="id" />
            <result property="accessType" column="access_type" />
            <result property="accessCount" column="access_count" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,access_type,access_count,create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
