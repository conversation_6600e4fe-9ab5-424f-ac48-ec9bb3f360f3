<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.bussiness.mapper.SysDownloadRecordMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.bussiness.model.entity.SysDownloadRecord">
            <id property="id" column="id" />
            <result property="downloadFileName" column="download_file_name" />
            <result property="downloadType" column="download_type" />
            <result property="downloadTime" column="download_time" />
            <result property="username" column="username" />
            <result property="optLocation" column="opt_location" />
            <result property="remark" column="remark" />
            <result property="createBy" column="create_by" />
            <result property="createTime" column="create_time" />
            <result property="updateBy" column="update_by" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,download_file_name,download_type,download_time,username,opt_location,remark,
        create_by,create_time,update_by,update_time
    </sql>
</mapper>
