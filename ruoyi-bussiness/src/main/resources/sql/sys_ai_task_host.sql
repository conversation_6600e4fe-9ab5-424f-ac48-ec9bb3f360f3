CREATE TABLE `sys_ai_task_host` (
  `host_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主机ID',
  `host_name` varchar(50) NOT NULL COMMENT '主机名称',
  `host_ip` varchar(50) NOT NULL COMMENT '主机IP',
  `ssh_port` int(11) NOT NULL DEFAULT '22' COMMENT 'SSH端口',
  `ssh_username` varchar(50) NOT NULL COMMENT 'SSH用户名',
  `ssh_password` varchar(100) NOT NULL COMMENT 'SSH密码',
  `executable_path` varchar(255) NOT NULL COMMENT '可执行文件路径',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '主机状态（0空闲 1执行中 2离线）',
  `cpu_usage` decimal(5,2) DEFAULT NULL COMMENT 'CPU使用率',
  `memory_usage` decimal(5,2) DEFAULT NULL COMMENT '内存使用率',
  `disk_usage` decimal(5,2) DEFAULT NULL COMMENT '磁盘使用率',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`host_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI任务主机信息表'; 