package com.ruoyi.framework.web.service;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.ISysRoleService;

/**
 * 角色有效期验证服务
 * 提供统一的角色有效期检查方法
 * 
 * <AUTHOR>
 */
@Service
public class RoleValidityService
{
    private static final Logger log = LoggerFactory.getLogger(RoleValidityService.class);
    
    @Autowired
    private ISysRoleService roleService;
    
    /**
     * 检查用户是否有有效的角色
     * 
     * @param user 用户信息
     * @return true-有有效角色，false-没有有效角色
     */
    public boolean hasValidRoles(SysUser user)
    {
        if (user == null)
        {
            return false;
        }
        
        // 管理员用户始终有效
        if (user.isAdmin())
        {
            return true;
        }
        
        try
        {
            // 获取用户的有效角色列表
            List<SysRole> validRoles = roleService.selectValidRolesByUserId(user.getUserId());
            boolean hasValid = validRoles != null && !validRoles.isEmpty();
            
            if (!hasValid)
            {
                log.info("用户 {} (ID: {}) 没有有效的角色权限", user.getUserName(), user.getUserId());
            }
            else
            {
                log.debug("用户 {} (ID: {}) 有 {} 个有效角色", user.getUserName(), user.getUserId(), validRoles.size());
            }
            
            return hasValid;
        }
        catch (Exception e)
        {
            log.error("检查用户 {} 角色有效性时发生异常", user.getUserName(), e);
            return false;
        }
    }
    
    /**
     * 检查用户是否有有效的角色（通过用户ID）
     * 
     * @param userId 用户ID
     * @return true-有有效角色，false-没有有效角色
     */
    public boolean hasValidRoles(Long userId)
    {
        if (userId == null)
        {
            return false;
        }
        
        // 管理员用户始终有效
        if (SysUser.isAdmin(userId))
        {
            return true;
        }
        
        try
        {
            // 获取用户的有效角色列表
            List<SysRole> validRoles = roleService.selectValidRolesByUserId(userId);
            boolean hasValid = validRoles != null && !validRoles.isEmpty();
            
            if (!hasValid)
            {
                log.info("用户 ID {} 没有有效的角色权限", userId);
            }
            else
            {
                log.debug("用户 ID {} 有 {} 个有效角色", userId, validRoles.size());
            }
            
            return hasValid;
        }
        catch (Exception e)
        {
            log.error("检查用户 ID {} 角色有效性时发生异常", userId, e);
            return false;
        }
    }
    
    /**
     * 获取用户的有效角色列表
     * 
     * @param userId 用户ID
     * @return 有效角色列表
     */
    public List<SysRole> getValidRoles(Long userId)
    {
        if (userId == null)
        {
            return null;
        }
        
        try
        {
            return roleService.selectValidRolesByUserId(userId);
        }
        catch (Exception e)
        {
            log.error("获取用户 ID {} 有效角色列表时发生异常", userId, e);
            return null;
        }
    }
    
    /**
     * 检查角色列表是否发生变化
     * 
     * @param currentRoles 当前角色列表
     * @param validRoles 有效角色列表
     * @return true-发生变化，false-未发生变化
     */
    public boolean isRolesChanged(List<SysRole> currentRoles, List<SysRole> validRoles)
    {
        if (currentRoles == null && validRoles == null)
        {
            return false;
        }
        
        if (currentRoles == null || validRoles == null)
        {
            return true;
        }
        
        if (currentRoles.size() != validRoles.size())
        {
            return true;
        }
        
        // 检查角色ID是否一致
        for (SysRole currentRole : currentRoles)
        {
            boolean found = false;
            for (SysRole validRole : validRoles)
            {
                if (currentRole.getRoleId().equals(validRole.getRoleId()))
                {
                    found = true;
                    break;
                }
            }
            if (!found)
            {
                return true;
            }
        }
        
        return false;
    }
}
