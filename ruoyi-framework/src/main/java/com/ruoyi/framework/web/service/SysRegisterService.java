package com.ruoyi.framework.web.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 注册校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysRegisterService
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysSmsService smsService;

    /**
     * 用户类型与角色ID的映射关系  初始情况的角色赋予与默认的角色id绑定 后期管理员认证后，修改对应的角色
     */
    private static final Map<String, Long> USER_TYPE_ROLE_MAP = new HashMap<String, Long>() {{
        put("personal", 100L);           // 个人用户 -> 普通角色
        put("university", 101L);         // 高校用户 -> 普通角色
        put("company", 102L);            // 公司用户 -> 普通角色
        put("business_evaluation", 103L); // 商业评估用户 -> 普通角色
        put("business_contract", 104L);   // 商业合同用户 -> 普通角色
        put("admin", 1L);              // 管理员 -> 超级管理员角色
    }};

    /**
     * 统一注册方法
     */
    public String register(RegisterBody registerBody)
    {
        String msg = "", username = registerBody.getUsername(), password = registerBody.getPassword();
        String phoneNumber = registerBody.getPhoneNumber();
        String smsCode = registerBody.getSmsCode();
        String email = registerBody.getEmail();
        String userType = registerBody.getUserType();

        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);

//        // 验证码开关
//        boolean captchaEnabled = configService.selectCaptchaEnabled();
//        if (captchaEnabled)
//        {
//            validateCaptcha(username, registerBody.getCode(), registerBody.getUuid());
//        }

        // 验证短信验证码
        if (StringUtils.isNotEmpty(phoneNumber) && StringUtils.isNotEmpty(smsCode))
        {
            // 确保使用统一的手机号码格式进行验证
            String phoneForValidation = normalizePhoneNumber(phoneNumber);
            smsService.validateSmsCode(phoneForValidation, smsCode);
        }

        // 基础字段验证
        msg = validateBasicFields(username, password, phoneNumber, smsCode, email, userType, registerBody.getConfirmPassword());
        if (StringUtils.isNotEmpty(msg)) {
            return msg;
        }

        // 验证协议同意状态
        msg = validateAgreement(registerBody.getAgreement());
        if (StringUtils.isNotEmpty(msg)) {
            return msg;
        }

        // 验证用户名和手机号唯一性
        if (!userService.checkUserNameUnique(sysUser))
        {
            return "保存用户'" + username + "'失败，注册账号已存在";
        }
        if (!checkPhoneNumberUnique(phoneNumber))
        {
            return "保存用户'" + username + "'失败，手机号码已存在";
        }

        // 验证企业用户必填字段（但不验证文件，因为文件将在注册成功后上传）
        if (isCompanyUser(userType)) {
            msg = validateCompanyFieldsWithoutFiles(registerBody);
            if (StringUtils.isNotEmpty(msg)) {
                return msg;
            }
        }

        // 设置用户基本信息
        setupUserInfo(sysUser, registerBody, username, password, phoneNumber, email, userType);

        // 执行注册
        boolean regFlag = userService.registerUser(sysUser);
        if (!regFlag)
        {
            return "注册失败,请联系系统管理人员";
        }
        else
        {
            // 注册成功后，记录用户ID到RegisterBody中，供后续文件上传使用
            registerBody.setUserId(sysUser.getUserId());

            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
        }
        return msg;
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            throw new CaptchaException();
        }
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param phoneNumber 手机号码
     * @return 结果
     */
    public boolean checkPhoneNumberUnique(String phoneNumber)
    {
        SysUser user = new SysUser();
        user.setPhonenumber(phoneNumber);
        return userService.checkPhoneUnique(user);
    }

    /**
     * 验证基础字段
     */
    private String validateBasicFields(String username, String password, String phoneNumber, String smsCode, String email, String userType, String confirmPassword) {
        if (StringUtils.isEmpty(username)) {
            return "用户名不能为空";
        }
        if (StringUtils.isEmpty(password)) {
            return "用户密码不能为空";
        }
        if (StringUtils.isEmpty(phoneNumber)) {
            return "手机号码不能为空";
        }
        if (StringUtils.isEmpty(smsCode)) {
            return "短信验证码不能为空";
        }
        if (StringUtils.isEmpty(email)) {
            return "邮箱不能为空";
        }
        if (StringUtils.isEmpty(userType)) {
            return "用户类型不能为空";
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            return "账户长度必须在2到20个字符之间";
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            return "密码长度必须在5到20个字符之间";
        }
        // 验证密码格式
        if (!isValidPassword(password)) {
            return "不能包含非法字符：< > \" ' \\ |";
        }
        // 验证确认密码
        if (StringUtils.isEmpty(confirmPassword)) {
            return "确认密码不能为空";
        }
        if (!password.equals(confirmPassword)) {
            return "两次输入的密码不一致";
        }
        // 验证手机号格式
        if (!isValidPhone(phoneNumber)) {
            return "请输入正确的手机号码";
        }
        // 验证邮箱格式
        if (!isValidEmail(email)) {
            return "邮箱格式不正确";
        }
        return "";
    }

    /**
     * 验证协议同意状态
     */
    private String validateAgreement(Boolean agreement) {
        if (agreement == null || !agreement) {
            return "请阅读并同意用户协议和隐私政策";
        }
        return "";
    }

    /**
     * 验证密码格式
     */
    private boolean isValidPassword(String password) {
        String passwordRegex = "^[^<>\"'|\\\\]+$";
        return password.matches(passwordRegex);
    }

    /**
     * 验证手机号格式
     */
    private boolean isValidPhone(String phoneNumber) {
        String phoneRegex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(phoneRegex);
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.matches(emailRegex);
    }

    /**
     * 判断是否为企业用户
     */
    private boolean isCompanyUser(String userType) {
        return "company".equals(userType) || "business_evaluation".equals(userType) || "business_contract".equals(userType);
    }

    /**
     * 验证企业用户必填字段（包含文件验证）
     */
    private String validateCompanyFields(RegisterBody registerBody) {
        if (StringUtils.isEmpty(registerBody.getCompanyName())) {
            return "公司名称不能为空";
        }
        if (StringUtils.isEmpty(registerBody.getCompanyWebsite())) {
            return "公司官网不能为空";
        }
        // 验证是否上传了必要的文件
        if (registerBody.getFileIdentifiers() == null || registerBody.getFileIdentifiers().isEmpty()) {
            return "企业用户必须上传公司注册信息和NDA合同";
        }
        return "";
    }

    /**
     * 验证企业用户必填字段（不验证文件，用于注册阶段）
     */
    private String validateCompanyFieldsWithoutFiles(RegisterBody registerBody) {
        if (StringUtils.isEmpty(registerBody.getCompanyName())) {
            return "公司名称不能为空";
        }
        if (StringUtils.isEmpty(registerBody.getCompanyWebsite())) {
            return "公司官网不能为空";
        }

        // 商业合同用户需要验证合同号
        if ("business_contract".equals(registerBody.getUserType())) {
            if (StringUtils.isEmpty(registerBody.getContractNumber())) {
                return "合作合同号不能为空";
            }
        }

        return "";
    }

    /**
     * 设置用户信息
     */
    private void setupUserInfo(SysUser sysUser, RegisterBody registerBody, String username, String password, String phoneNumber, String email, String userType) {
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        sysUser.setPhonenumber(phoneNumber);
        sysUser.setEmail(email);
        sysUser.setUpdateTime(new Date());
        sysUser.setCreateTime(new Date());

        // 设置用户类型
        sysUser.setUserType(userType);

        // 设置协议同意状态（注册时必须同意协议，所以设置为"1"）
        sysUser.setAgreement(registerBody.getAgreement() != null && registerBody.getAgreement() ? "1" : "0");

        // 根据用户类型设置角色
        Long roleId = USER_TYPE_ROLE_MAP.getOrDefault(userType, 2L); // 默认为普通用户角色
        sysUser.setRoleIds(new Long[]{roleId});

        // 设置企业用户的额外信息
        if (isCompanyUser(userType)) {
            sysUser.setCompanyName(registerBody.getCompanyName());
            sysUser.setCompanyWebsite(registerBody.getCompanyWebsite());

            // 商业合同用户设置合同号
            if ("business_contract".equals(userType)) {
                sysUser.setContractNumber(registerBody.getContractNumber());
            }
        }

        // 简化备注信息
        sysUser.setRemark("注册用户 - " + getUserTypeDesc(userType));
    }

    /**
     * 获取用户类型描述
     */
    private String getUserTypeDesc(String userType) {
        switch (userType) {
            case "personal": return "个人";
            case "university": return "高校";
            case "company": return "公司";
            case "business_evaluation": return "商业评估";
            case "business_contract": return "商业合同";
            case "admin": return "管理员";
            default: return "未知";
        }
    }

    /**
     * 标准化手机号码格式，确保发送验证码和验证验证码使用相同的格式
     *
     * @param phoneNumber 原始手机号码
     * @return 标准化后的手机号码
     */
    private String normalizePhoneNumber(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return phoneNumber;
        }

        // 移除可能的空格、破折号等字符
        String normalized = phoneNumber.replaceAll("[\\s-]", "");

        // 确保是11位数字的中国手机号
        if (normalized.matches("^1[3-9]\\d{9}$")) {
            return normalized;
        }

        return phoneNumber; // 如果不符合标准格式，返回原始号码
    }

    /**
     * 将文件关联到已注册的用户（公开方法，供Controller调用）
     */
    public void associateFilesToUser(Long userId, List<String> fileIdentifiers) {
        if (userId == null || fileIdentifiers == null || fileIdentifiers.isEmpty()) {
            return;
        }

        try {
            // 获取用户信息
            SysUser user = userService.selectUserById(userId);
            if (user != null) {
                // 更新用户files字段，添加文件信息
                String currentFiles = StringUtils.isNotEmpty(user.getFiles()) ? user.getFiles() : "";
                StringBuilder newFiles = new StringBuilder(currentFiles);

                if (!currentFiles.isEmpty()) {
                    newFiles.append(";");
                }
                for (int i = 0; i < fileIdentifiers.size(); i++) {
                    if (i > 0) {
                        newFiles.append(";");
                    }
                    newFiles.append(fileIdentifiers.get(i));
                }

                // 更新用户files字段
                user.setFiles(newFiles.toString());
                userService.updateUserProfile(user);
            }
        } catch (Exception e) {
            // 文件关联失败不影响注册结果，只记录日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(
                "用户ID:" + userId,
                Constants.REGISTER,
                "文件关联失败：" + e.getMessage()
            ));
        }

        // 记录文件关联日志
        for (String identifier : fileIdentifiers) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(
                "用户ID:" + userId,
                Constants.REGISTER,
                "注册后关联上传文件：" + identifier
            ));
        }
    }
}
