package com.ruoyi.framework.web.service;

import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SmsUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;

/**
 * 短信验证码服务
 * 
 * <AUTHOR>
 */
@Component
public class SysSmsService
{
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SmsUtils smsUtils;

    /**
     * 发送短信验证码
     * 
     * @param phoneNumber 手机号码
     * @return 发送结果消息
     */
    public String sendSmsCode(String phoneNumber)
    {
        // 验证手机号格式
        if (StringUtils.isEmpty(phoneNumber))
        {
            return "手机号码不能为空";
        }
        
        if (!isValidPhoneNumber(phoneNumber))
        {
            return "手机号码格式不正确";
        }

        // 检查发送频率限制（60秒内只能发送一次）
        String rateLimitKey = CacheConstants.SMS_CODE_KEY + "rate_limit:" + phoneNumber;
        if (redisCache.hasKey(rateLimitKey))
        {
            return "发送过于频繁，请稍后再试";
        }

        // 生成验证码
        String code = smsUtils.generateCode();
        
        // 发送短信
        boolean sendResult = smsUtils.sendSmsCode(phoneNumber, code);
        
        if (sendResult)
        {
            // 将验证码存储到Redis，有效期5分钟（短信验证码比图形验证码需要更长的有效期）
            String verifyKey = CacheConstants.SMS_CODE_KEY + phoneNumber;
            redisCache.setCacheObject(verifyKey, code, Constants.SMS_CODE_EXPIRATION, TimeUnit.MINUTES);

            // 设置发送频率限制，60秒
            redisCache.setCacheObject(rateLimitKey, "1", 60, TimeUnit.SECONDS);

            // 记录日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(phoneNumber, Constants.REGISTER, "短信验证码发送成功"));

            return "";
        }
        else
        {
            return "短信发送失败，请稍后重试";
        }
    }

    /**
     * 验证短信验证码
     *
     * @param phoneNumber 手机号码
     * @param code 验证码
     * @return 验证结果
     */
    public void validateSmsCode(String phoneNumber, String code)
    {
        String verifyKey = CacheConstants.SMS_CODE_KEY + StringUtils.nvl(phoneNumber, "");
        String smsCode = redisCache.getCacheObject(verifyKey);

        if (smsCode == null)
        {
            throw new CaptchaExpireException();
        }

        if (!code.equals(smsCode))
        {
            throw new CaptchaException();
        }

        // 验证成功后才删除验证码，防止验证失败时用户无法重试
        redisCache.deleteObject(verifyKey);
    }

    /**
     * 验证手机号格式
     * 
     * @param phoneNumber 手机号码
     * @return 是否有效
     */
    private boolean isValidPhoneNumber(String phoneNumber)
    {
        // 简单的手机号验证正则表达式
        String regex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(regex);
    }
}
