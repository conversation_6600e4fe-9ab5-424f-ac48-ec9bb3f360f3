package com.ruoyi.framework.task;

import java.util.Collection;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.framework.web.service.RoleValidityService;
import com.ruoyi.system.service.ISysRoleService;

/**
 * 过期角色清理定时任务
 * 专门用于清理角色已过期的用户登录状态
 * 
 * <AUTHOR>
 */
@EnableScheduling
@Component
public class ExpiredRoleCleanupTask
{
    private static final Logger log = LoggerFactory.getLogger(ExpiredRoleCleanupTask.class);
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private RoleValidityService roleValidityService;
    
    /**
     * 每10分钟执行一次，清理角色已过期的用户登录状态
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void cleanupExpiredRoleUsers()
    {
        log.info("开始执行过期角色用户清理任务");
        
        try
        {
            // 获取所有在线用户
            Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
            int cleanupCount = 0;
            
            for (String key : keys)
            {
                try
                {
                    LoginUser loginUser = redisCache.getCacheObject(key);
                    if (loginUser != null)
                    {
                        // 检查用户角色是否过期
                        boolean cleaned = checkAndCleanupExpiredUser(loginUser);
                        if (cleaned)
                        {
                            cleanupCount++;
                        }
                    }
                }
                catch (Exception e)
                {
                    log.error("检查用户角色过期状态失败，key: {}, 错误信息: {}", key, e.getMessage());
                }
            }
            
            log.info("过期角色用户清理任务完成，共清理 {} 个用户", cleanupCount);
        }
        catch (Exception e)
        {
            log.error("过期角色用户清理任务执行失败", e);
        }
    }
    
    /**
     * 检查并清理角色过期的用户
     *
     * @param loginUser 登录用户
     * @return 是否清理了用户
     */
    private boolean checkAndCleanupExpiredUser(LoginUser loginUser)
    {
        try
        {
            SysUser user = loginUser.getUser();
            if (user == null || user.isAdmin())
            {
                return false;
            }

            // 使用统一的角色有效性检查服务
            if (!roleValidityService.hasValidRoles(user))
            {
                List<SysRole> currentRoles = user.getRoles();
                if (currentRoles != null && !currentRoles.isEmpty())
                {
                    log.info("用户 {} 的所有角色已过期，清除登录状态", user.getUserName());
                    tokenService.delLoginUser(loginUser.getToken());
                    return true;
                }
            }

            return false;
        }
        catch (Exception e)
        {
            log.error("检查用户 {} 角色过期状态失败", loginUser.getUsername(), e);
            return false;
        }
    }
}
