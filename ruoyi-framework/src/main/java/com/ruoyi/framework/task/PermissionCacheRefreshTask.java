package com.ruoyi.framework.task;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.framework.web.service.RoleValidityService;
import com.ruoyi.system.service.ISysRoleService;

/**
 * 权限缓存刷新定时任务
 * 
 * <AUTHOR>
 */
@EnableScheduling
@Component
public class PermissionCacheRefreshTask
{
    private static final Logger log = LoggerFactory.getLogger(PermissionCacheRefreshTask.class);
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private ISysRoleService roleService;
    
    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private RoleValidityService roleValidityService;
    
    /**
     * 每5分钟执行一次，清理过期权限缓存
     * 由于现在在每次请求时都会检查角色有效期，可以适当降低定时任务的执行频率
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void refreshPermissionCache()
    {
        log.info("开始执行权限缓存刷新任务");
        
        try
        {
            // 获取所有在线用户
            Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
            int refreshCount = 0;
            
            for (String key : keys)
            {
                try
                {
                    LoginUser loginUser = redisCache.getCacheObject(key);
                    if (loginUser != null)
                    {
                        // 重新加载用户权限
                        boolean refreshed = refreshUserPermission(loginUser);
                        if (refreshed)
                        {
                            refreshCount++;
                        }
                    }
                }
                catch (Exception e)
                {
                    log.error("刷新用户权限失败，key: {}, 错误信息: {}", key, e.getMessage());
                }
            }
            
            log.info("权限缓存刷新任务完成，共刷新 {} 个用户的权限", refreshCount);
        }
        catch (Exception e)
        {
            log.error("权限缓存刷新任务执行失败", e);
        }
    }
    
    /**
     * 刷新用户权限
     *
     * @param loginUser 登录用户
     * @return 是否刷新成功
     */
    private boolean refreshUserPermission(LoginUser loginUser)
    {
        try
        {
            SysUser user = loginUser.getUser();
            if (user != null && !user.isAdmin())
            {
                // 获取最新的有效角色列表
                List<SysRole> validRoles = roleService.selectValidRolesByUserId(user.getUserId());
                List<SysRole> currentRoles = user.getRoles();

                // 使用统一的角色有效性检查服务
                if (!roleValidityService.hasValidRoles(user))
                {
                    if (currentRoles != null && !currentRoles.isEmpty())
                    {
                        log.info("用户 {} 的所有角色已过期，清除登录状态", user.getUserName());
                        tokenService.delLoginUser(loginUser.getToken());
                        return true;
                    }
                }

                // 检查角色是否有变化
                if (roleValidityService.isRolesChanged(currentRoles, validRoles))
                {
                    log.info("用户 {} 的角色权限发生变化，正在更新缓存", user.getUserName());

                    // 更新用户角色
                    user.setRoles(validRoles);

                    // 获取最新的权限列表
                    Set<String> permissions = permissionService.getMenuPermission(user);
                    loginUser.setPermissions(permissions);

                    // 更新缓存
                    tokenService.setLoginUser(loginUser);

                    return true;
                }
            }
            return false;
        }
        catch (Exception e)
        {
            log.error("刷新用户 {} 权限失败", loginUser.getUsername(), e);
            return false;
        }
    }
    
    /**
     * 检查角色是否有变化
     * 
     * @param currentRoles 当前角色列表
     * @param validRoles 有效角色列表
     * @return 是否有变化
     */
    private boolean isRolesChanged(List<SysRole> currentRoles, List<SysRole> validRoles)
    {
        if (currentRoles == null && validRoles == null)
        {
            return false;
        }
        
        if (currentRoles == null || validRoles == null)
        {
            return true;
        }
        
        if (currentRoles.size() != validRoles.size())
        {
            return true;
        }
        
        // 检查角色ID是否一致
        for (SysRole currentRole : currentRoles)
        {
            boolean found = false;
            for (SysRole validRole : validRoles)
            {
                if (currentRole.getRoleId().equals(validRole.getRoleId()))
                {
                    found = true;
                    break;
                }
            }
            if (!found)
            {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 手动刷新指定用户的权限缓存
     * 
     * @param userId 用户ID
     */
    public void refreshUserPermissionByUserId(Long userId)
    {
        log.info("手动刷新用户 {} 的权限缓存", userId);
        
        try
        {
            // 获取所有在线用户
            Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
            
            for (String key : keys)
            {
                LoginUser loginUser = redisCache.getCacheObject(key);
                if (loginUser != null && loginUser.getUserId().equals(userId))
                {
                    refreshUserPermission(loginUser);
                    log.info("用户 {} 权限缓存刷新完成", userId);
                    return;
                }
            }
            
            log.info("用户 {} 当前不在线，无需刷新权限缓存", userId);
        }
        catch (Exception e)
        {
            log.error("手动刷新用户 {} 权限缓存失败", userId, e);
        }
    }
}
