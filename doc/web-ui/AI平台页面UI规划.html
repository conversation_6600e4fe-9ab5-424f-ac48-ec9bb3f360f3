<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8">
<title>AI 模型转换平台</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<script src="https://cdn.tailwindcss.com"></script>
<!-- 添加 Markdown 渲染库 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet">
<script>
tailwind.config = {
theme: {
extend: {
colors: {
primary: '#3B82F6',
secondary: '#6B7280'
},
borderRadius: {
'none': '0px',
'sm': '2px',
DEFAULT: '4px',
'md': '8px',
'lg': '12px',
'xl': '16px',
'2xl': '20px',
'3xl': '24px',
'full': '9999px',
'button': '4px'
}
}
}
}
</script>
<style>
.nav-item {
display: flex;
align-items: center;
padding: 10px 24px;
color: #4B5563;
transition: all 0.3s;
cursor: pointer;
text-decoration: none;
font-size: 14px;
}
.nav-item:hover {
background: #EFF6FF;
color: #3B82F6;
}
.nav-item.active {
background: #EFF6FF;
color: #3B82F6;
border-right: 3px solid #3B82F6;
}
.nav-item i {
width: 16px;
height: 16px;
display: flex;
justify-content: center;
align-items: center;
margin-right: 12px;
}
.upload-zone {
border: 2px dashed #E5E7EB;
border-radius: 8px;
padding: 20px;
text-align: center;
transition: all 0.3s;
}
.upload-zone:hover {
border-color: #3B82F6;
}
input[type="file"] {
display: none;
}
.line-clamp-2 {
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
overflow: hidden;
}
select {
appearance: none;
background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
background-position: right 0.5rem center;
background-repeat: no-repeat;
background-size: 1.5em 1.5em;
padding-right: 2.5rem;
}
body {
min-height: 1024px;
}
.card {
background: white;
border-radius: 8px;
box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
transition: all 0.3s;
}
.card:hover {
box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
/* Markdown 样式 */
.markdown-body {
    font-size: 16px;
    line-height: 1.6;
    color: #374151;
}
.markdown-body h1 {
    font-size: 2em;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
    color: #111827;
}
.markdown-body h2 {
    font-size: 1.5em;
    margin-top: 1.25em;
    margin-bottom: 0.5em;
    font-weight: 600;
    color: #111827;
}
.markdown-body h3 {
    font-size: 1.25em;
    margin-top: 1em;
    margin-bottom: 0.5em;
    font-weight: 600;
    color: #111827;
}
.markdown-body p {
    margin-bottom: 1em;
}
.markdown-body ul, .markdown-body ol {
    margin-bottom: 1em;
    padding-left: 2em;
}
.markdown-body li {
    margin-bottom: 0.5em;
}
.markdown-body code {
    background-color: #F3F4F6;
    padding: 0.2em 0.4em;
    border-radius: 0.25em;
    font-size: 0.875em;
    color: #DC2626;
}
.markdown-body pre {
    background-color: #F3F4F6;
    padding: 1em;
    border-radius: 0.5em;
    margin-bottom: 1em;
    overflow-x: auto;
}
.markdown-body pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}
.markdown-body blockquote {
    border-left: 4px solid #E5E7EB;
    padding-left: 1em;
    margin-bottom: 1em;
    color: #6B7280;
}
.markdown-body table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1em;
}
.markdown-body th, .markdown-body td {
    border: 1px solid #E5E7EB;
    padding: 0.5em 1em;
}
.markdown-body th {
    background-color: #F9FAFB;
    font-weight: 600;
}
.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(to bottom, #ffffff, #f9fafb);
    border-bottom: 1px solid #e5e7eb;
}

.logo-container img {
    height: 48px;
    width: auto;
    object-fit: contain;
    margin-bottom: 1.25rem;
    transition: transform 0.3s ease;
}

.logo-container img:hover {
    transform: scale(1.05);
}

.logo-container h1 {
    font-size: 1.65rem;
    font-weight: 700;
    color: #111827;
    text-align: center;
    line-height: 1.4;
    letter-spacing: -0.025em;
    /* padding: 0.25rem 0; */
}

.image-preview-container {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #E5E7EB;
}

.image-preview-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 8px;
    opacity: 0;
    transition: opacity 0.2s;
}

.image-preview-container:hover .image-preview-overlay {
    opacity: 1;
}

.image-preview-name {
    color: white;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.image-preview-size {
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
}

.image-preview-delete {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #EF4444;
    transition: all 0.2s;
}

.image-preview-delete:hover {
    background: #EF4444;
    color: white;
}
</style>
</head>
<body class="bg-gray-50">
<div class="flex w-[1440px] mx-auto min-h-screen">
    <!-- 侧边栏导航 -->
    <div class="w-64 bg-white border-r border-gray-200 h-screen fixed">
        <!-- Logo 和标题区域 -->
        <div class="logo-container">
            <img src="logo.png" alt="AI Model Platform Logo" class="shadow-sm">
            <h1 class="platform-title">AI 模型转换平台</h1>
        </div>
        <nav class="py-4">
        <div class="mb-4">
        <div class="px-6 mb-2 text-xs font-medium text-gray-400 uppercase tracking-wider">通用功能</div>
        <a href="#" class="nav-item active">
        <i class="fas fa-exchange-alt"></i>
        <span>模型转换</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-cubes"></i>
        <span>Model Zoo</span>
        </a>
        </div>
        <div class="mb-4">
        <div class="px-6 mb-2 text-xs font-medium text-gray-400 uppercase tracking-wider">资源中心</div>
        <a href="#" class="nav-item">
        <i class="fas fa-download"></i>
        <span>LFNN 下载</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-book"></i>
        <span>LFNN 使用文档</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-code"></i>
        <span>示例工程</span>
        </a>
        </div>
        <div class="mb-4">
        <div class="px-6 mb-2 text-xs font-medium text-gray-400 uppercase tracking-wider">系统管理</div>
        <a href="#" class="nav-item">
        <i class="fas fa-users"></i>
        <span>用户管理</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-user-shield"></i>
        <span>角色权限</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-project-diagram"></i>
        <span>模型管理</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-folder-open"></i>
        <span>文件管理</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
        </a>
        </div>
        <div class="mb-4">
        <div class="px-6 mb-2 text-xs font-medium text-gray-400 uppercase tracking-wider">开发者社区</div>
        <a href="#" class="nav-item">
        <i class="fas fa-laptop-code"></i>
        <span>AI 编程</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-comments"></i>
        <span>开发论坛</span>
        </a>
        <a href="#" class="nav-item">
        <i class="fas fa-robot"></i>
        <span>AI Agents</span>
        </a>
        </div>
        </nav>
    </div>

    <!-- 所有页面容器的父容器 -->
    <div class="flex-1 ml-64">
        <!-- 模型转换页面 -->
        <div class="p-8" id="convertPage">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">在线模型转换</h2>
                <p class="text-gray-600 mt-2">快速转换您的深度学习模型，支持多种框架格式</p>
            </div>
            <!-- 原始模型文件上传 -->
            <div class="card mb-6 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">原始模型文件</h3>
                <div class="upload-zone">
                    <label for="modelFile" class="cursor-pointer">
                        <i class="fas fa-inbox text-4xl text-gray-400 mb-3"></i>
                        <p class="text-gray-600">点击或拖拽文件到此处上传</p>
                        <p class="text-sm text-gray-500 mt-2">支持格式：onnx, caffemodel+prototxt.pd 等格式</p>
                        <input type="file" id="modelFile" accept=".onnx,.caffemodel,.prototxt.pd">
                    </label>
                </div>
                <div class="flex items-center justify-between mt-4">
                    <div class="flex items-center">
                        <span class="text-gray-600 mr-4">示例原始模型下载：</span>
                        <select class="w-32 border border-gray-300 rounded-button p-2">
                            <option value="ResNet50">ResNet50</option>
                            <option value="VGG16">VGG16</option>
                            <option value="MobileNet">MobileNet</option>
                        </select>
                    </div>
                    <button class="bg-primary text-white px-4 py-2 !rounded-button hover:bg-blue-600 transition-colors whitespace-nowrap">
                        下载
                    </button>
                </div>
            </div>
            <!-- 配置文件上传 -->
            <div class="card mb-6 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">模型转换的配置文件 (CSV)</h3>
                <div class="upload-zone">
                    <label for="configFile" class="cursor-pointer">
                        <i class="fas fa-file-excel text-4xl text-gray-400 mb-3"></i>
                        <p class="text-gray-600">点击或拖拽配置文件到此处上传</p>
                        <input type="file" id="configFile" accept=".csv">
                    </label>
                </div>
                <div class="flex items-center justify-between mt-4">
                    <div class="flex items-center">
                        <span class="text-gray-600 mr-4">示例配置文件下载：</span>
                        <select class="w-32 border border-gray-300 rounded-button p-2">
                            <option value="ResNet50">ResNet50</option>
                            <option value="VGG16">VGG16</option>
                            <option value="MobileNet">MobileNet</option>
                        </select>
                    </div>
                    <button class="bg-primary text-white px-4 py-2 !rounded-button hover:bg-blue-600 transition-colors whitespace-nowrap">
                        下载
                    </button>
                </div>
            </div>
            <!-- 图片上传 -->
            <div class="card mb-6 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">模型量化图片</h3>
                <div class="upload-zone mb-4">
                    <label for="imageFile" class="cursor-pointer">
                        <i class="fas fa-images text-4xl text-gray-400 mb-3"></i>
                        <p class="text-gray-600">点击或拖拽图片文件到此处上传</p>
                        <p class="text-sm text-gray-500 mt-2">
                            请上传10～50张具有代表性的实际场景图片<br>
                            支持 jpg、png、bmp 格式，每张图片大小不超过 5MB
                        </p>
                        <input type="file" id="imageFile" accept="image/*" multiple>
                    </label>
                </div>
                
                <!-- 已上传图片预览区域 -->
                <div class="mt-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="text-gray-500">已上传 <span id="uploadedCount">0</span> 张图片</div>
                        <div class="flex items-center gap-4">
                            <button id="clearImages" class="text-gray-600 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-trash-alt mr-2"></i>清空图片
                            </button>
                        </div>
                    </div>
                    
                    <!-- 图片预览网格 -->
                    <div id="imagePreviewGrid" class="grid grid-cols-6 gap-4">
                        <!-- 预览图片将动态插入这里 -->
                    </div>
                </div>
            </div>
            <!-- 下载区域 -->
            <div class="card mb-6 p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button id="startConversion" class="bg-primary text-white px-4 py-2 !rounded-button hover:bg-blue-600 transition-colors whitespace-nowrap flex items-center">
                            <span>开始转换</span>
                        </button>
                        <div id="loadingSpinner" class="hidden">
                            <i class="fas fa-circle-notch fa-spin text-primary mr-2"></i>
                            <span class="text-gray-600">转换中...</span>
                        </div>
                        <button class="bg-primary text-white px-4 py-2 !rounded-button hover:bg-blue-600 transition-colors whitespace-nowrap">
                            模型下载
                        </button>
                        <button id="convertedDownload" class="bg-gray-300 text-white px-4 py-2 !rounded-button cursor-not-allowed whitespace-nowrap">
                            转换后模型下载
                        </button>
                    </div>
                    <div class="flex items-center">
                        <span class="text-gray-600 mr-4">模型 md5 值：</span>
                        <input type="text" class="border border-gray-300 rounded-button px-3 py-2 w-64" placeholder="请输入模型 md5 值">
                    </div>
                </div>
            </div>
            <!-- 日志区域 -->
            <div class="card p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">模型转换日志</h3>
                <div class="bg-gray-50 rounded-lg p-4 h-48 overflow-auto font-mono text-sm">
                    <p class="text-gray-600">[INFO] 开始加载模型文件...</p>
                    <p class="text-gray-600">[INFO] 模型文件加载完成</p>
                    <p class="text-gray-600">[INFO] 开始模型转换...</p>
                </div>
            </div>
        </div>

        <!-- Model Zoo 页面 -->
        <div class="p-8 hidden" id="modelZooPage">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Model Zoo</h2>
                <p class="text-gray-600 mt-2">浏览和下载预训练模型，快速开始您的深度学习项目</p>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="card mb-6 p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 flex-1">
                        <div class="relative flex-1 max-w-md">
                            <input type="text" placeholder="搜索模型..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        <select class="border border-gray-300 rounded-button p-2 w-40">
                            <option value="">所有类型</option>
                            <option value="图像分类">图像分类</option>
                            <option value="目标检测">目标检测</option>
                            <option value="语义分割">语义分割</option>
                            <option value="自然语言处理">自然语言处理</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-primary text-white px-4 py-2 rounded-button hover:bg-blue-600 transition-colors">
                            <i class="fas fa-filter mr-2"></i>筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- 模型列表 -->
            <div class="grid grid-cols-1 gap-6">
                <!-- 表头 -->
                <div class="card p-4 bg-gray-50">
                    <div class="grid grid-cols-5 gap-4 text-sm font-medium text-gray-600">
                        <div>模型分类</div>
                        <div>模型名称</div>
                        <div class="text-center">推理示例代码</div>
                        <div class="text-center">推理性能(ms)</div>
                        <div class="text-center">推理精度mAP</div>
                    </div>
                </div>

                <!-- 模型项 -->
                <div class="card p-4 hover:shadow-lg transition-shadow">
                    <div class="grid grid-cols-5 gap-4 items-center">
                        <div class="text-gray-600">图像分类</div>
                        <div class="font-medium text-gray-900">ResNet50</div>
                        <div class="text-center">
                            <button class="bg-primary text-white px-4 py-1 rounded-button text-sm hover:bg-blue-600 transition-colors">
                                下载
                            </button>
                        </div>
                        <div class="text-center text-gray-600">100</div>
                        <div class="text-center text-gray-600">90%</div>
                    </div>
                </div>

                <div class="card p-4 hover:shadow-lg transition-shadow">
                    <div class="grid grid-cols-5 gap-4 items-center">
                        <div class="text-gray-600">图像分类</div>
                        <div class="font-medium text-gray-900">VGG16</div>
                        <div class="text-center">
                            <button class="bg-primary text-white px-4 py-1 rounded-button text-sm hover:bg-blue-600 transition-colors">
                                下载
                            </button>
                        </div>
                        <div class="text-center text-gray-600">200</div>
                        <div class="text-center text-gray-600">85%</div>
                    </div>
                </div>

                <div class="card p-4 hover:shadow-lg transition-shadow">
                    <div class="grid grid-cols-5 gap-4 items-center">
                        <div class="text-gray-600">目标检测</div>
                        <div class="font-medium text-gray-900">YOLOv5</div>
                        <div class="text-center">
                            <button class="bg-primary text-white px-4 py-1 rounded-button text-sm hover:bg-blue-600 transition-colors">
                                下载
                            </button>
                        </div>
                        <div class="text-center text-gray-600">150</div>
                        <div class="text-center text-gray-600">92%</div>
                    </div>
                </div>

                <div class="card p-4 hover:shadow-lg transition-shadow">
                    <div class="grid grid-cols-5 gap-4 items-center">
                        <div class="text-gray-600">目标检测</div>
                        <div class="font-medium text-gray-900">Faster R-CNN</div>
                        <div class="text-center">
                            <button class="bg-primary text-white px-4 py-1 rounded-button text-sm hover:bg-blue-600 transition-colors">
                                下载
                            </button>
                        </div>
                        <div class="text-center text-gray-600">250</div>
                        <div class="text-center text-gray-600">88%</div>
                    </div>
                </div>

                <div class="card p-4 hover:shadow-lg transition-shadow">
                    <div class="grid grid-cols-5 gap-4 items-center">
                        <div class="text-gray-600">语义分割</div>
                        <div class="font-medium text-gray-900">U-Net</div>
                        <div class="text-center">
                            <button class="bg-primary text-white px-4 py-1 rounded-button text-sm hover:bg-blue-600 transition-colors">
                                下载
                            </button>
                        </div>
                        <div class="text-center text-gray-600">220</div>
                        <div class="text-center text-gray-600">86%</div>
                    </div>
                </div>

                <div class="card p-4 hover:shadow-lg transition-shadow">
                    <div class="grid grid-cols-5 gap-4 items-center">
                        <div class="text-gray-600">语义分割</div>
                        <div class="font-medium text-gray-900">Mask R-CNN</div>
                        <div class="text-center">
                            <button class="bg-primary text-white px-4 py-1 rounded-button text-sm hover:bg-blue-600 transition-colors">
                                下载
                            </button>
                        </div>
                        <div class="text-center text-gray-600">300</div>
                        <div class="text-center text-gray-600">83%</div>
                    </div>
                </div>

                <div class="card p-4 hover:shadow-lg transition-shadow">
                    <div class="grid grid-cols-5 gap-4 items-center">
                        <div class="text-gray-600">自然语言处理</div>
                        <div class="font-medium text-gray-900">BERT</div>
                        <div class="text-center">
                            <button class="bg-primary text-white px-4 py-1 rounded-button text-sm hover:bg-blue-600 transition-colors">
                                下载
                            </button>
                        </div>
                        <div class="text-center text-gray-600">280</div>
                        <div class="text-center text-gray-600">89%</div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="flex justify-center mt-8">
                <nav class="flex items-center space-x-2">
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-2 rounded-button bg-primary text-white">1</button>
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">2</button>
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">3</button>
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </nav>
            </div>
        </div>

        <!-- LFNN 下载页面 -->
        <div class="p-8 hidden" id="lfnnDownloadPage">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">LFNN 版本下载</h2>
                <p class="text-gray-600 mt-2">获取最新的 LFNN 版本，开始您的深度学习模型转换之旅</p>
            </div>

            <!-- 版本列表 -->
            <div class="space-y-6">
                <!-- 最新版本 -->
                <div class="card p-6 border-l-4 border-primary">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3">
                                <h3 class="text-xl font-bold text-gray-900">LFNN-V1.5.0-20240209-R2</h3>
                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-primary rounded-full">最新版本</span>
                            </div>
                            <p class="mt-3 text-gray-600">修复了部分已知的兼容性问题。优化了模型推理的性能。增加了新的模型支持。改进了用户界面的交互体验。</p>
                            <div class="mt-4 flex items-center space-x-6 text-sm text-gray-500">
                                <div class="flex items-center">
                                    <i class="far fa-calendar-alt mr-2"></i>
                                    <span>更新时间：2025-02-09</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-download mr-2"></i>
                                    <span>下载次数：500</span>
                                </div>
                            </div>
                        </div>
                        <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-blue-600 transition-colors flex items-center">
                            <i class="fas fa-download mr-2"></i>
                            下载
                        </button>
                    </div>
                </div>

                <!-- 历史版本 -->
                <div class="card p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3">
                                <h3 class="text-xl font-bold text-gray-900">LFNN-V1.4.0-20231115-R1</h3>
                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">历史版本</span>
                            </div>
                            <p class="mt-3 text-gray-600">初始版本发布。支持基本的模型转换功能。提供了简单的用户操作界面。</p>
                            <div class="mt-4 flex items-center space-x-6 text-sm text-gray-500">
                                <div class="flex items-center">
                                    <i class="far fa-calendar-alt mr-2"></i>
                                    <span>更新时间：2024-11-15</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-download mr-2"></i>
                                    <span>下载次数：300</span>
                                </div>
                            </div>
                        </div>
                        <button class="bg-white border border-gray-300 text-gray-700 px-6 py-2 rounded-button hover:bg-gray-50 transition-colors flex items-center">
                            <i class="fas fa-download mr-2"></i>
                            下载
                        </button>
                    </div>
                </div>
            </div>

            <!-- 版本说明 -->
            <div class="mt-8">
                <div class="card p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">版本说明</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-4">
                            <div class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">版本命名规则</h4>
                                <p class="mt-1 text-gray-600">LFNN-V[主版本号].[次版本号].[修订号]-[发布日期]-R[发布次数]</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-laptop-code"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">系统要求</h4>
                                <p class="mt-1 text-gray-600">支持 Windows 10/11、Linux、MacOS 等主流操作系统</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-book"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">相关文档</h4>
                                <div class="mt-2 flex space-x-4">
                                    <a href="#" class="text-primary hover:text-blue-600 flex items-center">
                                        <i class="fas fa-file-alt mr-2"></i>
                                        使用文档
                                    </a>
                                    <a href="#" class="text-primary hover:text-blue-600 flex items-center">
                                        <i class="fas fa-code mr-2"></i>
                                        API 文档
                                    </a>
                                    <a href="#" class="text-primary hover:text-blue-600 flex items-center">
                                        <i class="fas fa-question-circle mr-2"></i>
                                        常见问题
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- LFNN 使用文档页面 -->
        <div class="p-8 hidden" id="lfnnDocPage">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">LFNN 使用文档</h2>
                <p class="text-gray-600 mt-2">全面的 LFNN 使用指南和技术文档</p>
            </div>

            <div class="flex gap-6">
                <!-- 左侧导航 -->
                <div class="w-64 flex-shrink-0">
                    <div class="card p-4">
                        <div class="mb-4">
                            <div class="relative">
                                <input type="text" placeholder="搜索文档..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <nav class="space-y-1">
                            <div class="mb-4">
                                <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">快速上手文档</h3>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button active-doc">
                                    快速上手
                                </a>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    基础概念
                                </a>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    安装指南
                                </a>
                            </div>

                            <div class="mb-4">
                                <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">LFNN规格书</h3>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    API 参考
                                </a>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    配置说明
                                </a>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    性能指标
                                </a>
                            </div>

                            <div class="mb-4">
                                <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">进阶指南</h3>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    最佳实践
                                </a>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    故障排除
                                </a>
                                <a href="#" class="block px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-primary rounded-button">
                                    常见问题
                                </a>
                            </div>
                        </nav>
                    </div>
                </div>

                <!-- 右侧文档内容 -->
                <div class="flex-1">
                    <div class="card p-8">
                        <!-- 文档导航路径 -->
                        <div class="flex items-center text-sm text-gray-500 mb-6">
                            <a href="#" class="hover:text-primary">文档首页</a>
                            <i class="fas fa-chevron-right mx-2 text-gray-400"></i>
                            <a href="#" class="hover:text-primary">快速上手文档</a>
                            <i class="fas fa-chevron-right mx-2 text-gray-400"></i>
                            <span class="text-gray-900">快速上手</span>
                        </div>

                        <!-- Markdown 内容区域 -->
                        <div class="markdown-body" id="markdownContent">
                            <!-- 这里是示例内容，实际内容将由后端返回的 Markdown 渲染 -->
                            <div id="documentContent"></div>
                        </div>

                        <!-- 文档导航按钮 -->
                        <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
                            <button class="flex items-center text-gray-600 hover:text-primary">
                                <i class="fas fa-arrow-left mr-2"></i>
                                上一篇：基础概念
                            </button>
                            <button class="flex items-center text-gray-600 hover:text-primary">
                                下一篇：安装指南
                                <i class="fas fa-arrow-right ml-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 示例训练工程下载页面 -->
        <div class="p-8 hidden" id="exampleProjectPage">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">示例训练工程下载</h2>
                <p class="text-gray-600 mt-2">下载并体验完整的训练工程示例，快速开始您的模型训练之旅</p>
            </div>

            <!-- 分类筛选区 -->
            <div class="card mb-6 p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 flex-1">
                        <div class="relative flex-1 max-w-md">
                            <input type="text" placeholder="搜索示例工程..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        <select class="border border-gray-300 rounded-button p-2 w-40">
                            <option value="">所有分类</option>
                            <option value="yolov8s">yolov8s</option>
                            <option value="yolov5s">yolov5s</option>
                            <option value="mssd">mssd</option>
                            <option value="mobilenet">mobilenet</option>
                            <option value="openpose">openpose</option>
                            <option value="检测">检测</option>
                            <option value="追踪">追踪</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="bg-primary text-white px-4 py-2 rounded-button hover:bg-blue-600 transition-colors">
                            <i class="fas fa-filter mr-2"></i>筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- 示例工程列表 -->
            <div class="grid grid-cols-1 gap-6">
                <!-- yolov8s -->
                <div class="card hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <h3 class="text-xl font-bold text-gray-900">yolov8s</h3>
                                    <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-primary rounded-full">最新</span>
                                </div>
                                <p class="text-gray-600 mb-4">YOLOv8s 是一种轻量级的目标检测模型，具有较强的检测精度和速度。</p>
                                <div class="flex items-center space-x-6 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <i class="far fa-calendar-alt mr-2"></i>
                                        <span>更新时间：2025-01-01</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-download mr-2"></i>
                                        <span>下载次数：1000</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-tag mr-2"></i>
                                        <span>分类：目标检测</span>
                                    </div>
                                </div>
                            </div>
                            <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-blue-600 transition-colors flex items-center">
                                <i class="fas fa-download mr-2"></i>
                                下载
                            </button>
                        </div>
                    </div>
                </div>

                <!-- yolov5s -->
                <div class="card hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <h3 class="text-xl font-bold text-gray-900">yolov5s</h3>
                                </div>
                                <p class="text-gray-600 mb-4">YOLOv5s 是经典的目标检测模型，在多个领域有广泛应用。</p>
                                <div class="flex items-center space-x-6 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <i class="far fa-calendar-alt mr-2"></i>
                                        <span>更新时间：2024-12-15</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-download mr-2"></i>
                                        <span>下载次数：2000</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-tag mr-2"></i>
                                        <span>分类：目标检测</span>
                                    </div>
                                </div>
                            </div>
                            <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-blue-600 transition-colors flex items-center">
                                <i class="fas fa-download mr-2"></i>
                                下载
                            </button>
                        </div>
                    </div>
                </div>

                <!-- mssd -->
                <div class="card hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <h3 class="text-xl font-bold text-gray-900">mssd</h3>
                                </div>
                                <p class="text-gray-600 mb-4">MSSD 是一种多尺度的目标检测模型，能够检测不同大小的目标。</p>
                                <div class="flex items-center space-x-6 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <i class="far fa-calendar-alt mr-2"></i>
                                        <span>更新时间：2024-11-20</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-download mr-2"></i>
                                        <span>下载次数：500</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-tag mr-2"></i>
                                        <span>分类：目标检测</span>
                                    </div>
                                </div>
                            </div>
                            <button class="bg-primary text-white px-6 py-2 rounded-button hover:bg-blue-600 transition-colors flex items-center">
                                <i class="fas fa-download mr-2"></i>
                                下载
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="flex justify-center mt-8">
                <nav class="flex items-center space-x-2">
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-2 rounded-button bg-primary text-white">1</button>
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">2</button>
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">3</button>
                    <button class="px-3 py-2 rounded-button border border-gray-300 text-gray-600 hover:bg-gray-50">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </nav>
            </div>
        </div>
    </div>
</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有导航项
    const navItems = document.querySelectorAll('.nav-item');
    
    // 获取所有页面
    const pages = {
        '模型转换': document.getElementById('convertPage'),
        'Model Zoo': document.getElementById('modelZooPage'),
        'LFNN 下载': document.getElementById('lfnnDownloadPage'),
        'LFNN 使用文档': document.getElementById('lfnnDocPage'),
        '示例工程': document.getElementById('exampleProjectPage')
    };
    
    // 隐藏所有页面
    function hideAllPages() {
        Object.values(pages).forEach(page => {
            if (page) page.classList.add('hidden');
        });
    }
    
    // 显示指定页面
    function showPage(pageName) {
        hideAllPages();
        const page = pages[pageName];
        if (page) {
            page.classList.remove('hidden');
            // 如果是文档页面，加载文档内容
            if (pageName === 'LFNN 使用文档') {
                loadDocumentContent();
            }
        }
    }
    
    // 为每个导航项添加点击事件
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault(); // 阻止默认的链接行为
            
            // 移除所有导航项的激活状态
            navItems.forEach(nav => nav.classList.remove('active', 'bg-primary', 'text-white'));
            
            // 添加当前项的激活状态
            this.classList.add('active', 'bg-primary', 'text-white');
            
            // 获取导航项文本并显示对应页面
            const pageName = this.textContent.trim();
            showPage(pageName);
        });
    });

    // 添加文档链接的点击事件处理
    const docLinks = document.querySelectorAll('a[href="#"]');
    docLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            if (link.textContent.trim() === '使用文档') {
                // 找到对应的导航项并触发点击
                navItems.forEach(item => {
                    if (item.textContent.trim() === 'LFNN 使用文档') {
                        item.click();
                    }
                });
            }
        });
    });
    
    // 默认显示模型转换页面
    showPage('模型转换');
    navItems[0].classList.add('active', 'bg-primary', 'text-white');

    // 文件上传相关功能
    const modelFile = document.getElementById('modelFile');
    const configFile = document.getElementById('configFile');
    const imageFile = document.getElementById('imageFile');
    const startConversion = document.getElementById('startConversion');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const convertedDownload = document.getElementById('convertedDownload');

    // 文件选择处理
    function handleFileSelect(event) {
        const files = event.target.files;
        if (files.length > 0) {
            console.log(`Selected ${files.length} file(s)`);
            // 启用开始转换按钮
            if (modelFile.files.length > 0 && configFile.files.length > 0) {
                startConversion.disabled = false;
            }
        }
    }

    // 图片上传相关功能
    const uploadedCount = document.getElementById('uploadedCount');
    const clearImages = document.getElementById('clearImages');
    const imagePreviewGrid = document.getElementById('imagePreviewGrid');
    const uploadedImages = new Set();

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function createImagePreview(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const container = document.createElement('div');
            container.className = 'image-preview-container';
            container.innerHTML = `
                <img src="${e.target.result}" alt="${file.name}">
                <div class="image-preview-overlay">
                    <div class="image-preview-name">${file.name}</div>
                    <div class="image-preview-size">${formatFileSize(file.size)}</div>
                    <button class="image-preview-delete">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // 添加删除功能
            const deleteBtn = container.querySelector('.image-preview-delete');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                uploadedImages.delete(file);
                container.remove();
                updateImageCount();
            });

            imagePreviewGrid.appendChild(container);
            uploadedImages.add(file);
            updateImageCount();
        };
        reader.readAsDataURL(file);
    }

    function updateImageCount() {
        const count = uploadedImages.size;
        uploadedCount.textContent = count;
        clearImages.disabled = count === 0;
        
        // 更新上传区域的提示
        const uploadZone = document.querySelector('.upload-zone');
        if (count >= 50) {
            uploadZone.style.opacity = '0.5';
            uploadZone.style.pointerEvents = 'none';
        } else {
            uploadZone.style.opacity = '1';
            uploadZone.style.pointerEvents = 'auto';
        }
    }

    function handleImageSelect(event) {
        const files = Array.from(event.target.files);
        
        // 检查文件数量限制
        if (uploadedImages.size + files.length > 50) {
            alert('最多只能上传50张图片');
            return;
        }

        // 检查每个文件的大小和类型
        files.forEach(file => {
            if (file.size > 5 * 1024 * 1024) {
                alert(`文件 ${file.name} 超过5MB限制`);
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                alert(`文件 ${file.name} 不是有效的图片格式`);
                return;
            }
            
            createImagePreview(file);
        });
    }

    // 清空所有图片
    clearImages.addEventListener('click', () => {
        imagePreviewGrid.innerHTML = '';
        uploadedImages.clear();
        updateImageCount();
    });

    // 文件拖放处理
    const imageDropZone = document.querySelector('.upload-zone');
    imageDropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        imageDropZone.classList.add('border-primary');
    });

    imageDropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        imageDropZone.classList.remove('border-primary');
    });

    imageDropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        imageDropZone.classList.remove('border-primary');
        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        
        if (files.length === 0) {
            alert('请上传图片文件');
            return;
        }
        
        if (uploadedImages.size + files.length > 50) {
            alert('最多只能上传50张图片');
            return;
        }
        
        files.forEach(file => {
            if (file.size <= 5 * 1024 * 1024) {
                createImagePreview(file);
            } else {
                alert(`文件 ${file.name} 超过5MB限制`);
            }
        });
    });

    // 添加文件选择事件监听器
    modelFile.addEventListener('change', handleFileSelect);
    configFile.addEventListener('change', handleFileSelect);
    imageFile.addEventListener('change', handleImageSelect);

    // 开始转换按钮点击事件
    startConversion.addEventListener('click', function() {
        loadingSpinner.classList.remove('hidden');
        startConversion.disabled = true;
        // 模拟转换过程
        setTimeout(() => {
            loadingSpinner.classList.add('hidden');
            startConversion.disabled = false;
            convertedDownload.classList.remove('bg-gray-300', 'cursor-not-allowed');
            convertedDownload.classList.add('bg-primary', 'hover:bg-blue-600');
        }, 3000);
    });

    // 文档相关功能
    function loadDocumentContent() {
        // 模拟从后端获取的 Markdown 内容
        const markdownContent = `
# 快速上手

这是快速上手文档的部分内容。在这个文档中，你可以了解如何快速开始使用 LFNN。首先，你需要安装必要的依赖项......

## 安装步骤

1. 下载最新版本的 LFNN
2. 安装必要的依赖
3. 配置环境变量

## 基本使用

\`\`\`python
import lfnn

# 初始化模型
model = lfnn.Model()

# 加载配置
model.load_config("config.json")

# 开始转换
result = model.convert()
\`\`\`

## 注意事项

> 请确保您的环境满足以下要求：
> - Python 3.7+
> - CUDA 11.0+ (GPU 版本)
> - RAM 8GB+

更多详细信息请参考[完整文档](#)。
        `;

        const documentContent = document.getElementById('documentContent');
        if (documentContent) {
            documentContent.innerHTML = marked.parse(markdownContent);
            // 代码高亮
            Prism.highlightAll();
        }
    }

    // 文档导航处理
    const docNavItems = document.querySelectorAll('.markdown-nav a');
    docNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            docNavItems.forEach(navItem => navItem.classList.remove('active-doc'));
            this.classList.add('active-doc');
            // 这里可以添加加载对应文档内容的逻辑
        });
    });
});
</script>
</body>
</html>
