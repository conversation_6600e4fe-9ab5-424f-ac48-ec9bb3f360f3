package com.ruoyi.common.utils;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.ruoyi.common.config.AliyunSmsConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 短信工具类
 * 
 * <AUTHOR>
 */
@Component
public class SmsUtils
{
    private static final Logger log = LoggerFactory.getLogger(SmsUtils.class);

    @Autowired
    private AliyunSmsConfig aliyunSmsConfig;

    /**
     * 发送短信验证码
     * 
     * @param phoneNumber 手机号码
     * @param code 验证码
     * @return 发送结果
     */
    public boolean sendSmsCode(String phoneNumber, String code)
    {
        try
        {
            // 创建配置
            Config config = new Config()
                    .setAccessKeyId(aliyunSmsConfig.getAccessKeyId())
                    .setAccessKeySecret(aliyunSmsConfig.getAccessKeySecret())
                    .setRegionId(aliyunSmsConfig.getRegionId())
                    .setEndpoint("dysmsapi.aliyuncs.com");

            // 创建客户端
            Client client = new Client(config);

            // 创建发送短信请求
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phoneNumber)
                    .setSignName(aliyunSmsConfig.getSignName())
                    .setTemplateCode(aliyunSmsConfig.getTemplateCode())
                    .setTemplateParam("{\"code\":\"" + code + "\"}");

            // 发送短信
            SendSmsResponse response = client.sendSms(sendSmsRequest);
            
            // 检查发送结果
            if ("OK".equals(response.getBody().getCode()))
            {
                log.info("短信发送成功，手机号：{}，验证码：{}", phoneNumber, code);
                return true;
            }
            else
            {
                log.error("短信发送失败，手机号：{}，错误代码：{}，错误信息：{}", 
                         phoneNumber, response.getBody().getCode(), response.getBody().getMessage());
                return false;
            }
        }
        catch (Exception e)
        {
            log.error("短信发送异常，手机号：{}，异常信息：{}", phoneNumber, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成6位数字验证码
     * 
     * @return 验证码
     */
    public String generateCode()
    {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }
}
