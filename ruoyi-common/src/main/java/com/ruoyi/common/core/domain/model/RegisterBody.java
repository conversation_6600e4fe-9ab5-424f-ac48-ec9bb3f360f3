package com.ruoyi.common.core.domain.model;

import java.util.List;

/**
 * 用户注册对象
 *
 * <AUTHOR>
 */
public class RegisterBody extends LoginBody
{
    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 短信验证码
     */
    private String smsCode;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户类型（personal-个人，university-高校，company-公司，business_evaluation-商业评估，business_contract-商业合同，admin-管理员）
     */
    private String userType;

    /**
     * 公司名称（企业用户）
     */
    private String companyName;

    /**
     * 公司官网（企业用户）
     */
    private String companyWebsite;

    /**
     * 合作合同号（商业合同用户）
     */
    private String contractNumber;

    /**
     * 确认密码
     */
    private String confirmPassword;

    /**
     * 用户协议同意状态
     */
    private Boolean agreement;

    /**
     * 上传文件标识符列表（用于关联上传的文件）
     */
    private List<String> fileIdentifiers;

    /**
     * 注册角色ID（根据用户类型自动分配）
     */
    private Long roleId;

    /**
     * 用户ID（注册成功后设置，用于文件关联）
     */
    private Long userId;

    public String getPhoneNumber()
    {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber)
    {
        this.phoneNumber = phoneNumber;
    }

    public String getSmsCode()
    {
        return smsCode;
    }

    public void setSmsCode(String smsCode)
    {
        this.smsCode = smsCode;
    }

    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public String getUserType()
    {
        return userType;
    }

    public void setUserType(String userType)
    {
        this.userType = userType;
    }

    public String getCompanyName()
    {
        return companyName;
    }

    public void setCompanyName(String companyName)
    {
        this.companyName = companyName;
    }

    public String getCompanyWebsite()
    {
        return companyWebsite;
    }

    public void setCompanyWebsite(String companyWebsite)
    {
        this.companyWebsite = companyWebsite;
    }

    public String getContractNumber()
    {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber)
    {
        this.contractNumber = contractNumber;
    }

    public String getConfirmPassword()
    {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword)
    {
        this.confirmPassword = confirmPassword;
    }

    public Boolean getAgreement()
    {
        return agreement;
    }

    public void setAgreement(Boolean agreement)
    {
        this.agreement = agreement;
    }

    public List<String> getFileIdentifiers()
    {
        return fileIdentifiers;
    }

    public void setFileIdentifiers(List<String> fileIdentifiers)
    {
        this.fileIdentifiers = fileIdentifiers;
    }

    public Long getRoleId()
    {
        return roleId;
    }

    public void setRoleId(Long roleId)
    {
        this.roleId = roleId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }
}
