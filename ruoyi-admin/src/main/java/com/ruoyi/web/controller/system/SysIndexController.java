package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.StringUtils;

/**
 * 首页
 *
 * <AUTHOR>
 */
@Controller
public class SysIndexController
{
    /** 系统基础配置 */
    @Autowired
    private RuoYiConfig ruoyiConfig;

    /**
     * 访问首页，提示语
     */
    @RequestMapping(value = {"/", })
    public String index()
    {
//        return StringUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", ruoyiConfig.getName(), ruoyiConfig.getVersion());
        return "forward:/index.html";
    }

    /**
     * 访问首页，提示语
     */
    @RequestMapping(value = {"/index", })
    @Anonymous
    public String index2()
    {
        return "forward:/index.html";
    }


    // 处理前端请求路径时，刷新后跳转到 index.html 页面

    @RequestMapping(value = {"/device/**","/{path:[^\\.]*}"})
    @Anonymous
    public String redirect() {
        return "forward:/index.html";
    }
}
