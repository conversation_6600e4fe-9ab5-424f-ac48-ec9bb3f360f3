package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.entity.SysAiTrainEngineer;
import com.ruoyi.bussiness.service.SysAiTrainEngineerService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 训练工程Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/train")
public class SysAiTrainEngineerController  extends BaseController {

    @Autowired
    private SysAiTrainEngineerService aiTrainEngineerService;

    /**
     * 查询训练工程列表
     */
    @PreAuthorize("@ss.hasPermi('admin:train:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAiTrainEngineer sysAiTrainEngineer) {
        startPage();
        List<SysAiTrainEngineer> list = aiTrainEngineerService.listPage(sysAiTrainEngineer);
        return getDataTable(list);
    }

    /**
     * 获取训练工程详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:train:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(aiTrainEngineerService.getById(id));
    }

    /**
     * 新增训练工程
     */
    @PreAuthorize("@ss.hasPermi('admin:train:add')")
    @Log(title = "训练工程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAiTrainEngineer sysAiTrainEngineer) {
        sysAiTrainEngineer.setCreateBy(getUsername());
        sysAiTrainEngineer.setUpdateBy(getUsername());
        return toAjax(aiTrainEngineerService.saveCustom(sysAiTrainEngineer));
    }

    /**
     * 修改训练工程
     */
    @PreAuthorize("@ss.hasPermi('admin:train:edit')")
    @Log(title = "训练工程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAiTrainEngineer sysAiTrainEngineer) {
        sysAiTrainEngineer.setUpdateBy(getUsername());
        return toAjax(aiTrainEngineerService.updateByIdCustom(sysAiTrainEngineer));
    }

    /**
     * 删除训练工程
     */
    @PreAuthorize("@ss.hasPermi('admin:train:remove')")
    @Log(title = "训练工程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(aiTrainEngineerService.removeByIdsCustom(Arrays.asList(ids)));
    }

    /**
     * 后台下载训练工程文件
     */
    @PreAuthorize("@ss.hasPermi('admin:train:download')")
    @GetMapping("/download/{identifier}")
    public AjaxResult download(@PathVariable String identifier) {
        return AjaxResult.success(aiTrainEngineerService.getDownloadUrl(identifier));
    }

    /**
     * 用户端下载训练工程文件
     */
    @PreAuthorize("@ss.hasPermi('ai:train:download')")
    @GetMapping("/ai/download/{identifier}")
    public AjaxResult downloadfront(@PathVariable String identifier) {
        return AjaxResult.success(aiTrainEngineerService.getDownloadUrl(identifier));
    }

}