package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.param.BatchInitTaskParam;
import com.ruoyi.bussiness.model.param.InitTaskParam;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 分片上传-分片任务记录(SysUploadTask)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-09 17:47:31
 */
@RestController
@RequestMapping("/v1/ai/file")
public class MinioUploadTaskController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(MinioUploadTaskController.class);
    /**
     * 服务对象
     */
    @Resource
    private SysUploadTaskService sysUploadTaskService;


    /**
     * 获取上传进度
     * @param identifier 文件md5
     * @param fileType 文件类型(可选)
     * @return  任务信息
     */
    @GetMapping("/{identifier}")
    public AjaxResult taskInfo (@PathVariable("identifier") String identifier,
                               @RequestParam(value = "fileType", required = false) String fileType) {
        return AjaxResult.success(sysUploadTaskService.getTaskInfo(identifier, fileType));
    }

    /**
     * 创建一个上传任务
     * @param param 包含文件名、大小、分片大小、标识符和文件类型(model/config/image)
     * @return 初始化任务信息
     */
    @PostMapping
    public AjaxResult initTask (@Valid @RequestBody InitTaskParam param, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.error(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        try {
            return AjaxResult.success(sysUploadTaskService.initTask(param));
        } catch (Exception e) {
            log.error("创建上传任务失败", e);
            return AjaxResult.error("创建上传任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件下载地址
     * @param identifier 文件标识
     * @param expires 过期时间（秒），默认1小时
     * @param fileType 文件类型
     * @return 预签名的下载URL
     */
    @PreAuthorize("@ss.hasPermi('admin:modelFile:download')")
    @GetMapping("/download/{identifier}")
    public AjaxResult getDownloadUrl(@PathVariable("identifier") String identifier,
                                     @RequestParam(value = "expires", required = false, defaultValue = "3600") Long expires,
                                     @RequestParam(value = "isRecord", required = false, defaultValue = "false") Boolean isRecord,
                                     @RequestParam(value = "fileType", required = false) String fileType) {
        try {
            String downloadUrl = sysUploadTaskService.genPreSignDownloadUrl(identifier, expires, isRecord, fileType);
            return AjaxResult.success("获取下载地址成功", downloadUrl);
        } catch (RuntimeException e) {
            log.error("获取下载地址失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取每个分片的预签名上传地址
     * @param identifier 标识
     * @param partNumber   分片号
     * @return
     */
    @GetMapping("/{identifier}/{partNumber}/{fileType}")
    public AjaxResult preSignUploadUrl (@PathVariable("identifier") String identifier,
                                        @PathVariable("partNumber") Integer partNumber,
                                        @PathVariable("fileType") String fileType) {
        String username = SecurityUtils.getUsername();
        SysUploadTask task = sysUploadTaskService.getByIdentifierAndUserAndType(identifier, username, fileType);
        if (task == null) {
            return AjaxResult.error("分片任务不存在");
        }
        Map<String, String> params = new HashMap<>();
        params.put("partNumber", partNumber.toString());
        params.put("uploadId", task.getUploadId());
        return AjaxResult.success(sysUploadTaskService.genPreSignUploadUrl(task.getBucketName(), task.getObjectKey(), params));
    }

    /**
     * 合并分片
     * @param identifier    标识
     * @return  合并成功与否
     */
    @PostMapping("/merge/{identifier}/{fileType}")
    public AjaxResult merge (@PathVariable("identifier") String identifier, @PathVariable("fileType") String fileType) {
        sysUploadTaskService.merge(identifier, fileType);
        return AjaxResult.success();
    }

    /**
     * 批量创建上传任务
     * @param param 批量上传参数，包含文件列表和文件类型(model/config/image)
     * @return 批量任务信息
     */
    @PostMapping("/batchUpload")
    public AjaxResult batchInitTask(@Valid @RequestBody BatchInitTaskParam param, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return AjaxResult.error(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
        }
        try {
            return AjaxResult.success(sysUploadTaskService.batchInitTask(param));
        } catch (Exception e) {
            log.error("批量创建上传任务失败", e);
            return AjaxResult.error("批量创建上传任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取上传任务列表
     *
     * @param task 查询参数
     * @return 分页数据
     */
    @PreAuthorize("@ss.hasPermi('admin:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUploadTask task) {
        startPage();
        return getDataTable(sysUploadTaskService.queryPage(task));
    }


    /**
     * 导出上传任务列表
     *
     * @param task 查询参数
     * @return 导出结果
     */
    @PreAuthorize("@ss.hasPermi('admin:file:export')")
    @Log(title = "上传任务管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SysUploadTask task) {
        List<SysUploadTask> list = sysUploadTaskService.list();
        ExcelUtil<SysUploadTask> util = new ExcelUtil<>(SysUploadTask.class);
        return util.exportExcel(list, "上传任务数据");
    }

    /**
     * 获取上传任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    @PreAuthorize("@ss.hasPermi('admin:file:query')")
    @GetMapping("/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sysUploadTaskService.getTaskDetail(id));
    }


    /**
     * 通过文件标识符获取上传任务
     *
     * @param fileIdentifier 文件标识符（md5）
     * @return 上传任务信息
     */
    @GetMapping("/task/{fileIdentifier}")
    public AjaxResult getTaskByIdentifier(@PathVariable("fileIdentifier") String fileIdentifier) {
        try {
            SysUploadTask task = sysUploadTaskService.getByIdentifier(fileIdentifier);
            if (task == null) {
                return AjaxResult.error("未找到相关上传任务");
            }
            return AjaxResult.success(task);
        } catch (Exception e) {
            log.error("获取上传任务失败", e);
            return AjaxResult.error("获取上传任务失败：" + e.getMessage());
        }
    }

    /**
     * 修改上传任务
     *
     * @param task 上传任务信息
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('admin:file:edit')")
    @Log(title = "上传任务管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUploadTask task) {
        if (task.getId() == null) {
            return AjaxResult.error("任务ID不能为空");
        }
        return toAjax(sysUploadTaskService.updateTask(task));
    }

    /**
     * 删除上传任务
     *
     * @param id 任务ID
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('admin:file:remove')")
    @Log(title = "上传任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        try {
            return toAjax(sysUploadTaskService.deleteTask(id));
        } catch (Exception e) {
            log.error("删除上传任务失败", e);
            return AjaxResult.error("删除上传任务失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除上传任务
     *
     * @param ids 任务ID数组
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('admin:file:remove')")
    @Log(title = "上传任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult batchRemove(@PathVariable Long[] ids) {
        try {
            return toAjax(sysUploadTaskService.deleteBatch(ids));
        } catch (Exception e) {
            log.error("批量删除上传任务失败", e);
            return AjaxResult.error("批量删除上传任务失败：" + e.getMessage());
        }
    }

    /**
     * 批量下载文件
     *
     * @param identifiers 文件标识符列表
     * @param response HTTP响应
     */
    @PreAuthorize("@ss.hasPermi('admin:modelFile:download')")
    @PostMapping("/batchDownload")
    public void batchDownload(@RequestBody List<String> identifiers, HttpServletResponse response) {
        try {
            sysUploadTaskService.batchDownloadFiles(identifiers, response);
        } catch (Exception e) {
            log.error("批量下载文件失败", e);
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                response.getWriter().write("{\"msg\":\"批量下载文件失败：" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

}
