package com.ruoyi.web.controller.bussiness;

import java.util.Arrays;
import java.util.List;

import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.bussiness.model.entity.SysLfnnVersion;
import com.ruoyi.bussiness.service.ISysLfnnVersionService;

/**
 * LFNN版本Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/lfnnVersion")
public class SysLfnnVersionController extends BaseController {
    @Autowired
    private ISysLfnnVersionService lfnnVersionService;

    /**
     * 查询LFNN版本列表
     */
    @PreAuthorize("@ss.hasPermi('admin:lfnnVersion:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysLfnnVersion lfnnVersion) {
        startPage();
        List<SysLfnnVersion> list = lfnnVersionService.listPage(lfnnVersion);
        return getDataTable(list);
    }

    /**
     * 获取LFNN版本详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:lfnnVersion:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(lfnnVersionService.getById(id));
    }

    /**
     * 新增LFNN版本
     */
    @PreAuthorize("@ss.hasPermi('admin:lfnnVersion:add')")
    @Log(title = "LFNN版本", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysLfnnVersion lfnnVersion) {
        lfnnVersion.setCreateBy(getUsername());
        lfnnVersion.setUpdateBy(getUsername());
        return toAjax(lfnnVersionService.saveCustom(lfnnVersion));
    }

    /**
     * 修改LFNN版本
     */
    @PreAuthorize("@ss.hasPermi('admin:lfnnVersion:edit')")
    @Log(title = "LFNN版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysLfnnVersion lfnnVersion) {
        lfnnVersion.setUpdateBy(getUsername());
        return toAjax(lfnnVersionService.updateByIdCustom(lfnnVersion));
    }

    /**
     * 删除LFNN版本
     */
    @PreAuthorize("@ss.hasPermi('admin:lfnnVersion:remove')")
    @Log(title = "LFNN版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(lfnnVersionService.removeByIdsCustom(Arrays.asList(ids)));
    }

    /**
     * 下载LFNN版本文件
     */
    @PreAuthorize("@ss.hasPermi('admin:lfnnVersion:download')")
    @GetMapping("/download/{identifier}")
    public AjaxResult download(@PathVariable String identifier) {
        return AjaxResult.success(lfnnVersionService.getDownloadUrl(identifier));
    }


    /**
     * 下载LFNN版本文件  权限控制区分用户与管理员
     */
    @PreAuthorize("@ss.hasPermi('ai:lfnnVersion:download')")
    @GetMapping("/ai/download/{identifier}")
    public AjaxResult downloadlfnn(@PathVariable String identifier) {
        return AjaxResult.success(lfnnVersionService.getDownloadUrl(identifier));
    }


}
