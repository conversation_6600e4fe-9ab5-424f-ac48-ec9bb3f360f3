package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.entity.SysDownloadRecord;
import com.ruoyi.bussiness.model.entity.SysLfnnVersion;
import com.ruoyi.bussiness.service.ISysLfnnVersionService;
import com.ruoyi.bussiness.service.SysDownloadRecordService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 下载记录Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/downloadRecord")
public class SysDownloadRecordController extends BaseController {


    @Autowired
    private SysDownloadRecordService sysDownloadRecordService;

    /**
     * 查询下载记录列表
     */
    @PreAuthorize("@ss.hasPermi('admin:downloadRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDownloadRecord downloadRecord) {
        startPage();
        List<SysDownloadRecord> list = sysDownloadRecordService.listPage(downloadRecord);
        return getDataTable(list);
    }

    /**
     * 获取下载记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:downloadRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sysDownloadRecordService.getById(id));
    }

    /**
     * 新增下载记录
     */
    @PreAuthorize("@ss.hasPermi('admin:lfnnCersion:add')")
    @Log(title = "下载记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysDownloadRecord downloadRecord) {
        // 获取操作地址，参考AsyncFactory中recordLogininfor的实现
        String ip = IpUtils.getIpAddr();
        String optLocation = AddressUtils.getRealAddressByIP(ip);

        downloadRecord.setOptLocation(optLocation);
        downloadRecord.setCreateBy(getUsername());
        downloadRecord.setUpdateBy(getUsername());
        return toAjax(sysDownloadRecordService.save(downloadRecord));
    }

    /**
     * 修改下载记录
     */
    @PreAuthorize("@ss.hasPermi('admin:downloadRecord:edit')")
    @Log(title = "下载记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysDownloadRecord downloadRecord) {
        downloadRecord.setUpdateBy(getUsername());
        return toAjax(sysDownloadRecordService.updateById(downloadRecord));
    }

    /**
     * 删除下载记录
     */
    @PreAuthorize("@ss.hasPermi('admin:downloadRecord:remove')")
    @Log(title = "下载记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysDownloadRecordService.removeBatchByIds(Arrays.asList(ids)));
    }



}
