package com.ruoyi.web.controller.bussiness;

import cn.hutool.core.collection.ListUtil;
import com.ruoyi.bussiness.model.entity.SysAiTaskHost;
import com.ruoyi.bussiness.service.ISysAiTaskHostService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI任务主机管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/ai/host")
public class SysAiTaskHostController extends BaseController {

    @Autowired
    private ISysAiTaskHostService sysAiTaskHostService;

    /**
     * 获取AI任务主机列表
     */
    @PreAuthorize("@ss.hasPermi('admin:aiHost:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAiTaskHost host) {
        startPage();
        List<SysAiTaskHost> list = sysAiTaskHostService.listQuery(host);
        return getDataTable(list);
    }

    /**
     * 新增文件类型
     */
    @PreAuthorize("@ss.hasPermi('admin:aiHost:add')")
    @PostMapping
    public AjaxResult add(@RequestBody SysAiTaskHost sysAiTaskHost) {
        return toAjax(sysAiTaskHostService.saveLogic(sysAiTaskHost));
    }

    /**
     * 获取AI任务主机详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:aiHost:query')")
    @GetMapping(value = "/{hostId}")
    public AjaxResult getInfo(@PathVariable("hostId") Long hostId) {
        return success(sysAiTaskHostService.getById(hostId));
    }

    /**
     * 修改AI任务主机
     */
    @PreAuthorize("@ss.hasPermi('admin:aiHost:edit')")
    @Log(title = "AI任务主机", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAiTaskHost host) {
        if (host.getHostId() == null) {
            return error("主机ID不能为空");
        }
        return toAjax(sysAiTaskHostService.updateById(host));
    }

    /**
     * 删除AI任务主机
     */
    @PreAuthorize("@ss.hasPermi('admin:aiHost:remove')")
    @Log(title = "AI任务主机", businessType = BusinessType.DELETE)
    @DeleteMapping("/{hostIds}")
    public AjaxResult remove(@PathVariable Long[] hostIds) {
        return toAjax(sysAiTaskHostService.removeBatchByIds(ListUtil.of(hostIds)));
    }

    /**
     * 上传可执行文件到远程服务器
     */
    @PreAuthorize("@ss.hasPermi('admin:aiHost:uploadExecutable')")
    @PostMapping("/uploadExecutable")
    public AjaxResult uploadExecutable(@RequestBody SysAiTaskHost sysAiTaskHost) {
        return toAjax(sysAiTaskHostService.uploadExecutable(sysAiTaskHost));
    }

} 