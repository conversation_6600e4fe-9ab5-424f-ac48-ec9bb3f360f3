package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.entity.SysAiTransferRecord;
import com.ruoyi.bussiness.model.vo.SysAiTransferRecordListVo;
import com.ruoyi.bussiness.service.ISysAiTransferRecordService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * AI模型转换日志Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/ai/transferRecord")
public class SysAiTransferRecordController extends BaseController {
    @Autowired
    private ISysAiTransferRecordService sysAiTransferLogService;

    /**
     * 查询转换日志列表 此处不使用若依的分页 处在bug
     */
    @PreAuthorize("@ss.hasPermi('admin:transferLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAiTransferRecord log) {
        // 获取完整列表
        List<SysAiTransferRecordListVo> list = sysAiTransferLogService.selectLogList(log);
        // 使用BaseController的手动分页方法
        return getDataTablePage(list);
    }

    /**
     * 导出转换日志列表
     */
    @PreAuthorize("@ss.hasPermi('admin:transferLog:export')")
    @Log(title = "转换日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysAiTransferRecord log) {
        List<SysAiTransferRecordListVo> list = sysAiTransferLogService.selectLogList(log);
        ExcelUtil<SysAiTransferRecordListVo> util = new ExcelUtil<>(SysAiTransferRecordListVo.class);
        util.exportExcel(response, list, "转换日志数据");
    }

    /**
     * 获取转换日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:transferLog:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId) {
        return success(sysAiTransferLogService.getById(logId));
    }

    /**
     * 新增转换日志
     */
    @PreAuthorize("@ss.hasPermi('admin:transferLog:add')")
    @Log(title = "转换日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAiTransferRecord log) {
        return toAjax(sysAiTransferLogService.insertLog(log));
    }

    /**
     * 修改转换日志
     */
    @PreAuthorize("@ss.hasPermi('admin:transferLog:edit')")
    @Log(title = "转换日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAiTransferRecord log) {
        return toAjax(sysAiTransferLogService.updateLog(log));
    }

    /**
     * 删除转换日志
     */
    @PreAuthorize("@ss.hasPermi('admin:transferLog:remove')")
    @Log(title = "转换日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds) {
        return toAjax(sysAiTransferLogService.deleteLogByIds(logIds));
    }
} 