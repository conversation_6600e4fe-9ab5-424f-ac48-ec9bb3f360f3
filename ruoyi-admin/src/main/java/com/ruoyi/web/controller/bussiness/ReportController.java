package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.vo.*;
import com.ruoyi.bussiness.service.IReportService;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 报表数据Controller
 *
 * <AUTHOR>
 */
@Api(tags = "报表数据")
@RestController
@RequestMapping("/v1/report")
public class ReportController {

    @Autowired
    private IReportService reportService;

    /**
     * 获取模型转换地区分布数据
     */
    @ApiOperation("获取模型转换地区分布数据")
    @GetMapping("/region-distribution")
    @PreAuthorize("@ss.hasPermi('report:region:view')")
    public AjaxResult getRegionDistribution(
            @ApiParam(value = "数据类型: conversion(模型转换次数)或download(SDK下载次数)", required = true)
            @RequestParam String dataType,
            @ApiParam(value = "开始时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String endTime) {
        
        ReportRegionDataVO data = reportService.getRegionDistribution(dataType, startTime, endTime);
        return AjaxResult.success("操作成功", data);
    }

    /**
     * 获取平台使用趋势数据
     */
    @ApiOperation("获取平台使用趋势数据")
    @GetMapping("/trend-data")
    @PreAuthorize("@ss.hasPermi('report:trend:view')")
    public AjaxResult getTrendData(
            @ApiParam(value = "时间单位：day(日)、month(月)、year(年)", required = true)
            @RequestParam String timeUnit,
            @ApiParam(value = "开始时间(day模式格式: yyyy-MM-dd, month模式格式: yyyy-MM, year模式格式: yyyy)")
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间(day模式格式: yyyy-MM-dd, month模式格式: yyyy-MM, year模式格式: yyyy)")
            @RequestParam(required = false) String endTime) {
        
        ReportTrendDataVO data = reportService.getTrendData(timeUnit, startTime, endTime);
        return AjaxResult.success("操作成功", data);
    }

    /**
     * 获取各模块访问分布数据
     */
    @ApiOperation("获取各模块访问分布数据")
    @GetMapping("/module-distribution")
    @PreAuthorize("@ss.hasPermi('report:module:view')")
    public AjaxResult getModuleDistribution(
            @ApiParam(value = "开始时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String endTime) {
        
        ReportModuleDataVO data = reportService.getModuleDistribution(startTime, endTime);
        return AjaxResult.success(data);
    }

    /**
     * 获取用户类型分布数据
     */
    @ApiOperation("获取用户类型分布数据")
    @GetMapping("/user-type-distribution")
    @PreAuthorize("@ss.hasPermi('report:usertype:view')")
    public AjaxResult getUserTypeDistribution(
            @ApiParam(value = "开始时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String endTime) {
        
        ReportUserTypeDataVO data = reportService.getUserTypeDistribution(startTime, endTime);
        return AjaxResult.success(data);
    }

    /**
     * 获取模型转换成功率数据
     */
    @ApiOperation("获取模型转换成功率数据")
    @GetMapping("/conversion-rate")
    @PreAuthorize("@ss.hasPermi('report:conversion:view')")
    public AjaxResult getConversionRate(
            @ApiParam(value = "开始时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String endTime) {
        
        ReportConversionRateVO data = reportService.getConversionRate(startTime, endTime);
        return AjaxResult.success(data);
    }

    /**
     * 获取基础统计数据（注册用户总数、用户操作总次数）
     */
    @ApiOperation("获取基础统计数据")
    @GetMapping("/basic-stats")
    @PreAuthorize("@ss.hasPermi('report:basic:view')")
    public AjaxResult getBasicStats(
            @ApiParam(value = "开始时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间(格式: yyyy-MM-dd)")
            @RequestParam(required = false) String endTime) {

            ReportBasicStatsVO data = reportService.getBasicStats(startTime, endTime);
        return AjaxResult.success("操作成功", data);
    }
}
