package com.ruoyi.web.controller.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.task.PermissionCacheRefreshTask;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserRoleService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色权限时间管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-017
 */
@RestController
@RequestMapping("/system/user-role-time")
public class SysUserRoleTimeController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysUserRoleService userRoleService;

    @Autowired
    private PermissionCacheRefreshTask permissionCacheRefreshTask;

    /**
     * 根据用户编号获取授权角色（带有效期信息）
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRoleWithTime(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);

        // 获取所有角色列表
        List<SysRole> allRoles = roleService.selectRolesByUserId(userId);

        // 获取用户角色关联信息（包含有效期）
        List<SysUserRole> userRoles = userRoleService.selectUserRolesByUserId(userId);

        // 为角色添加有效期信息
        for (SysRole role : allRoles) {
            for (SysUserRole userRole : userRoles) {
                if (role.getRoleId().equals(userRole.getRoleId())) {
                    // 将有效期信息添加到角色对象中
                    role.setStartTime(userRole.getStartTime());
                    role.setEndTime(userRole.getEndTime());
                    break;
                }
            }
        }

        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? allRoles : allRoles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色（带有效期）
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(@RequestBody UserRoleTimeRequest request)
    {
        if (request.getUserId() == null)
        {
            return error("用户ID不能为空");
        }
        
        if (request.getRoleIds() == null || request.getRoleIds().length == 0)
        {
            return error("角色ID不能为空");
        }
        
        // 验证时间有效性
        String validationResult = validateTimeRanges(request);
        if (StringUtils.isNotEmpty(validationResult))
        {
            return error(validationResult);
        }
        
        userService.insertUserAuthWithTime(request.getUserId(), request.getRoleIds(),
                                         request.getStartTimes(), request.getEndTimes());

        // 立即刷新用户权限缓存，确保权限变更立即生效
        permissionCacheRefreshTask.refreshUserPermissionByUserId(request.getUserId());

        return success();
    }
    
    /**
     * 验证时间范围的有效性
     * 
     * @param request 请求参数
     * @return 验证结果，为空表示验证通过
     */
    private String validateTimeRanges(UserRoleTimeRequest request)
    {
        Date[] startTimes = request.getStartTimes();
        Date[] endTimes = request.getEndTimes();
        Long[] roleIds = request.getRoleIds();
        
        if (startTimes != null && startTimes.length != roleIds.length)
        {
            return "开始时间数量与角色数量不匹配";
        }
        
        if (endTimes != null && endTimes.length != roleIds.length)
        {
            return "结束时间数量与角色数量不匹配";
        }
        
        // 验证时间逻辑
        for (int i = 0; i < roleIds.length; i++)
        {
            Date startTime = (startTimes != null && i < startTimes.length) ? startTimes[i] : null;
            Date endTime = (endTimes != null && i < endTimes.length) ? endTimes[i] : null;
            
            if (startTime != null && endTime != null)
            {
                if (startTime.compareTo(endTime) >= 0)
                {
                    return "角色 " + roleIds[i] + " 的开始时间不能大于或等于结束时间";
                }
            }
            
            // 检查结束时间不能是过去时间（允许1分钟的时间差，避免时区和网络延迟问题）
            if (endTime != null)
            {
                Date now = new Date();
                Date oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
                if (endTime.before(oneMinuteAgo))
                {
                    return "角色 " + roleIds[i] + " 的结束时间不能是过去时间";
                }
            }
        }
        
        return null;
    }
    
    /**
     * 用户角色权限时间请求参数
     */
    public static class UserRoleTimeRequest
    {
        /** 用户ID */
        private Long userId;
        
        /** 角色ID数组 */
        private Long[] roleIds;
        
        /** 开始时间数组 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date[] startTimes;

        /** 结束时间数组 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date[] endTimes;

        public Long getUserId()
        {
            return userId;
        }

        public void setUserId(Long userId)
        {
            this.userId = userId;
        }

        public Long[] getRoleIds()
        {
            return roleIds;
        }

        public void setRoleIds(Long[] roleIds)
        {
            this.roleIds = roleIds;
        }

        public Date[] getStartTimes()
        {
            return startTimes;
        }

        public void setStartTimes(Date[] startTimes)
        {
            this.startTimes = startTimes;
        }

        public Date[] getEndTimes()
        {
            return endTimes;
        }

        public void setEndTimes(Date[] endTimes)
        {
            this.endTimes = endTimes;
        }
    }
}
