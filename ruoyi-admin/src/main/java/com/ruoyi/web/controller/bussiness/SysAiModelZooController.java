package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.entity.SysAiModelZoo;
import com.ruoyi.bussiness.service.SysAiModelZooService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * AI任务主机管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/ai/modelZoo")
public class SysAiModelZooController extends BaseController {

    @Autowired
    private SysAiModelZooService sysAiModelZooService;

    @Autowired
    private SysUploadTaskService sysUploadTaskService;

    /**
     * 查询模型列表
     */
    @PreAuthorize("@ss.hasPermi('admin:modelZooManager:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAiModelZoo model) {
        startPage();
        List<SysAiModelZoo> list = sysAiModelZooService.selectModelList(model);
        return getDataTable(list);
    }

    /**
     * 获取模型详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:modelZooManager:query')")
    @GetMapping(value = "/{modelId}")
    public AjaxResult getInfo(@PathVariable("modelId") Long modelId) {
        return success(sysAiModelZooService.getById(modelId));
    }

    /**
     * 新增模型
     */
    @PreAuthorize("@ss.hasPermi('admin:modelZooManager:add')")
    @Log(title = "AI模型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysAiModelZoo model) {
        model.setCreateBy(getUsername());
        model.setUpdateBy(getUsername());
        return toAjax(sysAiModelZooService.save(model));
    }

    /**
     * 修改模型
     */
    @PreAuthorize("@ss.hasPermi('admin:modelZooManager:edit')")
    @Log(title = "AI模型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysAiModelZoo model) {
        model.setUpdateBy(getUsername());
        return toAjax(sysAiModelZooService.updateByIdCustom(model));
    }

    /**
     * 删除模型
     */
    @PreAuthorize("@ss.hasPermi('admin:modelZooManager:remove')")
    @Log(title = "AI模型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{modelIds}")
    public AjaxResult remove(@PathVariable Long[] modelIds) {
        return toAjax(sysAiModelZooService.removeBatchByIdsCustom(Arrays.asList(modelIds)));
    }
    /**
     * 获取文件下载地址
     * @param identifier 文件标识
     * @param expires 过期时间（秒），默认1小时
     * @param fileType 文件类型
     * @return 预签名的下载URL
     */
    @PreAuthorize("@ss.hasPermi('ai:modelZooManager:download')")
    @GetMapping("/download/{identifier}")
    public AjaxResult getDownloadUrl(@PathVariable("identifier") String identifier,
                                     @RequestParam(value = "expires", required = false, defaultValue = "3600") Long expires,
                                     @RequestParam(value = "fileType", required = false) String fileType) {
        try {
            String downloadUrl = sysUploadTaskService.genPreSignDownloadUrl(identifier, expires, true, fileType);
            return AjaxResult.success("获取下载地址成功", downloadUrl);
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
