package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.dto.AiTransferRequest;
import com.ruoyi.bussiness.model.entity.SysAiTransferRecord;
import com.ruoyi.bussiness.service.IAiTransferService;
import com.ruoyi.bussiness.service.ISysAiTransferRecordService;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @desc    模型转换服务
 * @created 2025-05-19
 */

@Api(tags = "模型转换服务")
@Validated
@RestController
@RequestMapping("/v1/ai/transfer")
public class AiTransferController {

    @Autowired
    private  IAiTransferService aiTransferService;

    @Autowired
    private SysUploadTaskService sysUploadTaskService;

    @Autowired
    private ISysAiTransferRecordService sysAiTransferRecordService;

    @ApiOperation("执行模型转换")
    @PostMapping("/execute")
    @PreAuthorize("@ss.hasPermi('ai:transfer:transfer')")
    public AjaxResult executeTransfer(
            @ApiParam(value = "AI模型转换请求参数", required = true)
            @RequestBody AiTransferRequest request) {
        String taskId = aiTransferService.executeTransfer(request);
        return AjaxResult.success("提交成功", taskId);
    }

    /**
     * 获取文件下载地址
     * @param identifier 文件标识
     * @param expires 过期时间（秒），默认1小时
     * @param fileType 文件类型
     * @return 预签名的下载URL
     */
    @PreAuthorize("@ss.hasPermi('ai:transfer:download')")
    @GetMapping("/download/{identifier}")
    public AjaxResult getDownloadUrl(@PathVariable("identifier") String identifier,
                                     @RequestParam(value = "expires", required = false, defaultValue = "3600") Long expires,
                                     @RequestParam(value = "fileType", required = false) String fileType) {
        try {
            String downloadUrl = sysUploadTaskService.genPreSignDownloadUrl(identifier, expires, true, fileType);
            return AjaxResult.success("获取下载地址成功", downloadUrl);
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取转换任务记录信息
     * @param taskId
     * @return
     */
    @GetMapping("/transferStatus/{taskId}")
    public AjaxResult transferStatus(@PathVariable("taskId") String taskId) {
        try {
           SysAiTransferRecord transferRecord = sysAiTransferRecordService.transferStatus(taskId);
            return AjaxResult.success("获取下载地址成功", transferRecord);
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

}
