package com.ruoyi.web.controller.bussiness;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.bussiness.model.entity.SysAiAccessRecord;
import com.ruoyi.bussiness.service.SysAiAccessRecordService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "数据访问记录，用于记录访问数据统计报表数据展示")
@RestController
@RequestMapping("/v1/accessRecord")
public class SysAiAccessRecordController extends BaseController {
    
    
    @Autowired
    private SysAiAccessRecordService sysAiAccessRecordService;


    /**
     * 获取AI任务主机列表
     */
    @PreAuthorize("@ss.hasPermi('admin:aiAccessRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysAiAccessRecord record) {
        startPage();
        List<SysAiAccessRecord> list = sysAiAccessRecordService.list(Wrappers.query(record));
        return getDataTable(list);
    }

    /**
     * 新增文件类型
     */
    @PreAuthorize("@ss.hasPermi('admin:aiAccessRecord:add')")
    @PostMapping
    public AjaxResult add(@RequestBody SysAiAccessRecord SysAiAccessRecord) {
        return toAjax(sysAiAccessRecordService.saveOrUpdateCustom(SysAiAccessRecord));
    }

    /**
     * 获取AI任务主机详细信息
     */
    @PreAuthorize("@ss.hasPermi('admin:aiAccessRecord:query')")
    @GetMapping(value = "/{hostId}")
    public AjaxResult getInfo(@PathVariable("hostId") Long hostId) {
        return success(sysAiAccessRecordService.getById(hostId));
    }

    /**
     * 修改AI任务主机
     */
    @PreAuthorize("@ss.hasPermi('admin:aiAccessRecord:edit')")
    @PutMapping
    public AjaxResult edit(@RequestBody SysAiAccessRecord host) {
        return toAjax(sysAiAccessRecordService.updateById(host));
    }

    /**
     * 删除AI任务主机
     */
    @PreAuthorize("@ss.hasPermi('admin:aiAccessRecord:remove')")
    @DeleteMapping("/{hostIds}")
    public AjaxResult remove(@PathVariable Long[] hostIds) {
        return toAjax(sysAiAccessRecordService.removeBatchByIds(ListUtil.of(hostIds)));
    }
    

}
