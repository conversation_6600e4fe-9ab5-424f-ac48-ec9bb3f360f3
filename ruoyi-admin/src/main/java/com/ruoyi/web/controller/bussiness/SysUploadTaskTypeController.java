package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.entity.SysUploadTaskType;
import com.ruoyi.bussiness.service.SysUploadTaskTypeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传类型管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ai/file/type")
public class SysUploadTaskTypeController extends BaseController {

    @Resource
    private SysUploadTaskTypeService sysUploadTaskTypeService;

    /**
     * 获取文件类型列表
     */
    @PreAuthorize("@ss.hasPermi('admin:fileType:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUploadTaskType uploadTaskType) {
        startPage();
        List<SysUploadTaskType> list = sysUploadTaskTypeService.listQuery(uploadTaskType);
        return getDataTable(list);
    }


    /**
     * 新增文件类型
     */
    @PreAuthorize("@ss.hasPermi('admin:fileType:add')")
    @Log(title = "文件类型管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysUploadTaskType uploadTaskType) {
        return toAjax(sysUploadTaskTypeService.save(uploadTaskType));
    }

    /**
     * 修改文件类型
     */
    @PreAuthorize("@ss.hasPermi('admin:fileType:edit')")
    @Log(title = "文件类型管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUploadTaskType uploadTaskType) {
        return toAjax(sysUploadTaskTypeService.updateById(uploadTaskType));
    }

    /**
     * 删除文件类型
     */
    @PreAuthorize("@ss.hasPermi('admin:fileType:remove')")
    @Log(title = "文件类型管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sysUploadTaskTypeService.removeBatchByIds(Arrays.asList(ids)));
    }


    /**
     * 删除文件类型
     */
    @PreAuthorize("@ss.hasPermi('admin:fileType:info')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(sysUploadTaskTypeService.getById(id));
    }
} 