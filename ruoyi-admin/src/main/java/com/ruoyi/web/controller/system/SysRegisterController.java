package com.ruoyi.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.bussiness.model.dto.TaskInfoDTO;
import com.ruoyi.bussiness.model.param.InitTaskParam;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.bussiness.model.enums.FileTypeEnum;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysRegisterService;
import com.ruoyi.framework.web.service.SysSmsService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.util.DigestUtils;

/**
 * 注册验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysRegisterController extends BaseController
{
    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysSmsService smsService;

    @Resource
    private SysUploadTaskService sysUploadTaskService;

    @PostMapping("/register")
    public AjaxResult register(@RequestBody RegisterBody user)
    {
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return error("当前系统没有开启注册功能！");
        }

        String msg = registerService.register(user);
        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }



    /**
     * 支持文件上传的用户注册（两步流程：先注册用户，再上传文件）
     */
    @PostMapping("/registerWithFile")
    public AjaxResult registerWithFile(RegisterBody registerBody,
                                     @RequestParam(value = "companyRegistration", required = false) MultipartFile companyRegistration,
                                     @RequestParam(value = "ndaContract", required = false) MultipartFile ndaContract)
    {
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return error("当前系统没有开启注册功能！");
        }

        try {
            // 第一步：先执行用户注册（不处理文件）
            String msg = registerService.register(registerBody);
            if (StringUtils.isNotEmpty(msg)) {
                return error(msg);
            }

            // 第二步：注册成功后，处理文件上传
            if (companyRegistration != null && !companyRegistration.isEmpty() ||
                ndaContract != null && !ndaContract.isEmpty()) {

                List<String> fileIdentifiers = new ArrayList<>();

                // 上传公司注册信息文件
                if (companyRegistration != null && !companyRegistration.isEmpty()) {
                    String companyRegIdentifier = uploadFile(companyRegistration, FileTypeEnum.COMPANY_REGISTRATION.getCode(), registerBody.getUsername());
                    if (StringUtils.isNotEmpty(companyRegIdentifier)) {
                        fileIdentifiers.add(companyRegIdentifier);
                    }
                }

                // 上传NDA合同文件
                if (ndaContract != null && !ndaContract.isEmpty()) {
                    String ndaIdentifier = uploadFile(ndaContract, FileTypeEnum.NDA_CONTRACT.getCode(), registerBody.getUsername());
                    if (StringUtils.isNotEmpty(ndaIdentifier)) {
                        fileIdentifiers.add(ndaIdentifier);
                    }
                }

                // 第三步：关联文件到已注册的用户
                if (!fileIdentifiers.isEmpty() && registerBody.getUserId() != null) {
                    registerService.associateFilesToUser(registerBody.getUserId(), fileIdentifiers);
                }
            }

            return success();

        } catch (Exception e) {
            logger.error("注册失败", e);
            return error("注册失败：" + e.getMessage());
        }
    }

    /**
     * 发送手机验证码
     */
    @GetMapping("/sendSmsCode")
    public AjaxResult sendSmsCode(@RequestParam("phoneNumber") String phoneNumber)
    {
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return error("当前系统没有开启注册功能！");
        }

        if (StringUtils.isEmpty(phoneNumber)) {
            return error("手机号码不能为空");
        }

        // 标准化手机号码格式，确保与验证时使用相同的格式
        String normalizedPhone = normalizePhoneNumber(phoneNumber);

        String msg = smsService.sendSmsCode(normalizedPhone);
        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }

    /**
     * 上传文件到MinIO
     *
     * @param file 文件
     * @param fileType 文件类型
     * @param username 上传用户名
     * @return 文件标识符
     */
    private String uploadFile(MultipartFile file, String fileType, String username) {
        try {
            // 生成文件标识符（使用MD5）
            String identifier = generateFileIdentifier(file);

            // 创建上传任务参数
            InitTaskParam param = new InitTaskParam();
            param.setFileName(file.getOriginalFilename());
            param.setTotalSize(file.getSize());
            param.setChunkSize(file.getSize()); // 单文件上传，分片大小等于文件大小
            param.setIdentifier(identifier);
            param.setFileType(fileType);
            param.setCreateBy(username); // 设置上传用户为注册用户
            param.setUpdateBy(username);

            // 初始化上传任务
            TaskInfoDTO taskInfo = sysUploadTaskService.initTask(param);

            // 这里需要实现实际的文件上传逻辑
            // 由于SysUploadTask主要用于分片上传，对于注册时的小文件
            // 可以直接使用简单的上传方式，或者调用merge方法完成上传

            // 返回文件标识符用于后续关联
            return identifier;

        } catch (Exception e) {
            logger.error("文件上传失败", e);
            return null;
        }
    }

    /**
     * 生成文件标识符（使用MD5）
     *
     * @param file 文件
     * @return 文件标识符
     */
    private String generateFileIdentifier(MultipartFile file) {
        try {
            // 使用文件内容生成MD5作为标识符
            byte[] fileBytes = file.getBytes();
            return DigestUtils.md5DigestAsHex(fileBytes);
        } catch (Exception e) {
            // 如果MD5生成失败，使用文件名+大小+时间戳作为备选方案
            return file.getOriginalFilename() + "_" + file.getSize() + "_" + System.currentTimeMillis();
        }
    }

    /**
     * 标准化手机号码格式，确保发送验证码和验证验证码使用相同的格式
     *
     * @param phoneNumber 原始手机号码
     * @return 标准化后的手机号码
     */
    private String normalizePhoneNumber(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return phoneNumber;
        }

        // 移除可能的空格、破折号等字符
        String normalized = phoneNumber.replaceAll("[\\s-]", "");

        // 确保是11位数字的中国手机号
        if (normalized.matches("^1[3-9]\\d{9}$")) {
            return normalized;
        }

        return phoneNumber; // 如果不符合标准格式，返回原始号码
    }
}
