package com.ruoyi.web.controller.bussiness;

import com.ruoyi.RuoYiApplication;
import com.ruoyi.bussiness.model.vo.*;
import com.ruoyi.bussiness.service.IReportService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * 报表服务测试类
 *
 * <AUTHOR>
 */
@ExtendWith({SpringExtension.class, MockitoExtension.class})
@SpringBootTest
@Slf4j
public class ReportServiceTest {

    @Autowired
    private IReportService reportService;

    @Test
    public void testGetRegionDistribution() {
        log.info("地区分布数据测试开始");
        try {
            // 测试模型转换地区分布
            ReportRegionDataVO conversionResult = reportService.getRegionDistribution("conversion", null, null);
            log.info("模型转换地区分布数据测试成功: " + conversionResult);

            // 测试SDK下载地区分布
            ReportRegionDataVO downloadResult = reportService.getRegionDistribution("download", null, null);
            log.info("SDK下载地区分布数据测试成功: " + downloadResult);

        } catch (Exception e) {
            log.error("地区分布数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetTrendData() {
        try {
            ReportTrendDataVO result = reportService.getTrendData("month", null, null);
            log.info("趋势数据测试成功: " + result);
        } catch (Exception e) {
            log.error("趋势数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetModuleDistribution() {
        try {
            ReportModuleDataVO result = reportService.getModuleDistribution(null, null);
            log.info("模块分布数据测试成功: " + result);
        } catch (Exception e) {
            log.error("模块分布数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetUserTypeDistribution() {
        try {
            ReportUserTypeDataVO result = reportService.getUserTypeDistribution(null, null);
            log.info("用户类型分布数据测试成功: " + result);
        } catch (Exception e) {
            log.error("用户类型分布数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetConversionRate() {
        try {
            ReportConversionRateVO result = reportService.getConversionRate(null, null);
            log.info("转换成功率数据测试成功: " + result);
        } catch (Exception e) {
            log.error("转换成功率数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetBasicStats() {
        try {
            ReportBasicStatsVO result = reportService.getBasicStats(null, null);
            System.out.println("基础统计数据测试成功: " + result);
            System.out.println("注册用户总数: " + result.getTotalUsers());
            System.out.println("用户访问次数: " + result.getTotalVisits());
        } catch (Exception e) {
            System.err.println("基础统计数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
