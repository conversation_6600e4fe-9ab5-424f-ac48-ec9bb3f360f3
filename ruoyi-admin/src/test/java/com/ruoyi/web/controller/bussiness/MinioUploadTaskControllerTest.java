package com.ruoyi.web.controller.bussiness;

import com.ruoyi.bussiness.model.dto.CustomPartSummary;
import com.ruoyi.bussiness.model.dto.TaskInfoDTO;
import com.ruoyi.bussiness.model.dto.TaskRecordDTO;
import com.ruoyi.bussiness.model.entity.SysUploadTask;
import com.ruoyi.bussiness.model.enums.FileTypeEnum;
import com.ruoyi.bussiness.model.param.InitTaskParam;
import com.ruoyi.bussiness.service.SysUploadTaskService;
import com.ruoyi.common.core.domain.AjaxResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith({SpringExtension.class, MockitoExtension.class})
@SpringBootTest
class MinioUploadTaskControllerTest {

    @InjectMocks
    private MinioUploadTaskController minioUploadTaskController;

    @Mock
    private SysUploadTaskService sysUploadTaskService;

    @Mock
    private BindingResult bindingResult;

    private static final String TEST_IDENTIFIER = "test-file-md5";
    private static final String TEST_FILENAME = "test.txt";
    private static final Long TEST_FILE_SIZE = 1024L * 1024L; // 1MB
    private static final Long TEST_CHUNK_SIZE = 1024L * 100L; // 100KB
    private static final String TEST_FILE_TYPE = FileTypeEnum.MODEL.getCode();
    private static final String TEST_BUCKET = "test-bucket";
    private static final String TEST_OBJECT_KEY = "model/2024-03-20/test-object-key.txt";
    private static final String TEST_UPLOAD_ID = "test-upload-id";
    private static final String TEST_PRESIGNED_URL = "https://minio-server/presigned-url";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCompleteFileUploadProcess() {
        // 1. 首先测试获取不存在的上传任务
        when(sysUploadTaskService.getTaskInfo(TEST_IDENTIFIER, "model")).thenReturn(null);
        AjaxResult taskInfoResult = minioUploadTaskController.taskInfo(TEST_IDENTIFIER, "model");
        assertNull(taskInfoResult.get("data"));

        // 2. 创建上传任务
        InitTaskParam initTaskParam = new InitTaskParam();
        initTaskParam.setIdentifier(TEST_IDENTIFIER);
        initTaskParam.setFileName(TEST_FILENAME);
        initTaskParam.setTotalSize(TEST_FILE_SIZE);
        initTaskParam.setChunkSize(TEST_CHUNK_SIZE);
        initTaskParam.setFileType(TEST_FILE_TYPE);

        TaskInfoDTO mockTaskInfo = createMockTaskInfo(false);
        when(bindingResult.hasErrors()).thenReturn(false);
        when(sysUploadTaskService.initTask(any(InitTaskParam.class))).thenReturn(mockTaskInfo);

        AjaxResult initResult = minioUploadTaskController.initTask(initTaskParam, bindingResult);
        assertEquals(200, initResult.get("code"));
        assertNotNull(initResult.get("data"));

        // 3. 测试获取已存在的任务信息
        when(sysUploadTaskService.getTaskInfo(TEST_IDENTIFIER, "model")).thenReturn(mockTaskInfo);
        AjaxResult existingTaskResult = minioUploadTaskController.taskInfo(TEST_IDENTIFIER, "model");
        assertEquals(200, existingTaskResult.get("code"));
        assertNotNull(existingTaskResult.get("data"));

        // 4. 获取分片上传URL
        SysUploadTask mockTask = new SysUploadTask();
        mockTask.setFileIdentifier(TEST_IDENTIFIER);
        mockTask.setBucketName(TEST_BUCKET);
        mockTask.setObjectKey(TEST_OBJECT_KEY);
        mockTask.setUploadId(TEST_UPLOAD_ID);

        when(sysUploadTaskService.getByIdentifier(TEST_IDENTIFIER)).thenReturn(mockTask);

        // 预期的参数Map
        Map<String, String> expectedParams = new HashMap<>();
        expectedParams.put("partNumber", "1");
        expectedParams.put("uploadId", TEST_UPLOAD_ID);

        // 使用ArgumentCaptor捕获参数
        ArgumentCaptor<String> bucketCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> objectKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map<String, String>> paramsCaptor = ArgumentCaptor.forClass(Map.class);

        when(sysUploadTaskService.genPreSignUploadUrl(
            bucketCaptor.capture(),
            objectKeyCaptor.capture(),
            paramsCaptor.capture()
        )).thenReturn(TEST_PRESIGNED_URL);

        AjaxResult preSignResult = minioUploadTaskController.preSignUploadUrl(TEST_IDENTIFIER, 1, null);
        
        // 验证结果
        assertEquals(200, preSignResult.get("code"));
        assertEquals(TEST_PRESIGNED_URL, preSignResult.get("msg"));

        // 验证参数
        assertEquals(TEST_BUCKET, bucketCaptor.getValue());
        assertEquals(TEST_OBJECT_KEY, objectKeyCaptor.getValue());
        Map<String, String> capturedParams = paramsCaptor.getValue();
        assertEquals(expectedParams, capturedParams);
        
        // 验证方法调用
        verify(sysUploadTaskService, times(1)).getByIdentifier(TEST_IDENTIFIER);
        verify(sysUploadTaskService, times(1)).genPreSignUploadUrl(
            eq(TEST_BUCKET),
            eq(TEST_OBJECT_KEY),
            eq(expectedParams)
        );

        // 5. 合并分片
        doNothing().when(sysUploadTaskService).merge(TEST_IDENTIFIER, null);
        AjaxResult mergeResult = minioUploadTaskController.merge(TEST_IDENTIFIER, null);
        assertEquals(200, mergeResult.get("code"));
        verify(sysUploadTaskService, times(1)).merge(TEST_IDENTIFIER, null);

        // 6. 验证上传完成后的状态
        TaskInfoDTO completedTaskInfo = createMockTaskInfo(true);
        when(sysUploadTaskService.getTaskInfo(TEST_IDENTIFIER, "model")).thenReturn(completedTaskInfo);
        AjaxResult completedTaskResult = minioUploadTaskController.taskInfo(TEST_IDENTIFIER, "model");
        assertEquals(200, completedTaskResult.get("code"));
        TaskInfoDTO completedResultInfo = (TaskInfoDTO) completedTaskResult.get("data");
        assertTrue(completedResultInfo.isFinished());
    }

    @Test
    void testInitTaskValidationError() {
        InitTaskParam initTaskParam = new InitTaskParam();
        when(bindingResult.hasErrors()).thenReturn(true);
        FieldError fieldError = new FieldError("initTaskParam", "fileName", "文件名不能为空");
        when(bindingResult.getFieldError()).thenReturn(fieldError);

        AjaxResult result = minioUploadTaskController.initTask(initTaskParam, bindingResult);
        assertEquals(500, result.get("code"));
        assertEquals("文件名不能为空", result.get("msg"));
    }

    @Test
    void testPreSignUploadUrlWithNonExistentTask() {
        when(sysUploadTaskService.getByIdentifier(TEST_IDENTIFIER)).thenReturn(null);

        AjaxResult result = minioUploadTaskController.preSignUploadUrl(TEST_IDENTIFIER, 1, null);
        assertEquals(500, result.get("code"));
        assertEquals("分片任务不存在", result.get("msg"));
    }

    private TaskInfoDTO createMockTaskInfo(boolean finished) {
        TaskInfoDTO taskInfo = new TaskInfoDTO();
        taskInfo.setFinished(finished);
        
        TaskRecordDTO taskRecord = new TaskRecordDTO();
        taskRecord.setFileIdentifier(TEST_IDENTIFIER);
        taskRecord.setBucketName(TEST_BUCKET);
        taskRecord.setObjectKey(TEST_OBJECT_KEY);
        taskRecord.setUploadId(TEST_UPLOAD_ID);
        taskRecord.setFileName(TEST_FILENAME);
        taskRecord.setTotalSize(TEST_FILE_SIZE);
        taskRecord.setChunkSize(TEST_CHUNK_SIZE);
        taskRecord.setChunkNum((int) Math.ceil(TEST_FILE_SIZE.doubleValue() / TEST_CHUNK_SIZE));
        taskRecord.setFileType(TEST_FILE_TYPE);
        
        if (!finished) {
            // 模拟已上传的分片
            ArrayList<CustomPartSummary> partList = new ArrayList<>();
            CustomPartSummary part = new CustomPartSummary();
            part.setPartNumber(1);
            part.setSize(TEST_CHUNK_SIZE);
            partList.add(part);
            taskRecord.setExitPartList(partList);
        }
        
        taskInfo.setTaskRecord(taskRecord);
        taskInfo.setPath("https://minio-server/" + TEST_BUCKET + "/" + TEST_OBJECT_KEY);
        
        return taskInfo;
    }
} 