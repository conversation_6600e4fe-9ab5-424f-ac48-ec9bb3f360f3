<template>
  <div class="upload-file">
    <div class="upload-area" v-if="!disabled">
      <el-upload
        multiple
        :action="uploadFileUrl"
        :before-upload="handleBeforeUpload"
        :on-progress="handleProgress"
        :file-list="fileList"
        :data="data"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        :on-success="handleUploadSuccess"
        :show-file-list="false"
        :headers="headers"
        class="upload-file-uploader"
        ref="fileUpload"
      >
        <div class="upload-content">
          <el-icon class="upload-icon"><upload-filled /></el-icon>
          <div class="upload-text">点击或拖拽文件到此处上传</div>
          <div class="upload-tip" v-if="showTip">
            支持
            <template v-if="fileType">{{ fileType.join("/") }}</template>
            格式
            <template v-if="fileSize">，单个文件不超过 {{ fileSize }}MB</template>
          </div>
        </div>
      </el-upload>
    </div>

    <!-- 文件列表 -->
    <transition-group 
      ref="uploadFileList" 
      class="upload-file-list el-upload-list el-upload-list--text" 
      name="el-fade-in-linear" 
      tag="ul"
    >
      <li 
        :key="file.uid" 
        class="el-upload-list__item ele-upload-list__item-content" 
        v-for="(file, index) in fileList"
      >
        <el-link 
          :href="`${baseUrl}${file.url}`" 
          :underline="false" 
          target="_blank"
        >
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        
        <!-- 上传进度 -->
        <div v-if="file.status === 'uploading'" class="upload-progress">
          <el-progress 
            :percentage="file.percentage || 0" 
            :stroke-width="4" 
            :show-text="false"
          />
          <div class="progress-info">
            <span class="progress-text">{{ file.percentage || 0 }}%</span>
            <span class="progress-speed" v-if="file.speed">{{ file.speed }} MB/s</span>
            <span class="progress-time" v-if="file.remainingTime">剩余 {{ file.remainingTime }}</span>
          </div>
        </div>
        
        <div class="ele-upload-list__item-content-action" v-if="!disabled">
          <el-link 
            :underline="false" 
            @click="handleDelete(index)" 
            type="danger"
          >删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth"
import Sortable from 'sortablejs'
import { UploadFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: [String, Object, Array],
  // 上传接口地址
  action: {
    type: String,
    default: "/common/upload"
  },
  // 上传携带的参数
  data: {
    type: Object
  },
  // 数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "pdf"]
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  // 禁用组件（仅查看文件）
  disabled: {
    type: Boolean,
    default: false
  },
  // 拖动排序
  drag: {
    type: Boolean,
    default: true
  }
})

const { proxy } = getCurrentInstance()
const emit = defineEmits()
const number = ref(0)
const uploadList = ref([])
const baseUrl = import.meta.env.VITE_APP_BASE_API
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + props.action)
const headers = ref({ Authorization: "Bearer " + getToken() })
const fileList = ref([])
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
)

watch(() => props.modelValue, val => {
  if (val) {
    let temp = 1
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : props.modelValue.split(',')
    // 然后将数组转为对象数组
    fileList.value = list.map(item => {
      if (typeof item === "string") {
        item = { name: item, url: item }
      }
      item.uid = item.uid || new Date().getTime() + temp++
      return item
    })
  } else {
    fileList.value = []
    return []
  }
},{ deep: true, immediate: true })

// 更新文件上传进度
const updateFileProgress = (file, progress) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value[index] = {
      ...fileList.value[index],
      status: 'uploading',
      percentage: progress.percent,
      speed: progress.speed,
      remainingTime: progress.remainingTime
    }
  }
}

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.')
    const fileExt = fileName[fileName.length - 1]
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确，请上传${props.fileType.join("/")}格式文件!`)
      return false
    }
  }
  // 校检文件名是否包含特殊字符
  if (file.name.includes(',')) {
    proxy.$modal.msgError('文件名不正确，不能包含英文逗号!')
    return false
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`)
      return false
    }
  }
  
  // 添加文件到列表
  const fileInfo = {
    uid: file.uid,
    name: file.name,
    status: 'uploading',
    percentage: 0,
    speed: '0 MB/s',
    remainingTime: '计算中...'
  }
  fileList.value.push(fileInfo)
  
  number.value++
  return true
}

// 上传进度处理
function handleProgress(event, file) {
  const speed = (event.loaded / (Date.now() - file.startTime) * 1000 / 1024 / 1024).toFixed(2)
  const remainingTime = Math.ceil((file.size - event.loaded) / (speed * 1024 * 1024)) + 's'
  
  updateFileProgress(file, {
    percent: Math.round((event.loaded / event.total) * 100),
    speed: speed + ' MB/s',
    remainingTime
  })
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`)
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError("上传文件失败")
  proxy.$modal.closeLoading()
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    uploadList.value.push({ name: res.fileName, url: res.fileName })
    // 更新文件状态
    const index = fileList.value.findIndex(f => f.uid === file.uid)
    if (index > -1) {
      fileList.value[index] = {
        ...fileList.value[index],
        status: 'success',
        url: res.fileName
      }
    }
    uploadedSuccessfully()
  } else {
    number.value--
    proxy.$modal.closeLoading()
    proxy.$modal.msgError(res.msg)
    // 移除失败的文件
    const index = fileList.value.findIndex(f => f.uid === file.uid)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
    proxy.$refs.fileUpload.handleRemove(file)
    uploadedSuccessfully()
  }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1)
  emit("update:modelValue", listToString(fileList.value))
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value)
    uploadList.value = []
    number.value = 0
    emit("update:modelValue", listToString(fileList.value))
    proxy.$modal.closeLoading()
  }
}

// 获取文件名称
function getFileName(name) {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return name.slice(name.lastIndexOf("/") + 1)
  } else {
    return name
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = ""
  separator = separator || ","
  for (let i in list) {
    if (list[i].url) {
      strs += list[i].url + separator
    }
  }
  return strs != '' ? strs.substr(0, strs.length - 1) : ''
}

// 初始化拖拽排序
onMounted(() => {
  if (props.drag && !props.disabled) {
    nextTick(() => {
      const element = proxy.$refs.uploadFileList?.$el || proxy.$refs.uploadFileList
      Sortable.create(element, {
        ghostClass: 'file-upload-darg',
        onEnd: (evt) => {
          const movedItem = fileList.value.splice(evt.oldIndex, 1)[0]
          fileList.value.splice(evt.newIndex, 0, movedItem)
          emit('update:modelValue', listToString(fileList.value))
        }
      })
    })
  }
})
</script>
<style scoped lang="scss">
.upload-file {
  .upload-area {
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
    
    &:hover {
      border-color: #409eff;
      background-color: #f5f7fa;
    }
    
    .upload-icon {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 10px;
    }
    
    .upload-text {
      color: #606266;
      font-size: 14px;
      margin-bottom: 10px;
    }
  }
  
  .upload-file-uploader {
    margin-bottom: 10px;
  }
  
  .upload-file-list {
    .el-upload-list__item {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 8px 12px;
      margin-bottom: 8px;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
    
    .ele-upload-list__item-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .el-link {
        margin-right: 10px;
        font-size: 14px;
      }
    }
  }
  
  .upload-progress {
    margin-top: 10px;
    
    .progress-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 5px;
      
      .progress-text {
        font-size: 12px;
        color: #606266;
      }
      
      .progress-speed {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .file-upload-drag {
    opacity: 0.7;
    background: #e6f1fc;
    border: 1px dashed #409eff;
  }
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  
  b {
    color: #f56c6c;
    font-weight: normal;
  }
}
</style>
