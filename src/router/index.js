import { createWebHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    hidden: true,
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true, fullScreen: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      },
      {
        path: 'role-time/:userId(\\d+)',
        component: () => import('@/views/system/user/authRoleTime'),
        name: 'AuthRoleTime',
        meta: { title: '分配角色（有效期）', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  },
  //此处路由配置内容可以去除，并不生效，真正路由通过后台配置
  {
    path: '/ai/model-conversion',
    component: Layout,
    permissions: ['ai:modelConversion:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/ai/modelConversion/index'),
        name: 'ModelConversion',
        meta: { title: '模型转换', icon: 'model' }
      }
    ]
  },
  {
    path: '/ai/document',
    component: Layout,
    permissions: ['ai:document:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/ai/document/index'),
        name: 'LFNNDocument',
        meta: { title: 'LFNN使用文档', icon: 'documentation' }
      }
    ]
  },
  {
    path: '/ai/modelZoo',
    component: Layout,
    permissions: ['ai:modelZooManager:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/ai/modelZoo/index'),
        name: 'ModelZoo',
        meta: { title: 'ModelZoo', icon: 'model' }
      }
    ]
  },
  {
    path: '/ai/lfnn',
    component: Layout,
    permissions: ['ai:lfnn:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/ai/lfnn/index'),
        name: 'LfnnDownload',
        meta: { title: 'LFNN下载', icon: 'download' }
      }
    ]
  },
  {
    path: '/ai/trainEngineer',
    component: Layout,
    permissions: ['ai:trainEngineer:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/ai/trainEngineer/index'),
        name: 'TrainEngineer',
        meta: { title: '训练工程下载', icon: 'download' }
      }
    ]
  },
  {
    path: '/report',
    component: Layout,
    name: 'Report',
    meta: { title: '数据报表', icon: 'chart' },
    permissions: ['report:view'],
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/report/dashboard/index'),
        name: 'ReportDashboard',
        meta: { title: '统计仪表盘', icon: 'dashboard' },
        permissions: ['report:dashboard:view']
      }
    ]
  },
  {
    path: '/admin',
    component: Layout,
    name: 'Admin',
    meta: { title: '后台管理', icon: 'setting' },
    permissions: ['admin:manage'],
    children: [
      {
        path: 'file',
        component: () => import('@/views/admin/file/index'),
        name: 'AdminFile',
        meta: { title: '文件管理', icon: 'folder' },
        permissions: ['admin:file:list']
      },
      {
        path: 'fileType',
        component: () => import('@/views/admin/fileType/index'),
        name: 'AdminFileType',
        meta: { title: '文件类型管理', icon: 'file' },
        permissions: ['admin:fileType:list']
      },
      {
        path: 'aiHost',
        component: () => import('@/views/admin/host/index'),
        name: 'AiHost',
        meta: { title: 'AI任务主机管理', icon: 'server' },
        permissions: ['admin:aiHost:list']
      },
      {
        path: 'modelConversionLog',
        component: () => import('@/views/admin/modelConversionLog/index'),
        name: 'ModelConversionLog',
        meta: { title: '模型转换日志', icon: 'log' },
        permissions: ['admin:modelConversionLog:list']
      },
      {
        path: 'modelZooManager',
        component: () => import('@/views/admin/modelZooManager/index'),
        name: 'modelZooManager',
        meta: { title: 'modelZooManager', icon: 'model' },
        permissions: ['admin:modelZooManager:list']
      },
      {
        path: 'downloadRecord',
        component: () => import('@/views/admin/downloadRecord/index'),
        name: 'DownloadRecord',
        meta: { title: '下载记录', icon: 'download' },
        permissions: ['admin:lfnnVersion:list']
      },
      {
        path: 'lfnnVersion',
        component: () => import('@/views/admin/lfnnVersion/index'),
        name: 'LfnnVersion',
        meta: { title: 'LFNN版本下载管理', icon: 'download' },
        permissions: ['admin:lfnnVersion:list']
      },
      {
        path: 'config',
        component: () => import('@/views/admin/config/index'),
        name: 'AdminConfig',
        meta: { title: '参数设置', icon: 'setting' },
        permissions: ['system:config:list']
      },
      {
        path: 'reportDashboard',
        component: () => import('@/views/report/dashboard/index'),
        name: 'ReportDashboard',
        meta: { title: '报表总览', icon: 'chart' },
        permissions: ['admin:report:list']
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(), 
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})

export default router
