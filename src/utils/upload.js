import SparkMD5 from 'spark-md5'
import axios from 'axios'
import { getPreSignedUrl, createUploadTask, mergeChunks, getUploadProgress, batchCreateUploadTask } from '@/api/ai/file'
import { ElNotification } from 'element-plus'
import Queue from 'promise-queue-plus'
import { ref } from 'vue'

const DEFAULT_CHUNK_SIZE = 20 * 1024 * 1024 // 20MB，与后端保持一致
const MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024 // 2GB 文件大小限制
const SPARK_MD5_CHUNK_SIZE = 500 * 1024 * 1024 // 2MB，用于MD5计算的分块大小

// 文件上传分块任务的队列（用于移除文件时，停止该文件的上传队列） key：fileUid value： queue object
const fileUploadChunkQueue = ref({}).value

export class FileUploader {
  constructor(file, identifier = null) {
    this.file = file
    this.fileName = file.name
    this.chunkSize = DEFAULT_CHUNK_SIZE
    this.chunks = Math.ceil(file.size / DEFAULT_CHUNK_SIZE)
    this.uploadedSize = 0
    this.startMs = Date.now()
    this.identifier = identifier
    this.uid = file.uid || Math.random().toString(36).slice(2)
  }

  // 计算文件的MD5
  async calculateMD5(onProgress = () => {}) {
    // 检查文件大小
    if (this.file.size > MAX_FILE_SIZE) {
      throw new Error(`文件大小超过${MAX_FILE_SIZE / 1024 / 1024 / 1024}GB限制`)
    }

    // 对于小文件，直接计算MD5
    if (this.file.size < SPARK_MD5_CHUNK_SIZE) {
      onProgress({ percent: 50, status: 'preparing', message: '正在计算文件标识...' })
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsArrayBuffer(this.file)
        reader.onload = (e) => {
          const spark = new SparkMD5.ArrayBuffer()
          spark.append(e.target.result)
          onProgress({ percent: 100, status: 'preparing', message: '文件标识计算完成' })
          resolve(spark.end())
        }
        reader.onerror = reject
      })
    }
    
    // 对于大文件，分块计算MD5
    return new Promise((resolve, reject) => {
      const chunks = Math.ceil(this.file.size / SPARK_MD5_CHUNK_SIZE)
      const spark = new SparkMD5.ArrayBuffer()
      let currentChunk = 0

      const processNextChunk = () => {
        if (currentChunk < chunks) {
          const start = currentChunk * SPARK_MD5_CHUNK_SIZE
          const end = Math.min(start + SPARK_MD5_CHUNK_SIZE, this.file.size)
          const chunk = this.file.slice(start, end)

          const reader = new FileReader()
          reader.readAsArrayBuffer(chunk)
          reader.onload = (e) => {
            spark.append(e.target.result)
            currentChunk++

            // 报告MD5计算进度
            const progress = Math.floor((currentChunk / chunks) * 100)
            onProgress({
              percent: progress,
              status: 'preparing',
              message: `正在计算文件标识... ${progress}%`
            })

            processNextChunk()
          }
          reader.onerror = reject
        } else {
          onProgress({ percent: 100, status: 'preparing', message: '文件标识计算完成' })
          resolve(spark.end())
        }
      }

      // 开始计算MD5
      onProgress({ percent: 1, status: 'preparing', message: '正在计算文件标识...' })
      processNextChunk()
    })
  }

  // 获取上传速度（byte/s）
  getSpeed() {
    const intervalTime = (Date.now() - this.startMs) / 1000
    return intervalTime > 0 ? this.uploadedSize / intervalTime : 0
  }

  // 更新上传进度
  updateProgress(increment, onProgress) {
    this.uploadedSize += increment
    const percent = Math.round((this.uploadedSize * 100) / this.file.size)

    // 计算上传速度和剩余时间
    const speed = this.getSpeed()
    const remainingSize = this.file.size - this.uploadedSize
    const remainingTime = speed > 0 ? Math.ceil(remainingSize / speed) : 0

    onProgress({
      percent,
      speed: (speed / 1024 / 1024).toFixed(2), // MB/s
      remainingTime: remainingTime ? `${remainingTime}s` : '未知'
    })
  }

  // 上传单个分片
  async uploadChunk(identifier, partNumber, fileType, existPartList = []) {
    // 检查分片是否已上传
    const existPart = existPartList.find(part => part.partNumber === partNumber)
    if (existPart) {
      this.updateProgress(existPart.size)
      return { partNumber, size: existPart.size }
    }

    // 计算分片范围
    const start = (partNumber - 1) * this.chunkSize
    const end = Math.min(start + this.chunkSize, this.file.size)
    const chunk = this.file.slice(start, end)

    // 获取预签名URL并上传
    const { data: preSignedUrl } = await getPreSignedUrl(identifier, partNumber, fileType)
    await axios.put(preSignedUrl, chunk, {
      headers: { 'Content-Type': 'application/octet-stream' }
    })

    return { partNumber, size: chunk.size }
  }

  /**
   * 停止文件上传
   */
  stop() {
    const queue = fileUploadChunkQueue[this.uid]
    if (queue) {
      queue.stop()
      fileUploadChunkQueue[this.uid] = undefined
    }
  }

  /**
   * 上传逻辑处理，如果文件已经上传完成（完成分块合并操作），则不会进入到此方法中
   */
  handleUpload = async (file, taskRecord, fileType, options) => {
    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      return Promise.reject(new Error(`文件大小超过${MAX_FILE_SIZE / 1024 / 1024 / 1024}GB限制`))
    }
    
    let lastUploadedSize = 0; // 上次断点续传时上传的总大小
    let uploadedSize = 0 // 已上传的大小
    const totalSize = file.size || 0 // 文件总大小
    let startMs = new Date().getTime(); // 开始上传的时间
    const { exitPartList, chunkSize, chunkNum, fileIdentifier } = taskRecord
    const identifier = fileIdentifier

    // 获取从开始上传到现在的平均速度（byte/s）
    const getSpeed = () => {
      // 已上传的总大小 - 上次上传的总大小（断点续传）= 本次上传的总大小（byte）
      const intervalSize = uploadedSize - lastUploadedSize
      const nowMs = new Date().getTime()
      // 时间间隔（s）
      const intervalTime = (nowMs - startMs) / 1000
      return intervalTime > 0 ? intervalSize / intervalTime : 0
    }

    const uploadNext = async (partNumber) => {
      const start = new Number(chunkSize) * (partNumber - 1)
      const end = Math.min(start + new Number(chunkSize), file.size)
      const blob = file.slice(start, end)
      const { code, data, msg } = await getPreSignedUrl(identifier, partNumber, fileType)
      if (code === 200 && msg) {
        await axios.request({
          url: msg,
          method: 'PUT',
          data: blob,
          headers: { 'Content-Type': 'application/octet-stream' }
        })
        return Promise.resolve({ partNumber: partNumber, uploadedSize: blob.size })
      }
      return Promise.reject(`分片${partNumber}， 获取上传地址失败`)
    }

    /**
     * 更新上传进度
     * @param increment 为已上传的进度增加的字节量
     */
    const updateProcess = (increment) => {
      increment = new Number(increment)
      const { onProgress } = options
      let factor = 1000; // 每次增加1000 byte
      let from = 0;
      // 通过循环一点一点的增加进度
      while (from <= increment) {
        from += factor
        uploadedSize += factor
        const percent = Math.round(uploadedSize / totalSize * 100).toFixed(2);
        const speed = getSpeed();
        const remainingTime = speed != 0 ? Math.ceil((totalSize - uploadedSize) / speed) + 's' : '未知'
        onProgress({
          percent: percent,
          speed: (speed / 1024 / 1024).toFixed(2), // MB/s
          remainingTime
        })
      }
    }

    return new Promise(resolve => {
      const failArr = [];
      const queue = Queue(5, {
        "retry": 3,               //Number of retries
        "retryIsJump": false,     //retry now?
        "workReject": function(reason,queue){
          failArr.push(reason)
        },
        "queueEnd": function(queue){
          resolve(failArr);
        }
      })
      fileUploadChunkQueue[this.uid] = queue
      for (let partNumber = 1; partNumber <= chunkNum; partNumber++) {
        const exitPart = (exitPartList || []).find(exitPart => exitPart.partNumber == partNumber)
        if (exitPart) {
          // 分片已上传完成，累计到上传完成的总额中,同时记录一下上次断点上传的大小，用于计算上传速度
          lastUploadedSize += new Number(exitPart.size)
          updateProcess(exitPart.size)
        } else {
          queue.push(() => uploadNext(partNumber).then(res => {
            // 单片文件上传完成再更新上传进度
            updateProcess(res.uploadedSize)
          }))
        }
      }
      if (queue.getLength() == 0) {
        // 所有分片都上传完，但未合并，直接return出去，进行合并操作
        resolve(failArr);
        return;
      }
      queue.start()
    })
  }

  // 执行上传流程
  async upload(fileType, onProgress = () => {}, skipTaskCreation = false) {
    try {
      // 立即显示进度条，让用户知道上传已开始
      onProgress({
        percent: 1,
        speed: '0',
        remainingTime: '正在准备上传...',
        status: 'primary'
      })

      // 检查文件大小
      if (this.file.size > MAX_FILE_SIZE) {
        throw new Error(`文件大小超过${MAX_FILE_SIZE / 1024 / 1024 / 1024}GB限制`)
      }

      // 1. 获取或计算文件MD5
      const identifier = this.identifier || await this.calculateMD5((progress) => {
        // 在MD5计算过程中更新进度
        onProgress({
          percent: Math.min(progress.percent, 99), // 确保不超过99%，为后续步骤留空间
          speed: '0',
          remainingTime: progress.message || '正在准备上传...',
          status: 'primary'
        })
      })

      // 2. 根据文件类型决定是否先检查上传任务是否存在
      let task = null;
      
      // 只有图片类型才先检查任务是否存在
      if (fileType === 'image') {
        task = await getUploadProgress(identifier, fileType)

        // 如果任务已完成，直接返回
        if (task && task.data && task.data.finished) {
          onProgress({ percent: 100 })
          return identifier
        }
      }

      // 如果不是图片或任务不存在且未跳过创建，则创建上传任务
      if ((fileType !== 'image' || !task || !task.data) && !skipTaskCreation) {
        await createUploadTask({
          identifier,
          fileName: this.fileName,
          totalSize: this.file.size,
          chunkSize: this.chunkSize,
          fileType
        })
        task = await getUploadProgress(identifier, fileType)
      }

      // 如果任务已完成，直接返回
      if (task && task.data && task.data.finished) {
        onProgress({ percent: 100 })
        return identifier
      }

      // 3. 使用handleUpload处理分片上传
      const { data: { taskRecord } } = task
      const errorList = await this.handleUpload(this.file, taskRecord, fileType, {
        onProgress: (progress) => {
          onProgress({
            percent: progress.percent,
            speed: progress.speed,
            remainingTime: progress.remainingTime
          })
        }
      })

      if (errorList && errorList.length > 0) {
        throw new Error('部分分片上传失败，请重试')
      }

      // 4. 合并分片
      await mergeChunks(identifier, fileType)
      onProgress({ percent: 100 })

      return identifier
    } catch (error) {
      throw new Error(errorMessage)
    } finally {
      // 清理上传队列
      fileUploadChunkQueue[this.uid] = undefined
    }
  }

  // 批量上传文件
  static async batchUpload(files, fileType = 'image', onProgress = () => {}) {
    try {
      // 检查文件大小
      for (const file of files) {
        if (file.size > MAX_FILE_SIZE) {
          throw new Error(`文件 ${file.name} 大小超过${MAX_FILE_SIZE / 1024 / 1024 / 1024}GB限制`)
        }
      }
      
      // 1. 为每个文件计算MD5并创建上传器
      const fileInfos = await Promise.all(files.map(async (file) => {
        const md5 = await new FileUploader(file).calculateMD5()
        return {
          identifier: md5,
          fileName: file.name,
          totalSize: file.size,
          chunkSize: DEFAULT_CHUNK_SIZE,
          fileType
        }
      }))

      // 2. 批量创建上传任务
      await batchCreateUploadTask({ files: fileInfos })

      // 3. 创建上传器并执行每个文件的上传（使用已计算的MD5，跳过任务创建）
      const uploaders = files.map((file, index) =>
          new FileUploader(file, fileInfos[index].identifier)
      )

      const results = await Promise.all(uploaders.map(async (uploader, index) => {
        try {
          const identifier = await uploader.upload(fileType, (progress) => {
            onProgress({
              fileName: uploader.fileName,
              ...progress,
              index
            })
          }, true)

          return {
            fileName: uploader.fileName,
            identifier,
            status: 'success'
          }
        } catch (error) {
          console.error(`上传失败: ${uploader.fileName}`, error)
          return {
            fileName: uploader.fileName,
            status: 'error',
            error: error.message
          }
        }
      }))

      return results
    } catch (error) {
      // const errorMessage = error.message || '批量上传失败'
      // ElNotification.error({
      //   title: '批量上传失败',
      //   message: errorMessage
      // })
      // throw new Error(errorMessage)
    }
  }
} 