import { ref } from 'vue'

export default function useWebSocket(options) {
  const {
    url,
    onMessage,
    onError,
    onOpen,
    onClose,
    reconnectLimit = 3,  // 最大重连次数
    heartBeatInterval = 30000  // 心跳间隔，默认30秒
  } = options

  const ws = ref(null)
  const isConnected = ref(false)
  let reconnectCount = 0
  let heartbeatTimer = null
  let reconnectTimer = null

  // 获取完整的WebSocket URL
  const getFullUrl = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = window.location.host.split(':')[0] // 移除可能存在的端口信息
    // 从当前页面URL中获取上下文路径
    const contextPath = window.location.pathname.split('/')[1] || ''
    const basePath = contextPath ? `/${contextPath}` : ''
    return `${protocol}//${host}:8080${basePath}${url.startsWith('/') ? url : `/${url}`}`
  }

  // 开始心跳
  const startHeartbeat = () => {
    stopHeartbeat() // 确保之前的心跳被清除
    heartbeatTimer = setInterval(() => {
      if (ws.value?.readyState === WebSocket.OPEN) {
        try {
          ws.value.send('ping')
        } catch (error) {
          console.error('发送心跳失败:', error)
          stopHeartbeat()
          ws.value?.close()
        }
      } else {
        stopHeartbeat()
      }
    }, heartBeatInterval)
  }

  // 停止心跳
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  // 重连
  const reconnect = () => {
    if (reconnectCount >= reconnectLimit) {
      console.log('WebSocket重连次数超过限制，停止重连')
      onError?.({ message: '连接失败次数过多，请刷新页面重试' })
      return
    }

    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
    }

    reconnectTimer = setTimeout(() => {
      console.log(`WebSocket第${reconnectCount + 1}次重连...`)
      reconnectCount++
      connect()
    }, 3000 * (reconnectCount + 1)) // 重连间隔随次数增加
  }

  // 连接WebSocket
  const connect = () => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      console.log('WebSocket已经连接')
      return
    }

    // 确保之前的连接被完全清理
    disconnect()

    try {
      const wsUrl = getFullUrl()
      console.log('正在连接WebSocket:', wsUrl)
      ws.value = new WebSocket(wsUrl)

      ws.value.onopen = () => {
        console.log('WebSocket连接成功')
        isConnected.value = true
        reconnectCount = 0 // 重置重连次数
        startHeartbeat() // 开始心跳
        onOpen?.()
      }

      ws.value.onmessage = (event) => {
        try {
          // 如果是心跳响应，不处理
          if (event.data === 'pong') {
            return
          }
          onMessage?.(event.data)
        } catch (error) {
          console.error('处理消息失败:', error)
        }
      }

      ws.value.onerror = (error) => {
        console.error('WebSocket错误:', error)
        isConnected.value = false
        onError?.(error)
        ws.value?.close() // 确保连接被关闭
      }

      ws.value.onclose = (event) => {
        console.log('WebSocket连接关闭, code:', event.code, 'reason:', event.reason)
        isConnected.value = false
        stopHeartbeat()
        onClose?.()
        
        // 非正常关闭时才重连
        if (event.code !== 1000) {
          reconnect()
        }
      }
    } catch (error) {
      console.error('创建WebSocket连接失败:', error)
      isConnected.value = false
      onError?.(error)
      reconnect()
    }
  }

  // 断开WebSocket连接
  const disconnect = () => {
    stopHeartbeat()
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    if (ws.value) {
      // 标记为正常关闭
      const socket = ws.value
      ws.value = null
      isConnected.value = false
      try {
        socket.close(1000, 'Normal closure')
      } catch (error) {
        console.error('关闭WebSocket连接失败:', error)
      }
    }
  }

  // 发送消息
  const send = (message) => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      try {
        ws.value.send(typeof message === 'string' ? message : JSON.stringify(message))
        return true
      } catch (error) {
        console.error('发送消息失败:', error)
        return false
      }
    } else {
      console.error('WebSocket未连接，无法发送消息')
      return false
    }
  }

  // 返回实例方法和状态
  return {
    isConnected,
    connect,
    disconnect,
    send,
    // 暴露重连方法，以便外部在需要时手动重连
    reconnect: () => {
      reconnectCount = 0 // 重置重连次数
      connect()
    }
  }
} 