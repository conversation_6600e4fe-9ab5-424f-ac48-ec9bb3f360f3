<template>
  <div class="app-container">
    <div class="lfnn-download">
      <div class="header-container">
        <div class="header-left">
          <h1 class="title">LFNN SDK 下载 </h1>
          <p class="subtitle">下载完整的LFNN SDK版本包，支撑项目开发 </p>
        </div>
        <div class="header-right">
          <div class="pagination-container">
            <el-button 
              :disabled="currentPage <= 1" 
              @click="handlePrevPage" 
              class="pagination-btn"
            >
              <el-icon><ArrowLeft /></el-icon> 上一页
            </el-button>
            <span class="page-info">{{ currentPage }}/{{ totalPages }}</span>
            <el-button 
              :disabled="currentPage >= totalPages" 
              @click="handleNextPage" 
              class="pagination-btn"
            >
              下一页 <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <el-input
            v-model="chipTypeFilter"
            placeholder="输入芯片类型筛选"
            class="chip-type-filter"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select 
            v-model="selectedTransferTool" 
            placeholder="请选择模型转换工具" 
            class="transfer-tool-select"
            clearable
            filterable
            :disabled="!checkPermi(['ai:transfer:download'])"
          >
            <el-option 
              v-for="item in transferToolList" 
              :key="item.id" 
              :label="item.fileName" 
              :value="item.fileIdentifier"
            />
          </el-select>
          <el-button 
            type="primary" 
            @click="handleTransferToolDownload" 
            :disabled="!checkPermi(['ai:transfer:download']) || !selectedTransferTool"
            class="transfer-tool-btn"
          >
            <el-icon><Download /></el-icon> 模型转换工具下载
          </el-button>
        </div>
      </div>

      <div class="version-list">
        <div v-for="(version, index) in filteredVersionList" :key="index" class="version-item">
          <div class="version-card" :class="{ 'latest-version': isLatestVersion(version) }">
            <div class="version-header">
              <div class="version-info-top">
                <div class="version-filename" :title="version.lfnnFileName">
                  <el-icon><Document /></el-icon>
                  {{ version.lfnnFileName }}
                </div>
                <div class="version-name-container">
                  <span class="version-name">{{ version.lfnnVersion }}</span>
                  <span v-if="isLatestVersion(version)" class="version-tag">最新版本</span>
                  <span v-else class="version-tag history">历史版本</span>
                </div>
              </div>
            </div>
            
            <div class="divider"></div>
            
            <div class="version-desc">{{ version.lfnnRemark }}</div>
            
            <div class="version-footer">
              <div class="version-info">
                <span class="version-date">
                  <el-icon><Clock /></el-icon> 更新时间: {{ parseTime(version.createTime) }}
                </span>
                <span class="version-downloads">
                  <el-icon><Download /></el-icon> 下载次数: {{ version.downloadCount }}
                </span>
                <span class="version-chip-type">
                  <el-icon><Cpu /></el-icon> 芯片类型: {{ version.chipType || '通用' }}
                </span>
              </div>
              <div class="version-action">
                <el-button 
                  type="primary" 
                  @click="handleDownload(version)" 
                  class="download-btn"
                  :disabled="!checkPermi(['ai:lfnnVersion:download'])"
                >
                  <el-icon><Download /></el-icon> 下载
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="version-info-section">
        <h2 class="section-title">版本说明</h2>
        <div class="info-items-row">
          <div class="info-item">
            <div class="info-icon blue"><el-icon><InfoFilled /></el-icon></div>
            <div class="info-content">
              <h3>版本命名规则</h3>
              <p>LFNN-V[主版本号]-[次版本号]-[发布日期]-R[发布次数]</p>
            </div>
          </div>
          <div class="info-item">
            <div class="info-icon cyan"><el-icon><Monitor /></el-icon></div>
            <div class="info-content">
              <h3>系统要求</h3>
              <p>支持 Windows 10/11、Linux、MacOS 等主流操作系统</p>
            </div>
          </div>
          <div class="info-item">
            <div class="info-icon purple"><el-icon><Document /></el-icon></div>
            <div class="info-content">
              <h3>相关文档</h3>
              <div class="doc-links">
                <a href="javascript:void(0);" class="doc-link" @click="goToDocument">
                  <el-icon><Document /></el-icon> 使用文档
                </a>
                <!-- <a href="javascript:void(0);" class="doc-link">
                  <el-icon><QuestionFilled /></el-icon> 常见问题
                </a> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="LfnnDownload">
import { ref, onMounted, computed, watch, onActivated } from 'vue'
import { listLfnnVersion, downloadLfnnVersion } from '@/api/ai/lfnnVersion'
import { getModelTransferDownloadUrl } from '@/api/ai/transfer'
import { listFile } from '@/api/admin/file'
import { parseTime } from '@/utils/ruoyi'
import { ElNotification, ElLoading } from 'element-plus'
import { useRouter } from 'vue-router'
import { checkPermi } from '@/utils/permission'
import { addAccessRecord } from '@/api/report/index'
import { 
  Clock, 
  Download, 
  InfoFilled, 
  Monitor, 
  Document, 
  Tickets, 
  QuestionFilled,
  Cpu,
  Search,
  ArrowLeft,
  ArrowRight
} from '@element-plus/icons-vue'

const versionList = ref([])
const loading = ref(false)
const router = useRouter()
const transferToolList = ref([]) // 模型转换工具列表
const selectedTransferTool = ref('') // 选中的模型转换工具的fileIdentifier
const chipTypeFilter = ref('') // 芯片类型筛选
const currentPage = ref(1)
const totalPages = ref(1)

// 获取版本列表
const getVersionList = async () => {
  loading.value = true
  try {
    const res = await listLfnnVersion({
      pageNum: currentPage.value,
      pageSize: 6,
      chipType: chipTypeFilter.value || undefined
    })
    // 根据接口返回的数据结构进行处理
    if (res.code === 200) {
      versionList.value = res.rows || []
      // 服务端分页后，直接用total计算总页数
      totalPages.value = Math.ceil(res.total / 6) || 1
    } else {
      versionList.value = []
      totalPages.value = 1
      ElNotification.warning({
        title: '警告',
        message: '未获取到版本数据'
      })
    }
  } catch (error) {
    console.error('获取LFNN版本列表失败', error)
    ElNotification.error({
      title: '错误',
      message: '获取版本列表失败，请稍后重试'
    })
  } finally {
    loading.value = false
  }
}

// 获取模型转换工具列表
const getTransferToolList = async () => {
  try {
    const res = await listFile({
      pageNum: 1,
      pageSize: 20,
      fileType: 'model-transfer-tool'
    })
    transferToolList.value = res.rows || []
    // 如果列表不为空且有默认工具标识符，则选中它
    if (transferToolList.value.length > 0) {
      // 默认选择第一个工具
      selectedTransferTool.value = transferToolList.value[0].fileIdentifier
    }
  } catch (error) {
    console.error('获取模型转换工具列表失败', error)
    ElNotification.error({
      title: '错误',
      message: '获取模型转换工具列表失败'
    })
  }
}

// 处理下载
const handleDownload = (version) => {
  if (!version.lfnnIdentifier) {
    ElNotification.warning({
      title: '警告',
      message: '下载标识不存在，无法下载'
    })
    return
  }
  
  const loading = ElLoading.service({
    lock: true,
    text: '获取下载地址中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  
  downloadLfnnVersion(version.lfnnIdentifier).then(response => {
    if (response.code === 200 && response.msg) {
      // 创建一个隐藏的a标签用于下载
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = response.msg;
      link.setAttribute('download', version.lfnnFileName); 
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElNotification.success({
        title: '成功',
        message: '下载成功'
      });
    } else {
      ElNotification.error({
        title: '错误',
        message: '获取下载地址失败'
      });
    }
  }).catch(error => {
    console.error('下载失败:', error);
    ElNotification.error({
      title: '错误',
      message: '下载失败，请稍后重试'
    });
  }).finally(() => {
    loading.close();
  });
}

// 处理模型转换工具下载
const handleTransferToolDownload = () => {
  if (!selectedTransferTool.value) {
    ElNotification.warning({
      title: '警告',
      message: '请先选择要下载的模型转换工具'
    })
    return
  }
  
  const loading = ElLoading.service({
    lock: true,
    text: '获取下载地址中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  
  getModelTransferDownloadUrl(selectedTransferTool.value, 3600, 'model-transfer-tool').then(response => {
    if (response.code === 200 && response.data) {
      // 创建一个隐藏的a标签用于下载
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = response.data;
      // 从选中的工具列表中找到对应的文件名
      const selectedTool = transferToolList.value.find(item => item.fileIdentifier === selectedTransferTool.value);
      const fileName = selectedTool ? selectedTool.fileName : 'model-transfer-tool.zip';
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElNotification.success({
        title: '成功',
        message: '下载成功'
      });
    } else {
      ElNotification.error({
        title: '错误',
        message: '获取下载地址失败'
      });
    }
  }).catch(error => {
    console.error('下载失败:', error);
    ElNotification.error({
      title: '错误',
      message: '下载失败，请稍后重试'
    });
  }).finally(() => {
    loading.close();
  });
}

// 跳转到LFNN使用文档页面
const goToDocument = () => {
  router.push('/ai/document/index')
}

// 过滤版本列表
const filteredVersionList = computed(() => {
  // 分页和筛选已由服务端处理
  return versionList.value
});

onMounted(() => {
  // 记录页面访问
  recordPageAccess()
  getVersionList()
  getTransferToolList()
})

// 添加onActivated生命周期钩子
onActivated(() => {
  // 记录页面访问
  recordPageAccess()
})

// 记录页面访问
const recordPageAccess = async () => {
  try {
    await addAccessRecord({
      accessType: 'lfnn',
      accessCount: 1
    }).catch(err => {
      console.warn('LFNN页面访问记录添加失败，可能是防重复提交导致:', err)
      // 忽略错误，不影响页面正常加载
    })
    console.log('LFNN页面访问记录添加成功')
  } catch (error) {
    console.warn('LFNN页面访问记录添加失败:', error)
    // 忽略错误，不影响页面正常加载
  }
}

// 监听芯片类型筛选值变化
watch(chipTypeFilter, () => {
  // 当筛选条件变化时，重置为第一页并重新获取数据
  currentPage.value = 1;
  getVersionList();
});

// 监听当前页码变化
watch(currentPage, () => {
  getVersionList();
});

// 判断是否为最新版本
const isLatestVersion = (version) => {
  if (versionList.value.length > 0) {
    // 原始列表中的第一个版本（按创建时间排序后）就是最新版本
    return versionList.value[0].lfnnIdentifier === version.lfnnIdentifier;
  }
  return false;
}

const handlePrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const handleNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}
</script>

<style scoped>
.app-container {
  background-color: #fff;
  min-height: calc(100vh - 84px);
  padding: 20px 80px;
}

.lfnn-download {
  max-width: 100%;
  margin: 0 auto;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  margin-bottom: 18px;
}

.pagination-btn {
  padding: 8px 15px;
  font-size: 12px;
}

.page-info {
  margin: 0 10px;
  font-size: 14px;
  color: #606266;
  min-width: 40px;
  text-align: center;
}

.chip-type-filter {
  margin-right: 10px;
  width: 220px;
}

.transfer-tool-select {
  margin-right: 10px;
  width: 220px;
}

.transfer-tool-btn {
  font-weight: 500;
}

.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.subtitle {
  font-size: 14px;
  color: #606266;
  margin-bottom: 0;
}

.version-list {
  margin-bottom: 40px;
}

.version-item {
  margin-bottom: 16px;
}

.version-card {
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 21, 41, 0.15);
  background-color: #fff;
  border-left: 4px solid #909399;
  position: relative;
  transition: all 0.3s ease;
}

.version-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 21, 41, 0.2);
}

.version-card.latest-version {
  border-left: 4px solid var(--el-color-primary);
}

.version-header {
  margin-bottom: 15px;
}

.version-info-top {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.version-filename {
  font-size: 18px;
  color: #303133;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: flex;
  align-items: center;
  font-weight: bold;
}

.version-filename .el-icon {
  margin-right: 5px;
  font-size: 16px;
  color: #303133;
}

.version-name-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.version-name {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.version-tag {
  background-color: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 10px;
}

.version-tag.history {
  background-color: #909399;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 15px 0;
}

.version-desc {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.6;
  font-size: 14px;
  padding: 0 5px;
}

.version-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version-info {
  display: flex;
  color: #909399;
  font-size: 13px;
}

.version-info .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.version-date {
  margin-right: 24px;
  display: flex;
  align-items: center;
}

.version-downloads {
  display: flex;
  align-items: center;
}

.version-action {
  text-align: right;
}

.download-btn {
  padding: 8px 20px;
  font-size: 14px;
  border-radius: 4px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.version-info-section {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 21, 41, 0.15);
  transition: all 0.3s ease;
}

.version-info-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 21, 41, 0.2);
}

.info-items-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  min-height: 100px;
}

.info-item {
  display: flex;
  margin-bottom: 0;
  flex: 1;
  min-width: 250px;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: #fff;
  font-size: 18px;
  flex-shrink: 0;
}

.info-icon.blue {
  background-color: var(--el-color-primary);
}

.info-icon.cyan {
  background-color: var(--el-color-primary-light-5);
}

.info-icon.purple {
  background-color: var(--el-color-primary-light-3);
}

.info-content h3 {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: #303133;
  font-weight: 500;
}

.info-content p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

.doc-links {
  display: flex;
  flex-wrap: wrap;
}

.doc-link {
  display: inline-flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 8px;
  color: var(--el-color-primary);
  text-decoration: none;
  font-size: 14px;
}

.doc-link .el-icon {
  margin-right: 4px;
  font-size: 16px;
}

.doc-link:hover {
  color: var(--el-color-primary-light-3);
}

.version-chip-type {
  display: flex;
  align-items: center;
}

.version-downloads, .version-date {
  margin-right: 24px;
}

@media (max-width: 768px) {
  .app-container {
    padding: 15px 20px;
  }
  
  .header-right {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  .pagination-container {
    margin-right: 0;
    margin-bottom: 10px;
    width: 100%;
    justify-content: space-between;
  }
  
  .chip-type-filter {
    margin-right: 0;
    margin-bottom: 10px;
    width: 100%;
  }
  
  .transfer-tool-select {
    margin-right: 0;
    margin-bottom: 10px;
    width: 100%;
  }
  
  .transfer-tool-btn {
    width: 100%;
  }
  
  .version-name-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .version-tag {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .version-footer {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .version-info {
    flex-direction: column;
    margin-bottom: 15px;
  }
  
  .version-date, .version-downloads, .version-chip-type {
    margin-right: 0;
    margin-bottom: 8px;
  }
  
  .version-action {
    width: 100%;
    text-align: center;
  }
  
  .download-btn {
    width: 100%;
  }
  
  .version-filename {
    max-width: 100%;
  }
}
</style> 