<template>
  <div class="app-container">
    <!-- 标题和描述 -->
    <div class="model-zoo-header">
      <h1 class="model-zoo-title">Model Zoo</h1>
      <p class="model-zoo-description"> 列举部分已支持模型的性能和精度数据，方便快速支持方案评估，同时提供完整的推理工程下载，方便快速的进行项目开发</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="model-zoo-search-container">
      <div class="model-zoo-search-wrapper">
        <!-- 搜索框 -->
        <el-input
          v-model="queryParams.modelName"
          placeholder="搜索模型..."
          clearable
          class="search-input"
          @keyup.enter="handleQuery"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <!-- 芯片类型输入框 -->
        <el-input
          v-model="queryParams.chipType"
          placeholder="输入芯片类型..."
          clearable
          class="chiptype-input"
          @keyup.enter="handleQuery"
        />

        <!-- 类型选择 -->
        <el-select
          v-model="queryParams.modelType"
          placeholder="所有类型"
          clearable
          class="filter-select"
          @change="handleQuery"
        >
          <el-option
            v-for="item in modelTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <!-- 筛选按钮 -->
        <el-button type="primary" class="filter-button" @click="handleQuery">
          <el-icon><Filter /></el-icon>
          筛选
        </el-button>
      </div>
    </div>

    <!-- 模型列表卡片式布局 -->
    <div v-loading="loading" class="model-list-container">
      <!-- 表头 -->
      <div class="model-list-header">
        <div class="model-type-column">模型分类</div>
        <div class="model-name-column">模型名称</div>
        <div class="model-chip-column">芯片类型</div>
        <div class="model-size-column">模型输入Size</div>
        <div class="model-code-column">推理示例代码</div>
        <div class="model-before-performance-column">前推理性能(ms)</div>
        <div class="model-performance-column">推理性能(ms)</div>
        <div class="model-after-performance-column">后推理性能(ms)</div>
        <div class="model-total-performance-column">总推理性能(ms)</div>
        <div class="model-accuracy-column">推理精度mAP</div>
      </div>

      <!-- 模型列表项 -->
      <div v-for="item in modelList" :key="item.id" class="model-list-item">
        <div class="model-type-column" :title="item.modelType">{{ item.modelType }}</div>
        <div class="model-name-column" :title="item.modelName">{{ item.modelName }}</div>
        <div class="model-chip-column" :title="item.chipType">{{ item.chipType }}</div>
        <div class="model-size-column" :title="item.size">{{ item.size }}</div>
        <div class="model-code-column">
          <el-button 
            type="primary" 
            class="download-btn" 
            @click="handleViewCode(item)" 
            :disabled="!checkDownloadPermission"
            :title="!checkDownloadPermission ? '无下载权限' : '下载模型示例代码'"
          >下载</el-button>
        </div>
        <div class="model-before-performance-column" :title="item.beforeDedcutivePerformance">{{ item.beforeDedcutivePerformance }}</div>
        <div class="model-performance-column" :title="item.deductivePerformance">{{ item.deductivePerformance }}</div>
        <div class="model-after-performance-column" :title="item.afterDedcutivePerformance">{{ item.afterDedcutivePerformance }}</div>
        <div class="model-total-performance-column" :title="(parseFloat(item.beforeDedcutivePerformance || 0) + parseFloat(item.deductivePerformance || 0) + parseFloat(item.afterDedcutivePerformance || 0)).toFixed(2)">
          {{ (parseFloat(item.beforeDedcutivePerformance || 0) + parseFloat(item.deductivePerformance || 0) + parseFloat(item.afterDedcutivePerformance || 0)).toFixed(2) }}
        </div>
        <div class="model-accuracy-column" :title="`${item.dedcutiveAccuracy}%`">{{ item.dedcutiveAccuracy }}%</div>
      </div>

      <!-- 空数据提示 -->
      <div v-if="modelList.length === 0 && !loading" class="empty-data">
        暂无数据
      </div>
    </div>

    <!-- 分页组件 -->
    <pagination
      v-if="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      class="pagination-container"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onActivated, onUnmounted, nextTick } from 'vue'
import { ElNotification } from 'element-plus'
import { Search, Filter } from '@element-plus/icons-vue'
import { listModelZoo } from '@/api/ai/modelZoo'
import { getDownloadUrl } from '@/api/ai/modelZoo'
import Pagination from '@/components/Pagination'
import useUserStore from '@/store/modules/user'
import { addAccessRecord } from '@/api/report/index'

// 模型列表数据
const modelList = ref([])
const total = ref(0)
const loading = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  modelName: '',
  modelType: '',
  chipType: ''
})

// 模型类型选项
const modelTypeOptions = ref([
  { value: '图像分类', label: '图像分类' },
  { value: '目标检测', label: '目标检测' },
  { value: '语义分割', label: '语义分割' },
  { value: '自然语言处理', label: '自然语言处理' }
])

// 检查下载权限
const checkDownloadPermission = computed(() => {
  const permissions = useUserStore().permissions
  const all_permission = "*:*:*"
  return permissions.some(permission => {
    return all_permission === permission || permission === 'ai:modelZooManager:download'
  })
})

// 获取模型列表数据
const getList = () => {
  loading.value = true
  listModelZoo(queryParams).then(response => {
    modelList.value = response.rows || []
    total.value = response.total || 0
    loading.value = false
    
    // 在列表数据更新后检查文本是否被截断
    nextTick(() => {
      checkTextTruncation()
    })
  }).catch(() => {
    loading.value = false
    ElNotification.error({
      title: '错误',
      message: '获取模型列表失败'
    })
  })
}

// 查询按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.modelName = ''
  queryParams.modelType = ''
  queryParams.chipType = ''
  handleQuery()
}

// 检查文本是否被截断
const checkTextTruncation = () => {
  nextTick(() => {
    // 获取所有可能被截断的元素
    const elements = document.querySelectorAll('.model-list-item > div[title]')
    
    elements.forEach(el => {
      // 检查元素内容是否溢出
      const isOverflowing = el.scrollWidth > el.clientWidth
      
      // 如果内容溢出，添加截断标记类
      if (isOverflowing) {
        el.classList.add('is-truncated')
      } else {
        el.classList.remove('is-truncated')
      }
    })
  })
}

// 查看/下载代码操作
const handleViewCode = (row) => {
  if (!checkDownloadPermission.value) {
    ElNotification.warning({
      title: '警告',
      message: '您没有下载权限'
    })
    return
  }
  
  if (!row.codeIdentififer) {
    ElNotification.warning({
      title: '警告',
      message: '该模型暂无示例代码'
    })
    return
  }
  
  getDownloadUrl(row.codeIdentififer, 3600, 'modelZoo-demo-code').then(response => {
    if (response.code === 200 && response.data) {
      // 创建隐藏的a标签并触发下载
      const link = document.createElement('a')
      link.href = response.data
      link.target = '_blank'
      link.download = row.modelName + '_示例代码'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElNotification.success({
        title: '成功',
        message: `下载模型: ${row.modelName} 的示例代码成功`
      })
    } else {
      ElNotification.error({
        title: '错误',
        message: '获取下载链接失败'
      })
    }
  }).catch(() => {
    ElNotification.error({
      title: '错误',
      message: `下载模型: ${row.modelName} 的示例代码失败`
    })
  })
}

// 页面加载时获取数据
onMounted(() => {
  // 记录页面访问
  recordPageAccess()
  getList()
  
  // 监听窗口大小变化，重新检查文本是否被截断
  window.addEventListener('resize', checkTextTruncation)
})

// 组件销毁时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', checkTextTruncation)
})

// 添加onActivated生命周期钩子
onActivated(() => {
  // 记录页面访问
  recordPageAccess()
  // 页面激活时重新检查文本是否被截断
  checkTextTruncation()
})

// 记录页面访问
const recordPageAccess = async () => {
  try {
    await addAccessRecord({
      accessType: 'modelZoo',
      accessCount: 1
    }).catch(err => {
      console.warn('Model Zoo页面访问记录添加失败，可能是防重复提交导致:', err)
      // 忽略错误，不影响页面正常加载
    })
    console.log('Model Zoo页面访问记录添加成功')
  } catch (error) {
    console.warn('Model Zoo页面访问记录添加失败:', error)
    // 忽略错误，不影响页面正常加载
  }
}
</script>

<style scoped>
.model-zoo-header {
  margin-bottom: 20px;
}

.model-zoo-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.model-zoo-description {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.model-zoo-search-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.model-zoo-search-wrapper {
  display: flex;
  gap: 10px;
}

.search-input {
  flex: 1;
}

.filter-select {
  width: 180px;
}

.chiptype-input {
  width: 180px;
}

.filter-button {
  display: flex;
  align-items: center;
}

/* 模型列表样式 */
.model-list-container {
  margin-bottom: 20px;
}

.model-list-header {
  display: flex;
  padding: 12px 0;
  font-weight: bold;
  margin-bottom: 10px;
}

.model-list-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: all 0.3s;
  font-size: 14px;
}

.model-list-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 列宽设置 */
.model-type-column, 
.model-name-column, 
.model-chip-column,
.model-size-column, 
.model-before-performance-column,
.model-performance-column,
.model-after-performance-column,
.model-total-performance-column,
.model-accuracy-column {
  flex: 1;
  padding: 0 8px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  cursor: default;
}

/* 悬浮提示样式 */
[title] {
  position: relative;
}

/* 使用JS检测内容是否溢出，通过CSS实现悬浮提示 */
[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 100%;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  padding: 5px 10px;
  white-space: nowrap;
  z-index: 10;
  font-size: 12px;
  margin-bottom: 5px;
  max-width: 300px;
  word-break: break-word;
  white-space: normal;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  /* 默认隐藏提示框 */
  display: none;
}

/* 当内容被截断时才显示提示框 */
.is-truncated[title]:hover::after {
  display: block;
}

.model-code-column {
  flex: 1;
  padding: 0 8px;
  text-align: center;
}

.model-code-column .download-btn {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 14px;
  min-width: 80px;
}

.model-code-column .download-btn:hover {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

.empty-data {
  padding: 40px 0;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 