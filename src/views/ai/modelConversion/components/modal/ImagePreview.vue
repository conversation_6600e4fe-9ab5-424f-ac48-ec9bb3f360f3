<template>
  <el-dialog
      :model-value="modelValue"
      @update:model-value="$emit('update:modelValue', $event)"
      title="已选择图片预览"
      width="800px"
      class="image-preview-dialog"
      @close="handleClose"
  >
    <div class="image-preview-content">
      <div class="image-preview-header">
        <div class="image-count">共 {{ images.length }} 张图片</div>
        <el-button
            type="danger"
            :disabled="images.length === 0"
            plain
            size="small"
            @click="handleClear"
        >
          <i class="fas fa-trash-alt"></i>
          清空图片
        </el-button>
      </div>
      <div class="image-grid-container">
        <div class="image-preview-grid">
          <div
              v-for="(image, index) in images"
              :key="index"
              class="image-card"
          >
            <div class="image-card-content">
              <img :src="image.url" :alt="image.name">
              <div class="image-card-overlay">
                <button class="delete-btn" @click.stop="handleDelete(index)">
                  <span class="delete-icon">×</span>
                </button>
              </div>
              <div v-if="isUploading && uploadProgress[image.name]" class="upload-progress-overlay">
                <div class="progress-info">
                  <el-progress
                      :percentage="uploadProgress[image.name].percent"
                      :show-text="false"
                      :stroke-width="4"
                  />
                  <div class="progress-details">
                    <span class="progress-percent">{{ uploadProgress[image.name].percent }}%</span>
                    <span class="progress-speed">{{ uploadProgress[image.name].speed }} MB/s</span>
                    <span class="progress-time">剩余 {{ uploadProgress[image.name].remainingTime }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="image-card-footer">
              <div class="image-card-name">{{ image.name }}</div>
              <div class="image-card-size">{{ image.size }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
            type="primary"
            :loading="isUploading"
            :disabled="images.length === 0"
            @click="handleUpload"
        >
          <i class="fas fa-cloud-upload-alt"></i>
          {{ isUploading ? '上传中...' : '上传图片' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, ref } from 'vue'
import { ElNotification, ElLoading } from 'element-plus'
import { FileUploader } from '@/utils/upload'
import { batchCreateUploadTask } from '@/api/ai/file'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  images: {
    type: Array,
    required: true,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'delete', 'clear'])
const isUploading = ref(false)
const uploadProgress = ref({})
const totalProgress = ref({
  percent: 0,
  speed: '0',
  remainingTime: '未知'
})

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleDelete = (index) => {
  emit('delete', index)
}

const handleClear = () => {
  emit('clear')
}

// 计算总体上传进度
const calculateTotalProgress = () => {
  const progressValues = Object.values(uploadProgress.value)
  if (progressValues.length === 0) return

  // 计算平均进度
  const totalPercent = progressValues.reduce((sum, p) => sum + p.percent, 0) / progressValues.length

  // 计算总速度
  const totalSpeed = progressValues.reduce((sum, p) => sum + parseFloat(p.speed || 0), 0)

  // 计算剩余时间（使用最长的剩余时间）
  const maxRemainingTime = progressValues.reduce((max, p) => {
    if (p.remainingTime === '未知') return max
    const time = parseFloat(p.remainingTime)
    return time > max ? time : max
  }, 0)

  totalProgress.value = {
    percent: Math.round(totalPercent),
    speed: totalSpeed.toFixed(2),
    remainingTime: maxRemainingTime === 0 ? '未知' : maxRemainingTime.toFixed(1)
  }
}

/** 验证文件类型是否为jpg */
const isJpgImage = (file) => {
  // 检查文件MIME类型
  const isJpgMime = file.type === 'image/jpeg' || file.type === 'image/jpg';
  // 检查文件扩展名
  const fileName = file.name.toLowerCase();
  const isJpgExt = fileName.endsWith('.jpg') || fileName.endsWith('.jpeg');
  
  return isJpgMime && isJpgExt;
};

const handleUpload = async () => {
  if (isUploading.value || props.images.length === 0) {
    return
  }

  // 验证所有图片是否都是jpg格式
  const invalidImages = props.images.filter(img => !isJpgImage(img.file));
  if (invalidImages.length > 0) {
    ElNotification({
      title: '错误',
      message: `有${invalidImages.length}张图片不是jpg格式，无法上传`,
      type: 'error',
      duration: 5000
    });
    return;
  }

  isUploading.value = true
  
  // 初始化每个图片的上传进度为1%，表示已开始上传
  uploadProgress.value = props.images.reduce((acc, img) => {
    acc[img.name] = { percent: 1, speed: 0, remainingTime: '计算中...' }
    return acc
  }, {})
  
  // 初始化总进度为1%
  totalProgress.value = {
    percent: 1,
    speed: '0',
    remainingTime: '计算中...'
  }
  
  // 立即通知父组件更新总进度
  emit('upload-success', [], totalProgress.value)

  try {
    const files = props.images.map(img => img.file)
    const uploadResults = await FileUploader.batchUpload(files, 'image', (progress) => {
      if (uploadProgress.value[progress.fileName]) {
        uploadProgress.value[progress.fileName] = {
          percent: progress.percent,
          speed: progress.speed,
          remainingTime: progress.remainingTime
        }
        calculateTotalProgress()
      }
    })

    const successCount = uploadResults.filter(r => r.status === 'success').length
    const failCount = uploadResults.filter(r => r.status === 'error').length

    if (successCount > 0) {
      ElNotification({
        title: '成功',
        message: `成功上传 ${successCount} 张图片`,
        type: 'success',
        duration: 5000
      })
      
      // 确保总进度为100%
      if (failCount === 0) {
        totalProgress.value = {
          percent: 100,
          speed: '0',
          remainingTime: '上传完成'
        }
      }
      
      emit('upload-success', uploadResults.filter(r => r.status === 'success'), totalProgress.value)
    }

    if (failCount > 0) {
      ElNotification({
        title: '警告',
        message: `${failCount} 张图片上传失败`,
        type: 'warning',
        duration: 5000
      })
    }
    
    // 更新上传状态但保持进度条显示
    isUploading.value = false
  } catch (error) {
    isUploading.value = false
    
    // 显示错误状态
    totalProgress.value = {
      percent: totalProgress.value.percent,
      speed: '0',
      remainingTime: '上传失败'
    }
    
    // 在错误发生后，延迟1秒将进度条重置为0
    setTimeout(() => {
      totalProgress.value = {
        percent: 0,
        speed: '0',
        remainingTime: '未知'
      }
      // 通知父组件更新总进度
      emit('upload-success', [], totalProgress.value)
    }, 1000)
  }
}
</script>

<style scoped>
.image-preview-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.image-preview-content {
  padding: 20px;
}

.image-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 16px;
}

.image-count {
  color: #666;
  font-size: 14px;
}

.image-grid-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 4px;
}

.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
  padding: 12px;
}

.image-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.image-card-content {
  position: relative;
  aspect-ratio: 1;
}

.image-card-content img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-card:hover .image-card-overlay {
  opacity: 1;
}

.delete-btn {
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 77, 79, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.delete-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.delete-icon {
  font-size: 24px;
  line-height: 24px;
  color: #ff4d4f;
  font-weight: bold;
}

.delete-btn:hover .delete-icon {
  color: #ff1f1f;
}

.image-card-footer {
  padding: 6px;
  background: #f5f7fa;
}

.image-card-name {
  font-size: 11px;
  color: #333;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-card-size {
  font-size: 11px;
  color: #909399;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer .el-button i {
  font-size: 16px;
}

.image-preview-header .el-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.image-preview-header .el-button i {
  font-size: 14px;
}

.upload-progress-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px;
  color: white;
}

.progress-info {
  width: 100%;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  margin-top: 4px;
}

.progress-percent {
  color: #409EFF;
  font-weight: bold;
}

.progress-speed {
  color: #E6A23C;
}

.progress-time {
  color: #67C23A;
}

:deep(.el-progress-bar__outer) {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

:deep(.el-progress-bar__inner) {
  background-color: #409EFF !important;
}
</style> 