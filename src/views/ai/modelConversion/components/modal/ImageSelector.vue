<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="选择量化图片"
    width="800px"
    class="image-selector-dialog"
    @close="handleClose"
  >
    <div class="image-selector-content">
      <div class="image-selector-header">
        <div class="image-count">共 {{ imageList.length }} 张图片，已选中 {{ selectedImages.length }} 张图片</div>
        <div class="image-actions">
          <el-button size="small" @click="selectAllImages">全选</el-button>
          <el-button size="small" @click="unselectAllImages">取消全选</el-button>
        </div>
      </div>
      <div class="image-grid-container">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="3" animated />
        </div>
        <div v-else-if="imageList.length === 0" class="empty-container">
          <el-empty description="暂无图片" />
        </div>
        <div v-else class="image-selector-grid">
          <div
            v-for="(image, index) in imageList"
            :key="index"
            class="image-card"
            :class="{ 'is-selected': isImageSelected(image) }"
            @click="toggleImageSelection(image)"
          >
            <div class="image-card-content">
              <img :src="image.url" :alt="image.fileName">
              <div class="image-card-overlay">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
            <div class="image-card-footer">
              <div class="image-card-name">{{ image.fileName }}</div>
              <div class="image-card-size">{{ formatFileSize(image.totalSize || image.fileSize) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { listFile, getDownloadUrl } from '@/api/admin/file'
import { ElNotification } from 'element-plus'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  defaultSelected: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const loading = ref(false)
const imageList = ref([])
const selectedImages = ref([])

// 获取图片列表
const getImageList = async () => {
  loading.value = true
  try {
    const response = await listFile({
      fileType: 'image',
      pageNum: 1,
      pageSize: 200,
      sortBy: 'batchNo',
      isAsc: 'desc',
      createBy: userStore.name
    })

    if (response.code === 200 && response.rows) {
      // 先设置基本的图片信息
      imageList.value = response.rows.map(img => ({
        ...img,
        url: null // 初始化为null，稍后会通过getDownloadUrl设置
      }))

      // 为每个图片获取下载URL
      await Promise.all(
        imageList.value.map(async (img, index) => {
          if (img.fileIdentifier || img.id) {
            try {
              const urlRes = await getDownloadUrl(img.fileIdentifier || img.id, 3600, false, 'image')
              if (urlRes.code === 200 && urlRes.data) {
                imageList.value[index].url = urlRes.data
              } else {
                // 如果API调用失败，回退到默认URL
                imageList.value[index].url = `/dev-api/file/download/${img.fileIdentifier || img.id}`
              }
            } catch (error) {
              console.error('获取图片URL失败:', error)
              // 如果API调用出错，回退到默认URL
              imageList.value[index].url = `/dev-api/file/download/${img.fileIdentifier || img.id}`
            }
          }
        })
      )

      // 每次获取图片列表时，始终选中最新批次（第一个批次）的图片
      if (imageList.value.length > 0) {
        selectFirstBatchImages(false)
      } else if (props.defaultSelected && props.defaultSelected.length > 0) {
        // 如果有默认选中的图片，则同步状态
        syncSelectedImages()
      }

      // 自动发出confirm事件，通知父组件已选中图片
      emit('confirm', selectedImages.value)
    } else {
      ElNotification.error({
        title: '错误',
        message: '获取图片列表失败'
      })
    }
  } catch (error) {
    console.error('获取图片列表失败:', error)
    ElNotification.error({
      title: '错误',
      message: '获取图片列表失败'
    })
  } finally {
    loading.value = false
  }
}

// 选中第一组批次号的图片（最新上传的批次）
const selectFirstBatchImages = (showMessage = true) => {
  if (imageList.value.length === 0) return
  
  // 清空之前的选择
  selectedImages.value = []
  
  // 获取第一个批次号（由于排序是降序，所以第一个就是最新上传的批次）
  const firstBatchNo = imageList.value[0].batchNo
  
  // 选中所有具有该批次号的图片
  selectedImages.value = imageList.value.filter(img => img.batchNo === firstBatchNo)
  
  if (showMessage) {
    console.log(`已自动选中最新批次(${firstBatchNo})的 ${selectedImages.value.length} 张图片`)
  }
  
  // 通知父组件已选中图片
  emit('confirm', selectedImages.value)
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '未知大小'
  
  const kb = 1024
  const mb = kb * 1024
  const gb = mb * 1024
  
  if (size < kb) {
    return size + ' B'
  } else if (size < mb) {
    return (size / kb).toFixed(2) + ' KB'
  } else if (size < gb) {
    return (size / mb).toFixed(2) + ' MB'
  } else {
    return (size / gb).toFixed(2) + ' GB'
  }
}

// 监听对话框打开状态变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 先清空选中状态
    selectedImages.value = []
    // 重新获取图片列表，将会自动选择最新批次图片
    getImageList()
  }
})

// 监听默认选中图片的变化
watch(() => props.defaultSelected, () => {
  // 如果图片列表已经加载完成，则同步选中状态
  if (imageList.value.length > 0) {
    syncSelectedImages()
  }
}, { deep: true })

// 同步选中状态
const syncSelectedImages = () => {
  // 将defaultSelected中的图片与imageList匹配，匹配成功则添加到selectedImages
  const selected = []
  for (const defaultImg of props.defaultSelected) {
    const matchedImg = imageList.value.find(img =>
      img.fileIdentifier === defaultImg.fileIdentifier ||
      img.id === defaultImg.id ||
      (defaultImg.label && img.fileName === defaultImg.label) ||
      (defaultImg.name && img.fileName === defaultImg.name)
    )
    if (matchedImg) {
      selected.push(matchedImg)
    }
  }
  selectedImages.value = selected
  console.log('Synced selected images count:', selectedImages.value.length)
}



// 切换图片选中状态
const toggleImageSelection = (image) => {
  const index = selectedImages.value.findIndex(item => 
    item.fileIdentifier === image.fileIdentifier || item.id === image.id
  )
  if (index === -1) {
    selectedImages.value.push(image)
  } else {
    selectedImages.value.splice(index, 1)
  }
}

// 全选
const selectAllImages = () => {
  selectedImages.value = [...imageList.value]
}

// 取消全选
const unselectAllImages = () => {
  selectedImages.value = []
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', selectedImages.value)
  emit('update:modelValue', false)
}

// 取消选择
const handleCancel = () => {
  emit('cancel')
  emit('update:modelValue', false)
}

// 关闭对话框
const handleClose = () => {
  if (selectedImages.value.length > 0) {
    emit('confirm', selectedImages.value)
  }
  emit('update:modelValue', false)
}

// 组件挂载时初始化数据
onMounted(() => {
  if (props.modelValue) {
    getImageList()
  }
})

// 判断图片是否被选中
const isImageSelected = (image) => {
  return selectedImages.value.some(selected => 
    selected.fileIdentifier === image.fileIdentifier ||
    selected.id === image.id
  )
}

// 暴露方法和属性给父组件
defineExpose({
  getImageList,
  selectedImages,
  imageList
})
</script>

<style scoped>
.image-selector-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.image-selector-content {
  padding: 20px;
}

.image-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.image-count {
  color: #666;
  font-size: 14px;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.loading-container, .empty-container {
  padding: 40px;
  text-align: center;
}

.image-grid-container {
  max-height: 500px;
  overflow-y: auto;
  padding: 4px;
}

.image-selector-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
  padding: 12px;
}

.image-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.image-card.is-selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.image-card-content {
  position: relative;
  aspect-ratio: 1;
}

.image-card-content img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-card.is-selected .image-card-overlay {
  opacity: 1;
}

.image-card-overlay i {
  font-size: 24px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.image-card-footer {
  padding: 6px;
  background: #f5f7fa;
}

.image-card-name {
  font-size: 11px;
  color: #333;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-card-size {
  font-size: 11px;
  color: #909399;
}

.dialog-footer {
  padding: 20px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #e4e7ed;
}
</style> 