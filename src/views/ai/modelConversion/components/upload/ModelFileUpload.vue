<template>
  <div class="card">
    <h3 class="card-title">原始模型文件</h3>
    <div class="upload-zone" 
      @dragover.prevent
      @drop.prevent="handleFileDrop"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <div class="upload-label">
        <i class="fas fa-inbox upload-icon"></i>
        <p class="upload-text">点击或拖拽文件到此处上传</p>
        <p class="upload-desc">
          支持格式：onnx, .pb
        </p>
        <input type="file" id="modelFile" accept=".onnx,.pb" @change="handleFileChange" ref="fileInput" style="display: none;">
      </div>
    </div>
    <div class="upload-progress">
      <el-progress 
        :percentage="uploadProgress.percent" 
        :stroke-width="4"
        :status="uploadProgress.status"
      />
      <div class="progress-details" v-if="uploadProgress.percent > 0">
        <span class="progress-percent">{{ uploadProgress.percent }}%</span>
        <span class="progress-speed">{{ uploadProgress.speed }} MB/s</span>
        <span class="progress-time">{{ 
          uploadProgress.percent === 100 
            ? '上传完成' 
            : '剩余 ' + uploadProgress.remainingTime 
        }}</span>
      </div>
    </div>
    <div class="card-footer">
      <div class="download-section" v-if="hasDownloadPermission">
        <div class="select-group">
          <span class="select-label">示例原始模型：</span>
          <el-select 
            v-model="selectedModel" 
            class="model-select"
            @change="handleModelChange"
            filterable
            placeholder="请选择或搜索模型"
            :loading="modelsLoading"
            remote
            :remote-method="remoteSearch"
            :reserve-keyword="true"
          >
            <el-option 
              v-for="model in exampleModels" 
              :key="model.id" 
              :label="model.fileName" 
              :value="model.fileIdentifier || model.identifier"
            />
          </el-select>
        </div>
        <el-button 
          type="primary" 
          class="preview-btn" 
          @click="handleDownload" 
          :disabled="!selectedModel || downloading"
          :loading="downloading"
          v-hasPermi="['ai:modelFile:download']"
        >
          <i v-if="!downloading" class="fas fa-download"></i>
          {{ downloading ? '下载中' : '下载' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { ElNotification } from 'element-plus'
import { FileUploader } from '@/utils/upload'
import { listFile, getDownloadUrl } from '@/api/admin/file'
import { checkPermi } from '@/utils/permission'
import useUserStore from '@/store/modules/user'

const emit = defineEmits(['update:uploadedModels', 'update:selectedModel', 'file-uploaded'])

const props = defineProps({
  uploadedModels: {
    type: Array,
    required: true
  },
  selectedModel: {
    type: String,
    required: true
  }
})

const selectedModel = ref(props.selectedModel)
const uploadProgress = ref({
  percent: 0,
  speed: '0',
  remainingTime: '未知',
  status: ''
})

// 示例模型列表
const exampleModels = ref([])

// 添加下载状态跟踪
const downloading = ref(false)

// 远程搜索加载状态
const modelsLoading = ref(false)

const userStore = useUserStore()

// 检查是否有下载权限
const hasDownloadPermission = computed(() => {
  return checkPermi(['ai:modelFile:download'])
})

// 添加文件输入引用
const fileInput = ref(null)

/** 获取示例模型列表 */
async function getExampleModels() {
  try {
    const response = await listFile({
      fileType: 'model-original-file',  // 只获取模型类型的文件
      pageNum: 1,
      pageSize: 10,
      createBy: userStore.name // 添加当前登录用户名作为查询参数
    })
    console.log('Example models response:', response)
    if (response.code === 200 && response.rows) {
      // 处理返回数据，确保每个模型都有identifier字段
      exampleModels.value = response.rows.map(model => ({
        ...model,
        identifier: model.fileIdentifier || model.identifier || model.id
      }))
    } else {
      exampleModels.value = []
    }
  } catch (error) {
    console.error('获取示例模型列表失败:', error)
    exampleModels.value = []
  }
}

// 页面加载时获取示例模型列表
onMounted(() => {
  getExampleModels()
})

// 监听选中模型的变化
watch(selectedModel, (newVal) => {
  emit('update:selectedModel', newVal)
})

// 监听props中selectedModel的变化
watch(() => props.selectedModel, (newVal) => {
  selectedModel.value = newVal
})

/** 验证文件类型是否合法 */
const validateFileType = (file) => {
  // 允许的文件类型
  const allowedTypes = ['.onnx', '.pb'];
  // 获取文件扩展名
  const fileName = file.name.toLowerCase();
  const isValid = allowedTypes.some(type => fileName.endsWith(type));
  
  if (!isValid) {
    ElNotification({
      title: '错误',
      message: `不支持的文件类型，仅支持 ${allowedTypes.join(', ')} 格式`,
      type: 'error',
      duration: 5000
    });
    return false;
  }
  return true;
};

const handleFileUpload = async (file) => {
  // 验证文件类型
  if (!validateFileType(file)) {
    return;
  }

  try {
    // 初始化进度条为1%，表示已开始上传
    uploadProgress.value = {
      percent: 1,
      speed: '0',
      remainingTime: '计算中...',
      status: 'primary'
    }

    const uploader = new FileUploader(file)
    const identifier = await uploader.upload('model', (progress) => {
      uploadProgress.value = {
        percent: progress.percent,
        speed: progress.speed,
        remainingTime: progress.remainingTime,
        status: progress.percent === 100 ? 'success' : 'primary'
      }
      
      // 当上传完成时，更新显示文本
      if (progress.percent === 100) {
        uploadProgress.value.remainingTime = '上传完成'
      }
    })

    // 更新上传列表，将新文件添加到列表开头实现优先显示
    const fileInfo = {
      label: file.name,
      value: identifier
    }

    // 检查是否已存在相同的文件，如果存在则移除旧的
    const existingIndex = props.uploadedModels.findIndex(model => model.value === identifier)
    let updatedModels = [...props.uploadedModels]
    if (existingIndex !== -1) {
      updatedModels.splice(existingIndex, 1)
    }

    // 将新文件添加到列表开头
    updatedModels.unshift(fileInfo)
    emit('update:uploadedModels', updatedModels)

    // 清空示例模型选择，因为用户已经上传了新文件用于转换
    selectedModel.value = ''
    emit('update:selectedModel', '')

    // 触发文件上传成功事件
    emit('file-uploaded', { type: 'model', identifier })
    ElNotification({
      title: '成功',
      message: `${file.name} 上传成功`,
      type: 'success',
      duration: 5000
    })

  } catch (error) {
    uploadProgress.value.status = 'exception'
    uploadProgress.value.remainingTime = '上传失败'
    // 错误已由axios错误拦截器处理，此处不需重复提示
    // 仅在没有明确错误信息时才提示通用错误
    if (!error.message || error.message === 'error') {
      ElNotification({
        title: '错误',
        message: '文件上传失败',
        type: 'error',
        duration: 5000
      })
    }
    
    // 在错误发生后，延迟1秒将进度条重置为0
    setTimeout(() => {
      uploadProgress.value = {
        percent: 0,
        speed: '0',
        remainingTime: '未知',
        status: ''
      }
    }, 1000)
  }
}

const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 验证文件类型
    if (validateFileType(file)) {
      handleFileUpload(file)
    }
  }
  // 清空文件输入框的值，以便于下次选择同一个文件时也能触发change事件
  event.target.value = ''
}

const handleFileDrop = (event) => {
  const file = event.dataTransfer.files[0]
  if (file) {
    // 验证文件类型
    if (validateFileType(file)) {
      handleFileUpload(file)
    }
  }
}

const handleDragEnter = (event) => {
  event.preventDefault()
  event.currentTarget.classList.add('drag-over')
}

const handleDragLeave = (event) => {
  event.preventDefault()
  event.currentTarget.classList.remove('drag-over')
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click()
}

/** 下载示例模型 */
async function handleDownload() {
  console.log('Selected model:', selectedModel.value)
  
  if (!selectedModel.value) {
    ElNotification({
      title: '警告',
      message: '请先选择一个示例模型',
      type: 'warning',
      duration: 5000
    })
    return
  }
  
  // 权限检查
  if (!hasDownloadPermission.value) {
    ElNotification({
      title: '错误',
      message: '对不起，您没有下载权限',
      type: 'error',
      duration: 5000
    })
    return
  }
  
  try {
    downloading.value = true
    console.log('Download model with ID:', selectedModel.value)
    
    const response = await getDownloadUrl(selectedModel.value, 3600, true, 'model-original-file')
    console.log('Download response:', response)
    
    if (response.code === 200 && response.data) {
      // 获取当前选中的模型信息
      const selectedModelInfo = exampleModels.value.find(model => 
        model.identifier === selectedModel.value || 
        model.fileIdentifier === selectedModel.value
      )
      const fileName = selectedModelInfo ? selectedModelInfo.fileName : `model-${selectedModel.value}.bin`
      
      // 创建一个隐藏的 a 标签来触发下载
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = response.data
      link.setAttribute('download', fileName) // 设置下载文件名
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElNotification({
        title: '成功',
        message: '下载开始，请稍等',
        type: 'success',
        duration: 5000
      })
    } else {
      ElNotification({
        title: '错误',
        message: '获取下载链接失败',
        type: 'error',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Download error:', error)
    ElNotification({
      title: '错误',
      message: '下载失败：' + (error.message || '未知错误'),
      type: 'error',
      duration: 5000
    })
  } finally {
    downloading.value = false
  }
}

/** 处理模型选择变化 */
function handleModelChange(value) {
  console.log('Model selection changed:', value)
  selectedModel.value = value
  emit('update:selectedModel', value)
}

/** 远程搜索模型文件 */
async function remoteSearch(query) {
  if (query) {
    modelsLoading.value = true
    try {
      const response = await listFile({
        fileType: 'model-original-file',
        fileName: query,
        pageNum: 1,
        pageSize: 10,
        createBy: userStore.name // 添加当前登录用户名作为查询参数
      })
      console.log('Search models response:', response)
      if (response.code === 200 && response.rows) {
        exampleModels.value = response.rows.map(model => ({
          ...model,
          identifier: model.fileIdentifier || model.identifier || model.id
        }))
      }
    } catch (error) {
      console.error('搜索模型文件失败:', error)
    } finally {
      modelsLoading.value = false
    }
  } else {
    getExampleModels()
  }
}
</script>

<style scoped>
.card {
  background: white;
  border-radius: 12px;
  padding: 12px 24px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card:hover {
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 20px;
}

.upload-zone {
  border: 2px dashed #e5e7eb;
  border-radius: 12px;
  padding: 32px 20px;
  text-align: center;
  background-color: #fafafa;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-zone::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(64, 158, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.upload-zone.drag-over::after {
  opacity: 1;
}

.upload-zone:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.upload-icon {
  font-size: 40px;
  margin-bottom: 16px;
  color: #909399;
  transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon {
  color: #409eff;
  transform: scale(1.1);
}

.upload-text {
  color: #666;
  margin-bottom: 8px;
  font-size: 16px;
}

.upload-desc {
  color: #999;
  font-size: 14px;
  margin-top: 8px;
  line-height: 1.5;
}

input[type="file"] {
  display: none;
}

.card-footer {
  margin-top: auto;
}

.download-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.select-group {
  display: flex;
  align-items: center;
}

.select-label {
  font-size: 14px;
  color: #606266;
  margin-right: 20px;
}

.preview-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 60px;
  padding: 6px 12px;
}

.preview-btn i {
  font-size: 14px;
}

.model-select {
  width: 200px;
}

.upload-progress {
  margin: 0 0 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-top: 8px;
  color: #606266;
}

.progress-percent {
  color: #409EFF;
  font-weight: bold;
}

.progress-speed {
  color: #E6A23C;
}

.progress-time {
  color: #67C23A;
}

:deep(.el-progress-bar__outer) {
  background-color: #e9ecef !important;
}

:deep(.el-progress-bar__inner) {
  background-color: #409EFF !important;
}

:deep(.el-select) {
  .el-input__inner {
    padding-right: 30px;
  }
}

:deep(.el-select-dropdown) {
  min-width: 200px !important;
}

.upload-label {
  width: 100%;
  height: 100%;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style> 