<template>
  <div class="card">
    <h3 class="card-title">模型转换的配置文件</h3>
    <div class="upload-zone"
      @dragover.prevent
      @drop.prevent="handleFileDrop"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <div class="upload-label">
        <i class="fas fa-file-excel upload-icon"></i>
        <p class="upload-text">点击或拖拽配置文件到此处上传</p>
        <p class="upload-desc">
          支持格式：CSV、JSON<br>
          用于配置模型转换参数
        </p>
        <input type="file" id="configFile" accept=".csv,.json" @change="handleFileChange" ref="fileInput" style="display: none;">
      </div>
    </div>
    <div class="upload-progress">
      <el-progress 
        :percentage="uploadProgress.percent" 
        :stroke-width="4"
        :status="uploadProgress.status"
      />
      <div class="progress-details" v-if="uploadProgress.percent > 0">
        <span class="progress-percent">{{ uploadProgress.percent }}%</span>
        <span class="progress-speed">{{ uploadProgress.speed }} MB/s</span>
        <span class="progress-time">{{ 
          uploadProgress.percent === 100 
            ? '上传完成' 
            : '剩余 ' + uploadProgress.remainingTime 
        }}</span>
      </div>
    </div>
    <div class="card-footer">
      <div class="download-section" v-if="hasDownloadPermission">
        <div class="select-group">
          <span class="select-label">示例配置文件：</span>
          <el-select 
            v-model="selectedConfig" 
            class="model-select" 
            @change="handleConfigChange"
            filterable
            placeholder="请选择或搜索配置文件"
            :loading="configsLoading"
            remote
            :remote-method="remoteSearch"
            :reserve-keyword="true"
          >
            <el-option v-for="config in exampleConfigs" :key="config.identifier" :label="config.fileName" :value="config.identifier" />
          </el-select>
        </div>
        <el-button 
          type="primary" 
          class="preview-btn" 
          @click="handleDownload" 
          :disabled="!selectedConfig || downloading"
          :loading="downloading"
          v-hasPermi="['ai:configFile:download']"
        >
          <i v-if="!downloading" class="fas fa-download"></i>
          {{ downloading ? '下载中' : '下载' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { ElNotification } from 'element-plus'
import { FileUploader } from '@/utils/upload'
import { listFile, getDownloadUrl } from '@/api/admin/file'
import { checkPermi } from '@/utils/permission'
import useUserStore from '@/store/modules/user'

const emit = defineEmits(['update:uploadedConfigs', 'update:selectedConfig', 'file-uploaded'])
const userStore = useUserStore()

const props = defineProps({
  uploadedConfigs: {
    type: Array,
    required: true
  },
  selectedConfig: {
    type: String,
    required: true
  }
})

const selectedConfig = ref(props.selectedConfig)
const uploadProgress = ref({
  percent: 0,
  speed: '0',
  remainingTime: '未知',
  status: ''
})

// 示例配置文件列表
const exampleConfigs = ref([])
// 下载状态
const downloading = ref(false)
// 远程搜索加载状态
const configsLoading = ref(false)

// 检查是否有下载权限
const hasDownloadPermission = computed(() => {
  return checkPermi(['ai:configFile:download'])
})

// 添加文件输入引用
const fileInput = ref(null)

/** 获取示例配置文件列表 */
async function getExampleConfigs() {
  try {
    const response = await listFile({
      fileType: 'model-config-file',  // 只获取配置文件类型
      pageNum: 1,
      pageSize: 10,
      createBy: userStore.name // 添加当前登录用户名作为查询参数
    })
    console.log('Example configs response:', response)
    if (response.code === 200 && response.rows) {
      // 处理返回数据，确保每个配置文件都有identifier字段
      exampleConfigs.value = response.rows.map(config => ({
        ...config,
        identifier: config.fileIdentifier || config.identifier || config.id
      }))
    } else {
      exampleConfigs.value = []
    }
  } catch (error) {
    console.error('获取示例配置文件列表失败:', error)
    exampleConfigs.value = []
  }
}

/** 远程搜索配置文件 */
async function remoteSearch(query) {
  if (query) {
    configsLoading.value = true
    try {
      const response = await listFile({
        fileType: 'model-config-file',
        fileName: query,
        pageNum: 1,
        pageSize: 10,
        createBy: userStore.name // 添加当前登录用户名作为查询参数
      })
      if (response.code === 200 && response.rows) {
        exampleConfigs.value = response.rows.map(config => ({
          ...config,
          identifier: config.fileIdentifier || config.identifier || config.id
        }))
      }
    } catch (error) {
      console.error('搜索配置文件失败:', error)
    } finally {
      configsLoading.value = false
    }
  } else {
    getExampleConfigs()
  }
}

// 页面加载时获取示例配置文件列表
onMounted(() => {
  getExampleConfigs()
})

// 监听选中配置的变化
watch(selectedConfig, (newVal) => {
  emit('update:selectedConfig', newVal)
})

// 监听props中selectedConfig的变化
watch(() => props.selectedConfig, (newVal) => {
  selectedConfig.value = newVal
})

/** 处理配置文件选择变化 */
function handleConfigChange(value) {
  console.log('Config selection changed:', value)
  selectedConfig.value = value
  emit('update:selectedConfig', value)
}

/** 下载示例配置文件 */
async function handleDownload() {
  console.log('Selected config:', selectedConfig.value)
  
  if (!selectedConfig.value) {
    ElNotification({
      title: '警告',
      message: '请先选择一个示例配置文件',
      type: 'warning',
      duration: 5000
    })
    return
  }
  
  // 权限检查
  if (!hasDownloadPermission.value) {
    ElNotification({
      title: '错误',
      message: '对不起，您没有下载权限',
      type: 'error',
      duration: 5000
    })
    return
  }
  
  try {
    downloading.value = true
    console.log('Download config with ID:', selectedConfig.value)
    
    const response = await getDownloadUrl(selectedConfig.value, 3600, true, 'model-config-file')
    console.log('Download response:', response)
    
    if (response.code === 200 && response.data) {
      // 获取当前选中的配置文件信息
      const selectedConfigInfo = exampleConfigs.value.find(config => 
        config.identifier === selectedConfig.value || 
        config.fileIdentifier === selectedConfig.value
      )
      const fileName = selectedConfigInfo ? selectedConfigInfo.fileName : `config-${selectedConfig.value}.csv`
      
      // 创建一个隐藏的 a 标签来触发下载
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = response.data
      link.setAttribute('download', fileName) // 设置下载文件名
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      ElNotification({
        title: '成功',
        message: '下载开始，请稍等',
        type: 'success',
        duration: 5000
      })
    } else {
      ElNotification({
        title: '错误',
        message: '获取下载链接失败',
        type: 'error',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('Download error:', error)
    ElNotification({
      title: '错误',
      message: '下载失败：' + (error.message || '未知错误'),
      type: 'error',
      duration: 5000
    })
  } finally {
    downloading.value = false
  }
}

/** 验证文件类型是否合法 */
const validateFileType = (file) => {
  // 允许的文件类型
  const allowedTypes = ['.csv', '.json'];
  // 获取文件扩展名
  const fileName = file.name.toLowerCase();
  const isValid = allowedTypes.some(type => fileName.endsWith(type));
  
  if (!isValid) {
    ElNotification({
      title: '错误',
      message: `不支持的文件类型，仅支持 ${allowedTypes.join(', ')} 格式`,
      type: 'error',
      duration: 5000
    });
    return false;
  }
  return true;
};

const handleFileUpload = async (file) => {
  // 验证文件类型
  if (!validateFileType(file)) {
    return;
  }

  try {
    // 初始化进度条为1%，表示已开始上传
    uploadProgress.value = {
      percent: 1,
      speed: '0',
      remainingTime: '计算中...',
      status: 'primary'
    }

    const uploader = new FileUploader(file)
    const identifier = await uploader.upload('config', (progress) => {
      uploadProgress.value = {
        percent: progress.percent,
        speed: progress.speed,
        remainingTime: progress.remainingTime,
        status: progress.percent === 100 ? 'success' : 'primary'
      }
      
      // 当上传完成时，更新显示文本
      if (progress.percent === 100) {
        uploadProgress.value.remainingTime = '上传完成'
      }
    })

    // 更新上传列表
    const fileInfo = {
      label: file.name,
      value: identifier
    }

    // 更新上传列表，将新文件添加到列表开头实现优先显示
    // 检查是否已存在相同的文件，如果存在则移除旧的
    const existingIndex = props.uploadedConfigs.findIndex(config => config.value === identifier)
    let updatedConfigs = [...props.uploadedConfigs]
    if (existingIndex !== -1) {
      updatedConfigs.splice(existingIndex, 1)
    }

    // 将新文件添加到列表开头
    updatedConfigs.unshift(fileInfo)
    emit('update:uploadedConfigs', updatedConfigs)

    // 清空示例配置文件选择，因为用户已经上传了新文件用于转换
    selectedConfig.value = ''
    emit('update:selectedConfig', '')

    // 触发文件上传成功事件
    emit('file-uploaded', { type: 'config', identifier })
    ElNotification({
      title: '成功',
      message: `${file.name} 上传成功`,
      type: 'success',
      duration: 5000
    })

  } catch (error) {
    uploadProgress.value.status = 'exception'
    uploadProgress.value.remainingTime = '上传失败'
    // 错误已由axios错误拦截器处理，此处不需重复提示
    // 仅在没有明确错误信息时才提示通用错误
    if (!error.message || error.message === 'error') {
      ElNotification({
        title: '错误',
        message: `文件上传失败`,
        type: 'error',
        duration: 5000
      })
    }
    
    // 在错误发生后，延迟1秒将进度条重置为0
    setTimeout(() => {
      uploadProgress.value = {
        percent: 0,
        speed: '0',
        remainingTime: '未知',
        status: ''
      }
    }, 1000)
  }
}

const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (file) {
    // 验证文件类型
    if (validateFileType(file)) {
      handleFileUpload(file)
    }
  }
  // 清空文件输入框的值，以便于下次选择同一个文件时也能触发change事件
  event.target.value = ''
}

const handleFileDrop = (event) => {
  const file = event.dataTransfer.files[0]
  if (file) {
    // 验证文件类型
    if (validateFileType(file)) {
      handleFileUpload(file)
    }
  }
}

const handleDragEnter = (event) => {
  event.preventDefault()
  event.currentTarget.classList.add('drag-over')
}

const handleDragLeave = (event) => {
  event.preventDefault()
  event.currentTarget.classList.remove('drag-over')
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click()
}
</script>

<style scoped>
.card {
  background: white;
  border-radius: 12px;
  padding: 12px 24px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card:hover {
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 20px;
}

.upload-zone {
  border: 2px dashed #e5e7eb;
  border-radius: 12px;
  padding: 32px 20px;
  text-align: center;
  background-color: #fafafa;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-zone::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(64, 158, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.upload-zone.drag-over::after {
  opacity: 1;
}

.upload-zone:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.upload-icon {
  font-size: 40px;
  margin-bottom: 16px;
  color: #909399;
  transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon {
  color: #409eff;
  transform: scale(1.1);
}

.upload-text {
  color: #666;
  margin-bottom: 8px;
  font-size: 16px;
}

.upload-desc {
  color: #999;
  font-size: 14px;
  margin-top: 8px;
  line-height: 1.5;
}

input[type="file"] {
  display: none;
}

.card-footer {
  margin-top: auto;
}

.download-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.select-group {
  display: flex;
  align-items: center;
}

.select-label {
  font-size: 14px;
  color: #606266;
  margin-right: 20px;
}

.preview-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 60px;
  padding: 6px 12px;
}

.preview-btn i {
  font-size: 14px;
}

.model-select {
  width: 200px;
}

.upload-progress {
  margin: 0 0 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-top: 8px;
  color: #606266;
}

.progress-percent {
  color: #409EFF;
  font-weight: bold;
}

.progress-speed {
  color: #E6A23C;
}

.progress-time {
  color: #67C23A;
}

:deep(.el-progress-bar__outer) {
  background-color: #e9ecef !important;
}

:deep(.el-progress-bar__inner) {
  background-color: #409EFF !important;
}

:deep(.el-select) {
  .el-input__inner {
    padding-right: 30px;
  }
}

:deep(.el-select-dropdown) {
  min-width: 200px !important;
}

.upload-label {
  width: 100%;
  height: 100%;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style> 