<template>
  <div class="card">
    <h3 class="card-title">模型量化图片</h3>
    <div class="upload-zone"
      @dragover.prevent
      @drop.prevent="handleFileDrop"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <div class="upload-label">
        <i class="fas fa-images upload-icon"></i>
        <p class="upload-text">点击或拖拽图片文件到此处上传</p>
        <p class="upload-desc">
          仅支持 jpg 格式<br>
          每张图片大小不超过 5MB<br>
          最多上传 200 张图片
        </p>
        <input type="file" id="imageFile" accept=".jpg,.jpeg" multiple @change="handleFileChange" ref="fileInput" style="display: none;">
      </div>
    </div>
    <div class="upload-progress">
      <el-progress 
        :percentage="totalProgress.percent" 
        :stroke-width="4"
        :status="totalProgress.status"
      />
      <div class="progress-details" v-if="totalProgress.percent > 0">
        <span class="progress-percent">{{ totalProgress.percent }}%</span>
        <span class="progress-speed">{{ totalProgress.speed }} MB/s</span>
        <span class="progress-time">{{ 
          totalProgress.percent === 100 
            ? '上传完成' 
            : '剩余 ' + totalProgress.remainingTime 
        }}</span>
      </div>
    </div>
    <div class="selected-images">
      <div class="selected-count">
        {{ selectedFiles.length > 0 ? `已选择 ${selectedFiles.length} 张图片` : '未选择图片' }}
      </div>
      <el-button type="primary" size="small" @click="showPreview" :disabled="selectedFiles.length === 0">
        <i class="fas fa-eye"></i>
        预览图片
      </el-button>
    </div>
  </div>
  
  <!-- 图片预览对话框 -->
  <image-preview
    v-model="previewVisible"
    :images="selectedFiles"
    @upload-success="handleUploadSuccess"
    @delete="handleImageDelete"
    @clear="handleImageClear"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElNotification } from 'element-plus'
import ImagePreview from '../modal/ImagePreview.vue'

const emit = defineEmits(['update:uploadedImages', 'upload-complete'])

const props = defineProps({
  uploadedImages: {
    type: Array,
    required: true
  }
})

const selectedFiles = ref([])
const previewVisible = ref(false)
const totalProgress = ref({
  percent: 0,
  speed: '0',
  remainingTime: '未知',
  status: ''
})

const MAX_IMAGES = 200;

// 添加文件输入引用
const fileInput = ref(null)

const showPreview = () => {
  previewVisible.value = true
}

const handleFileChange = (event) => {
  const files = Array.from(event.target.files)
  handleFiles(files)
  // 清空文件输入框的值，以便于下次选择同样的文件时也能触发change事件
  event.target.value = ''
}

const handleFileDrop = (event) => {
  const files = Array.from(event.dataTransfer.files)
  handleFiles(files)
}

/** 验证文件类型是否为jpg */
const isJpgImage = (file) => {
  // 检查文件MIME类型
  const isJpgMime = file.type === 'image/jpeg' || file.type === 'image/jpg';
  // 检查文件扩展名
  const fileName = file.name.toLowerCase();
  const isJpgExt = fileName.endsWith('.jpg') || fileName.endsWith('.jpeg');
  
  return isJpgMime && isJpgExt;
};

const handleFiles = (files) => {
  // 筛选出jpg格式的图片
  const jpgFiles = files.filter(file => isJpgImage(file));
  const invalidFiles = files.length - jpgFiles.length;
  
  if (invalidFiles > 0) {
    ElNotification.warning({
      title: '警告',
      message: `${invalidFiles} 个文件不是jpg格式，已被过滤`,
      duration: 5000
    });
  }

  const oversizedFiles = jpgFiles.filter(file => file.size > 5 * 1024 * 1024);
  if (oversizedFiles.length > 0) {
    ElNotification.warning({
      title: '警告',
      message: `${oversizedFiles.length} 张图片超过 5MB 大小限制，已被过滤`,
      duration: 5000
    });
  }

  const validFiles = jpgFiles.filter(file => file.size <= 5 * 1024 * 1024);
  
  // 检查图片总数是否超过最大限制
  if (selectedFiles.value.length + validFiles.length > MAX_IMAGES) {
    ElNotification.warning({
      title: '警告',
      message: `最多只能上传 ${MAX_IMAGES} 张图片，已超出限制`,
      duration: 5000
    });
    const remainingSlots = MAX_IMAGES - selectedFiles.value.length;
    
    // 只取剩余可上传数量的图片
    if (remainingSlots > 0) {
      const limitedFiles = validFiles.slice(0, remainingSlots);
      
      const newFiles = limitedFiles.map(file => ({
        file,
        name: file.name,
        url: URL.createObjectURL(file),
        size: (file.size / 1024).toFixed(1) + ' KB'
      }));

      selectedFiles.value = [...selectedFiles.value, ...newFiles];
      
      ElNotification.success({
        title: '成功',
        message: `已添加 ${limitedFiles.length} 张图片`,
        duration: 5000
      });
      // 自动打开预览模态框
      showPreview();
    }
    
    return;
  }
  
  const newFiles = validFiles.map(file => ({
    file,
    name: file.name,
    url: URL.createObjectURL(file),
    size: (file.size / 1024).toFixed(1) + ' KB'
  }));

  selectedFiles.value = [...selectedFiles.value, ...newFiles];
  
  if (validFiles.length > 0) {
    ElNotification.success({
      title: '成功',
      message: `已添加 ${validFiles.length} 张图片`,
      duration: 5000
    });
    // 自动打开预览模态框
    showPreview();
  }
};

const handleUploadSuccess = (results, progress) => {
  // 更新总进度
  totalProgress.value = {
    ...progress,
    status: progress.percent === 100 ? 'success' : 'primary'
  }

  // 当上传完成时，更新显示文本
  if (progress.percent === 100) {
    totalProgress.value.remainingTime = '上传完成'
    
    // 关闭预览模态框
    previewVisible.value = false
    
    // 清空已选择的图片
    selectedFiles.value.forEach(file => {
      URL.revokeObjectURL(file.url)
    })
    selectedFiles.value = []
  }

  // 如果结果为空且进度不为100%，说明是初始化进度，不需要更新上传列表
  if (results.length === 0 && progress.percent !== 100) {
    return;
  }

  // 更新上传列表
  const uploadedFiles = results.map(result => ({
    label: result.fileName,
    value: result.identifier
  }))

  // 确保上传后的图片总数不超过限制
  const totalUploadedImages = [...props.uploadedImages, ...uploadedFiles];
  if (totalUploadedImages.length > MAX_IMAGES) {
    ElNotification.warning({
      title: '警告',
      message: `已达到最大上传限制 ${MAX_IMAGES} 张图片，超出部分将被忽略`,
      duration: 5000
    });
    emit('update:uploadedImages', totalUploadedImages.slice(0, MAX_IMAGES));
    return;
  }

  emit('update:uploadedImages', totalUploadedImages);
  
  // 触发上传完成事件，并传递新上传的图片标识符
  if (progress.percent === 100) {
    emit('upload-complete', uploadedFiles.map(file => file.value))
  }
}

const handleImageDelete = (index) => {
  const file = selectedFiles.value[index]
  URL.revokeObjectURL(file.url)
  selectedFiles.value.splice(index, 1)
}

const handleImageClear = () => {
  selectedFiles.value.forEach(file => {
    URL.revokeObjectURL(file.url)
  })
  selectedFiles.value = []
  previewVisible.value = false
}

const handleDragEnter = (event) => {
  event.preventDefault()
  event.currentTarget.classList.add('drag-over')
}

const handleDragLeave = (event) => {
  event.preventDefault()
  event.currentTarget.classList.remove('drag-over')
}

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value.click()
}
</script>

<style scoped>
.card {
  background: white;
  border-radius: 12px;
  padding: 12px 24px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card:hover {
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 20px;
}

.upload-zone {
  border: 2px dashed #e5e7eb;
  border-radius: 12px;
  padding: 32px 20px;
  text-align: center;
  background-color: #fafafa;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.upload-zone::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(64, 158, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.upload-zone.drag-over::after {
  opacity: 1;
}

.upload-zone:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
}

.upload-icon {
  font-size: 40px;
  margin-bottom: 16px;
  color: #909399;
  transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon {
  color: #409eff;
  transform: scale(1.1);
}

.upload-text {
  color: #666;
  margin-bottom: 8px;
  font-size: 16px;
}

.upload-desc {
  color: #999;
  font-size: 14px;
  margin-top: 8px;
  line-height: 1.5;
}

input[type="file"] {
  display: none;
}

.selected-images {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 12px 16px;
  /* background: #f5f7fa; */
  border-radius: 8px;
  min-height: 52px;
  border: 1px solid #e4e7ed;
}

.selected-count {
  color: #606266;
  font-size: 14px;
}

.selected-images .el-button[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-progress {
  margin: 0 0 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-top: 8px;
  color: #606266;
}

.progress-percent {
  color: #409EFF;
  font-weight: bold;
}

.progress-speed {
  color: #E6A23C;
}

.progress-time {
  color: #67C23A;
}

:deep(.el-progress-bar__outer) {
  background-color: #e9ecef !important;
}

:deep(.el-progress-bar__inner) {
  background-color: #409EFF !important;
}

.el-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.el-button i {
  font-size: 14px;
}

.upload-label {
  width: 100%;
  height: 100%;
  min-height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style> 