<template>
  <div class="log-container">
    <div class="log-header">
      <h3>模型转换日志</h3>
      <div class="log-actions">
        <el-button size="small" type="primary" @click="clearLogs">清空日志</el-button>
      </div>
    </div>
    <div class="log-content" ref="logContentRef">
      <!-- 使用虚拟滚动优化大量日志的渲染 -->
      <div v-for="(log, index) in visibleLogs" :key="index" class="log-item" :class="{'log-item-new': isNewLog(index)}">
        <span class="log-timestamp">[{{ log.timestamp }}]</span>
        <span class="log-level" :style="{ color: themeColor }">[{{ log.level }}]</span>
        <span class="log-message">{{ log.message }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onUnmounted, nextTick, onMounted, onActivated, computed } from 'vue'
import useWebSocket from '@/hooks/useWebSocket'
import { ElNotification } from 'element-plus'
import { transferStatus } from '@/api/ai/transfer'
import useSettingsStore from '@/store/modules/settings'

const settingsStore = useSettingsStore()
const themeColor = computed(() => settingsStore.theme)

const props = defineProps({
  taskId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['conversion-success', 'conversion-failed'])

const logs = ref([])
const logContentRef = ref(null)
let wsInstance = null
const maxVisibleLogs = 100 // 最大可见日志数量
const newLogTimeout = 500 // 新日志高亮时间(毫秒)
const newLogIndexes = ref({}) // 跟踪新添加的日志
const allowReconnect = ref(true) // 是否允许重连
const isConversionStarted = ref(false) // 是否已开始转换
let statusCheckInterval = null // 状态检查定时器

// 存储日志的localStorage键名
const LATEST_LOG_KEY = 'model_conversion_latest_logs'
const LATEST_TASK_ID_KEY = 'model_conversion_latest_task_id'

// 计算当前可见的日志
const visibleLogs = computed(() => {
  if (logs.value.length <= maxVisibleLogs) {
    return logs.value
  }
  // 只显示最新的maxVisibleLogs条日志
  return logs.value.slice(logs.value.length - maxVisibleLogs)
})

// 检查是否是新添加的日志
const isNewLog = (index) => {
  return newLogIndexes.value[index] === true
}

// 添加日志的通用函数
const addLog = (level, message, taskId) => {
  const newLog = {
    level,
    message,
    timestamp: new Date().toLocaleTimeString(),
    taskId: taskId || props.taskId
  }
  
  const newIndex = logs.value.length
  logs.value = [...logs.value, newLog]
  
  // 标记新日志
  if (level !== 'SYSTEM') { // 系统日志不高亮
    newLogIndexes.value[newIndex] = true
    setTimeout(() => {
      newLogIndexes.value[newIndex] = false
    }, newLogTimeout)
  }
  
  // 保存日志到localStorage
  debouncedSaveLogsToStorage()
  
  // 自动滚动到底部
  scrollToBottom()
  
  return newLog
}

// 处理转换完成状态
const handleConversionComplete = (isSuccess, message) => {
  // 通知父组件
  if (isSuccess) {
    emit('conversion-success')
  } else {
    emit('conversion-failed', { message: message || '模型转换失败' })
  }
  
  // 设置状态
  allowReconnect.value = false
  isConversionStarted.value = false
  
  // 断开WebSocket连接
  if (wsInstance) {
    wsInstance.disconnect()
  }
  
  // 添加日志
  const level = isSuccess ? 'INFO' : 'ERROR'
  const logMessage = isSuccess ? '转换完成，日志连接已关闭' : '转换失败，日志连接已关闭'
  addLog(level, logMessage)
  
  // 清除状态检查定时器
  clearStatusCheckInterval()
}

// 处理转换状态
const handleTransferStatus = (status, isRestore = false) => {
  switch (status) {
    case '1': // 转换成功
      if (isRestore && logs.value.some(log => log.message.includes('转换完成'))) {
        // 如果是恢复状态且已有成功日志，则不重复处理
        emit('conversion-success')
      } else {
        const message = isRestore ? '模型转换已完成' : '转换完成，日志连接已关闭'
        handleConversionComplete(true, message)
      }
      break
      
    case '0': // 转换失败
      if (isRestore && logs.value.some(log => log.message.includes('转换失败'))) {
        // 如果是恢复状态且已有失败日志，则不重复处理
        emit('conversion-failed', { message: '模型转换失败' })
      } else {
        const message = isRestore ? '模型转换失败' : '转换失败，日志连接已关闭'
        handleConversionComplete(false, message)
      }
      break
      
    case '2': // 转换中
      if (isRestore) {
        // 恢复转换状态
        isConversionStarted.value = true
        allowReconnect.value = true
        
        // 创建WebSocket连接
        createWebSocketConnection(props.taskId)
        
        // 启动状态检查
        startStatusCheckInterval()
        
        // 添加日志
        addLog('INFO', '检测到正在进行的转换任务，已恢复日志连接')
      }
      // 如果不是恢复状态，则继续等待下一次检查
      break
      
    default:
      addLog('WARNING', `未知的转换状态: ${status}`)
  }
}

// 检查转换状态
const checkTransferStatus = async () => {
  if (!props.taskId || !isConversionStarted.value) return
  
  try {
    const res = await transferStatus(props.taskId)
    // 确保res存在且有数据
    if (res && res.data) {
      // 处理转换状态（0失败 1成功 2转换中）
      handleTransferStatus(res.data.status)
    }
  } catch (error) {
    console.error('检查转换状态失败:', error)
    // 添加异常处理日志
    addLog('ERROR', `检查转换状态失败: ${error.message || '未知错误'}`)
  }
}

// 启动状态检查定时器
const startStatusCheckInterval = () => {
  // 清除可能存在的旧定时器
  clearStatusCheckInterval()
  
  // 每5秒检查一次转换状态
  statusCheckInterval = setInterval(() => {
    checkTransferStatus()
  }, 5000)
}

// 清除状态检查定时器
const clearStatusCheckInterval = () => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval)
    statusCheckInterval = null
  }
}

// 创建WebSocket连接
const createWebSocketConnection = (taskId) => {
  if (!taskId) return
  
  if (wsInstance) {
    wsInstance.disconnect()
  }

  // 重置重连标志
  allowReconnect.value = true

  // 使用标记防止重复错误提示
  let hasShownErrorMessage = false

  // 错误处理函数
  const handleWebSocketError = (error) => {
    if (!hasShownErrorMessage) {
      // 添加错误日志
      addLog('ERROR', `WebSocket连接错误: ${error.message}`)
      
      if (allowReconnect.value) {
        ElNotification({
          title: '错误',
          message: '日志连接发生错误，正在尝试重新连接...',
          type: 'error',
          duration: 5000
        })
      } else {
        ElNotification({
          title: '信息',
          message: '日志连接已关闭',
          type: 'info',
          duration: 5000
        })
      }
      
      // 设置标记，避免重复显示
      hasShownErrorMessage = true
      
      // 5秒后重置标记
      setTimeout(() => {
        hasShownErrorMessage = false
      }, 5000)
    }
  }

  // 修改useWebSocket配置，使用自定义错误处理
  const { connect, disconnect, isConnected } = useWebSocket({
    url: `/websocket/log/${taskId}`,
    onMessage: (message) => {
      // 添加日志
      addLog('INFO', message)
    },
    onError: handleWebSocketError,
    onOpen: () => {
      // 添加连接建立日志
      addLog('INFO', '日志连接已建立')
      // 重置错误提示标记
      hasShownErrorMessage = false
    },
    onClose: () => {
      // 只有在允许重连的情况下才显示重连消息
      if (allowReconnect.value) {
        addLog('INFO', '日志连接已断开，正在尝试重新连接...')
      }
    },
    reconnectLimit: 5,  // 最多重试5次
    heartBeatInterval: 30000,  // 30秒发送一次心跳
    reconnect: () => allowReconnect.value // 只有在允许重连的情况下才重连
  })

  wsInstance = { disconnect, isConnected }
  connect()
}

// 使用防抖函数优化频繁的localStorage存储
let saveLogsTimer = null
const debouncedSaveLogsToStorage = () => {
  if (saveLogsTimer) clearTimeout(saveLogsTimer)
  saveLogsTimer = setTimeout(() => {
    saveLogsToStorage()
  }, 300) // 300ms防抖
}

// 保存日志到localStorage - 只保存最近一次的日志
const saveLogsToStorage = () => {
  if (!props.taskId) return
  try {
    // 保存最新的日志数据
    localStorage.setItem(LATEST_LOG_KEY, JSON.stringify(logs.value))
    // 保存关联的taskId
    localStorage.setItem(LATEST_TASK_ID_KEY, props.taskId)
  } catch (error) {
    console.error('保存日志到localStorage失败:', error)
  }
}

// 从localStorage加载日志
const loadLogsFromStorage = (taskId) => {
  if (!taskId) return []
  try {
    // 获取最新保存的taskId
    const latestTaskId = localStorage.getItem(LATEST_TASK_ID_KEY)
    
    // 只有当请求的taskId与保存的最新taskId匹配时，才返回日志
    if (latestTaskId === taskId) {
      const storedLogs = localStorage.getItem(LATEST_LOG_KEY)
      return storedLogs ? JSON.parse(storedLogs) : []
    }
    
    // 如果taskId不匹配，返回空数组
    return []
  } catch (error) {
    console.error('从localStorage加载日志失败:', error)
    return []
  }
}

// 检查并恢复转换状态
const checkAndRestoreConversionState = async (taskId) => {
  if (!taskId) return
  
  try {
    const res = await transferStatus(taskId)
    if (res && res.data) {
      const status = res.data.status;
      
      // 只处理转换中的状态，成功或失败状态不再触发事件
      if (status === '2') { // 转换中
        // 恢复转换状态
        isConversionStarted.value = true
        allowReconnect.value = true
        
        // 创建WebSocket连接
        createWebSocketConnection(props.taskId)
        
        // 启动状态检查
        startStatusCheckInterval()
        
        // 添加日志
        addLog('INFO', '检测到正在进行的转换任务，已恢复日志连接')
      } else if (status === '1') { // 转换成功
        // 只更新状态，不触发事件
        isConversionStarted.value = false
        allowReconnect.value = false
      } else if (status === '0') { // 转换失败
        // 只更新状态，不触发事件
        isConversionStarted.value = false
        allowReconnect.value = false
      }
    }
  } catch (error) {
    console.error('检查转换状态失败:', error)
    // 添加异常处理日志
    addLog('ERROR', `检查转换状态失败: ${error.message || '未知错误'}`, taskId)
  }
}

// 初始化日志和WebSocket连接
const initLogsAndConnection = (taskId) => {
  if (!taskId) return
  
  // 加载该任务的历史日志
  logs.value = loadLogsFromStorage(taskId)
  
  // 检查当前转换状态
  checkAndRestoreConversionState(taskId)
  
  // 滚动到底部显示最新日志
  scrollToBottom()
}

// 监听taskId变化
watch(() => props.taskId, (newTaskId, oldTaskId) => {
  if (newTaskId) {
    // 如果是新的taskId，初始化连接
    if (newTaskId !== oldTaskId) {
      initLogsAndConnection(newTaskId)
    }
  }
}, { immediate: true })

// 自动滚动到底部 - 使用requestAnimationFrame优化
const scrollToBottom = () => {
  requestAnimationFrame(() => {
    if (logContentRef.value) {
      logContentRef.value.scrollTop = logContentRef.value.scrollHeight
    }
  })
}

// 清空日志
const clearLogs = () => {
  // 清空内存中的日志
  logs.value = []
  newLogIndexes.value = {}
  
  // 清空localStorage中的日志
  localStorage.removeItem(LATEST_LOG_KEY)
  localStorage.removeItem(LATEST_TASK_ID_KEY)
  
  // 移除通知，保留功能
  // ElNotification({
  //   title: '成功',
  //   message: '日志已清空',
  //   type: 'success',
  //   duration: 5000
  // })
}

// 开始转换，建立WebSocket连接
const startConversion = () => {
  isConversionStarted.value = true
  if (props.taskId) {
    createWebSocketConnection(props.taskId)
    // 启动状态检查
    startStatusCheckInterval()
  }
}

// 组件被激活时加载日志
onActivated(() => {
  // 尝试从localStorage获取当前任务ID
  const savedTaskId = localStorage.getItem('current_conversion_task_id')
  
  // 如果props中的taskId为空但localStorage中有值，使用localStorage中的值
  const taskIdToUse = props.taskId || savedTaskId
  
  if (taskIdToUse) {
    // 加载日志
    logs.value = loadLogsFromStorage(taskIdToUse)
    
    // 检查当前转换状态，如果任务仍在进行中，则重新连接WebSocket
    checkAndRestoreConversionState(taskIdToUse)
    
    scrollToBottom()
  }
})

// 组件挂载时只加载历史日志，不建立连接
onMounted(() => {
  if (props.taskId) {
    // 只加载日志，不建立连接
    logs.value = loadLogsFromStorage(props.taskId)
    scrollToBottom()
  }
})

// 组件卸载时断开WebSocket连接和清除定时器
onUnmounted(() => {
  if (wsInstance) {
    wsInstance.disconnect()
  }
  if (saveLogsTimer) {
    clearTimeout(saveLogsTimer)
  }
  // 清除状态检查定时器
  clearStatusCheckInterval()
})

// 暴露方法给父组件使用
defineExpose({
  clearLogs,
  startConversion
})
</script>

<style scoped>
.log-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  will-change: transform; /* 优化动画性能 */
}

.log-container:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.log-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-header h3 {
  font-size: 16px;
  color: #1f2f3d;
  margin: 0;
  transition: color 0.3s ease;
}

.log-container:hover .log-header h3 {
  color: v-bind(themeColor);
}

.log-actions {
  display: flex;
  gap: 8px;
}

.log-content {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.6;
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: v-bind(themeColor) #f4f4f5;
  contain: content; /* 提高滚动性能 */
}

.log-content::-webkit-scrollbar {
  width: 6px;
}

.log-content::-webkit-scrollbar-track {
  background: #f4f4f5;
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb {
  background: v-bind(themeColor);
  border-radius: 3px;
}

.log-item {
  padding: 6px 8px;
  color: #606266;
  border-radius: 4px;
  margin-bottom: 4px;
  transition: background-color 0.3s ease;
  background: transparent;
}

.log-item-new {
  animation: fadeInHighlight 0.5s ease;
}

@keyframes fadeInHighlight {
  0% {
    background-color: v-bind('`${themeColor}33`'); /* 使用v-bind动态绑定主题颜色，33是透明度20% */
  }
  100% {
    background-color: transparent;
  }
}

.log-item:hover {
  background: #f5f7fa;
}

.log-timestamp {
  color: #909399;
  margin-right: 8px;
  font-size: 0.9em;
}

.log-level {
  /* 注意：已通过内联样式绑定主题色，此处样式作为回退 */
  margin-right: 8px;
  font-weight: 500;
}

.log-message {
  color: #606266;
}
</style> 