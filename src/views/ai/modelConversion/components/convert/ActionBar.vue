<template>
  <div class="action-bar">
    <div class="select-group">
      <span class="label">模型文件：</span>
      <el-select 
        v-model="modelFile" 
        placeholder="请选择模型文件"
        class="select-item"
      >
        <el-option
          v-for="model in modelFileList"
          :key="model.value"
          :label="model.label"
          :value="model.value"
        />
      </el-select>
    </div>

    <div class="select-group">
      <span class="label">配置文件：</span>
      <el-select 
        v-model="configFile" 
        placeholder="请选择配置文件"
        class="select-item"
      >
        <el-option
          v-for="config in configFileList"
          :key="config.value"
          :label="config.label"
          :value="config.value"
        />
      </el-select>
    </div>

    <div class="select-group">
      <span class="label">量化图片：</span>
      <el-button 
        type="primary" 
        :icon="Picture"
        class="select-btn"
        @click="handleSelectImages"
      >
        选择图片 ({{ imageCount }})
      </el-button>
    </div>

    <div class="select-group">
      <span class="label">转换工具：</span>
      <el-select 
        v-model="transferTool" 
        placeholder="请选择转换工具"
        class="select-item"
      >
        <el-option
          v-for="tool in transferToolList"
          :key="tool.value"
          :label="tool.label"
          :value="tool.value"
        />
      </el-select>
    </div>

    <el-button 
      type="primary" 
      :icon="VideoPlay"
      class="convert-btn"
      :disabled="props.isConverting || !checkPermi(['ai:transfer:transfer'])"
      @click="handleConvert"
    >
      开始转换
    </el-button>

    <div v-if="props.isConverting" class="converting-indicator">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>转换中...</span>
    </div>

    <el-button
      type="primary"
      :icon="Download"
      class="download-btn"
      :disabled="!props.canDownload"
      @click="handleDownload"
    >
      下载模型
    </el-button>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch, onActivated } from 'vue'
import { Picture, VideoPlay, Download, Loading } from '@element-plus/icons-vue'
import { ElNotification } from 'element-plus'
import { listFile } from '@/api/admin/file'
import { executeTransfer, transferStatus } from '@/api/ai/transfer'
import { nanoid } from 'nanoid'
import { checkPermi } from '@/utils/permission'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'

const userStore = useUserStore()
const settingsStore = useSettingsStore()
const themeColor = computed(() => settingsStore.theme)

const props = defineProps({
  models: {
    type: Array,
    required: true
  },
  configs: {
    type: Array,
    required: true
  },
  modelFile: {
    type: String,
    required: true
  },
  configFile: {
    type: String,
    required: true
  },
  imageCount: {
    type: Number,
    required: true
  },
  imageIdentifiers: {
    type: Array,
    default: () => [],
    required: true
  },
  isConverting: {
    type: Boolean,
    default: false
  },
  canDownload: {
    type: Boolean,
    default: false
  },
  transferTool: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'update:modelFile',
  'update:configFile',
  'update:canDownload',
  'update:transferTool',
  'select-images',
  'convert',
  'download'
])

// 计算是否可以开始转换
const canConvert = computed(() => {
  return props.modelFile && props.configFile && props.imageCount > 0 && transferTool.value
})

// 处理选择图片
const handleSelectImages = () => {
  emit('select-images')
}

// 处理开始转换
const handleConvert = async () => {
  if (!props.modelFile) {
    ElNotification({
      title: '警告',
      message: '请选择模型文件',
      type: 'warning',
      duration: 5000
    })
    return
  }
  if (!props.configFile) {
    ElNotification({
      title: '警告',
      message: '请选择配置文件',
      type: 'warning',
      duration: 5000
    })
    return
  }
  if (props.imageCount === 0) {
    ElNotification({
      title: '警告',
      message: '请上传并选择图片',
      type: 'warning',
      duration: 5000
    })
    return
  }
  if (!transferTool.value) {
    ElNotification({
      title: '警告',
      message: '请选择转换工具',
      type: 'warning',
      duration: 5000
    })
    return
  }

  try {
    // 生成唯一的taskId
    const taskId = nanoid()
    
    // 通知父组件开始转换，更新isConverting状态
    emit('convert', { taskId, status: 'start' })
    
    const response = await executeTransfer({
      taskId,
      modelIdentifier: props.modelFile,
      configIdentifier: props.configFile,
      imageIdentifier: props.imageIdentifiers,
      toolIdentifier: transferTool.value // 添加转换工具标识符
    })
    
    if (response.code === 200) {
      emit('convert', { taskId, status: 'submitted', ...response.data })
    } else {
      // 错误消息已经由响应拦截器处理，这里不需要再显示
      // ElMessage.error(response.msg || '转换任务提交失败')
      // 转换失败，恢复状态
      emit('convert', { taskId, status: 'failed' })
    }
  } catch (error) {
    console.error('模型转换失败:', error)
    // 错误已经由axios响应拦截器处理，这里不需要再显示
    // ElMessage.error('模型转换失败，请稍后重试')
    // 转换失败，恢复状态
    emit('convert', { status: 'failed' })
  }
}

// 处理下载
const handleDownload = () => {
  emit('download')
}

// 监听模型文件变化
const modelFile = computed({
  get: () => props.modelFile,
  set: (value) => emit('update:modelFile', value)
})

// 监听配置文件变化
const configFile = computed({
  get: () => props.configFile,
  set: (value) => emit('update:configFile', value)
})

// 转换工具
const transferTool = computed({
  get: () => props.transferTool,
  set: (value) => emit('update:transferTool', value)
})

// 监听props中modelFile的变化
watch(() => props.modelFile, (newValue) => {
  console.log('props.modelFile变化:', newValue)
})

// 监听props中configFile的变化
watch(() => props.configFile, (newValue) => {
  console.log('props.configFile变化:', newValue)
})

// 模型文件列表
const modelFileList = ref([])

/** 获取模型文件列表 */
async function getModelFileList() {
  try {
    const response = await listFile({
      fileType: 'model',
      pageNum: 1,
      pageSize: 50,
      sortBy: 'batchNo',  // 按批次号排序
      isAsc: 'desc',  // 降序排列，最新的在前面
      createBy: userStore.name // 添加当前登录用户名作为查询参数
    })
    if (response.code === 200 && response.rows) {
      modelFileList.value = response.rows.map(model => ({
        label: model.fileName,
        value: model.fileIdentifier || model.id
      }))

      // 始终选择第一个（最新上传的）
      if (modelFileList.value.length > 0) {
        emit('update:modelFile', modelFileList.value[0].value)
      }
    }
  } catch (error) {
    console.error('获取模型文件列表失败:', error)
  }
}

// 配置文件列表
const configFileList = ref([])

/** 获取配置文件列表 */
async function getConfigFileList() {
  try {
    const response = await listFile({
      fileType: 'config',
      pageNum: 1,
      pageSize: 50,
      sortBy: 'batchNo',  // 按批次号排序
      isAsc: 'desc',  // 降序排列，最新的在前面
      createBy: userStore.name // 添加当前登录用户名作为查询参数
    })
    if (response.code === 200 && response.rows) {
      configFileList.value = response.rows.map(config => ({
        label: config.fileName,
        value: config.fileIdentifier || config.id
      }))

      // 始终选择第一个（最新上传的）
      if (configFileList.value.length > 0) {
        emit('update:configFile', configFileList.value[0].value)
      }
    }
  } catch (error) {
    console.error('获取配置文件列表失败:', error)
  }
}

// 转换工具列表
const transferToolList = ref([])

/** 获取转换工具列表 */
async function getTransferToolList() {
  try {
    const response = await listFile({
      fileType: 'model-transfer-tool',
      pageNum: 1,
      pageSize: 20,
      sortBy: 'createTime',
      isAsc: 'desc',
      // createBy: userStore.name // 添加当前登录用户名作为查询参数  转换工具不需要用户筛选
    })
    if (response.code === 200 && response.rows) {
      transferToolList.value = response.rows.map(tool => ({
        label: tool.fileName,
        value: tool.fileIdentifier || tool.id
      }))
      
      // 如果有工具，默认选择第一个
      if (transferToolList.value.length > 0) {
        emit('update:transferTool', transferToolList.value[0].value)
      }
    }
  } catch (error) {
    console.error('获取转换工具列表失败:', error)
  }
}

// 在组件挂载时获取模型文件列表和配置文件列表
onMounted(() => {
  getModelFileList()
  getConfigFileList()
  getTransferToolList()
})

// 组件被激活时检查下载按钮状态
onActivated(() => {
  // 获取当前任务ID
  const taskId = localStorage.getItem('current_conversion_task_id')
  if (taskId) {
    checkTaskStatus(taskId)
  }
})

// 检查任务状态
const checkTaskStatus = async (taskId) => {
  if (!taskId) return
  
  try {
    const res = await transferStatus(taskId)
    if (res.code === 200 && res.data) {
      // 转换状态（0失败 1成功 2转换中）
      const status = res.data.status
      
      // 根据状态更新UI
      if (status === '1') { // 转换成功
        // 更新下载按钮状态
        emit('update:canDownload', true)
      } else {
        // 其他状态下载按钮不可用
        emit('update:canDownload', false)
      }
    }
  } catch (error) {
    console.error('检查任务状态失败:', error)
  }
}

// 暴露给外部的刷新方法
const refreshModelFiles = async () => {
  try {
    const response = await listFile({
      fileType: 'model',
      pageNum: 1,
      pageSize: 50,
      sortBy: 'batchNo',  // 按批次号排序
      isAsc: 'desc',  // 降序排列，最新的在前面
      createBy: userStore.name // 添加当前登录用户名作为查询参数
    })
    if (response.code === 200 && response.rows) {
      modelFileList.value = response.rows.map(model => ({
        label: model.fileName,
        value: model.fileIdentifier || model.id
      }))

      console.log('刷新后的模型文件列表:', modelFileList.value)

      // 始终选择第一个（最新上传的）
      if (modelFileList.value.length > 0) {
        const newValue = modelFileList.value[0].value
        console.log('选中最新的模型文件:', newValue)
        emit('update:modelFile', newValue)
      }
    }
  } catch (error) {
    console.error('获取模型文件列表失败:', error)
  }
}

const refreshConfigFiles = async () => {
  try {
    const response = await listFile({
      fileType: 'config',
      pageNum: 1,
      pageSize: 50,
      sortBy: 'batchNo',  // 按批次号排序
      isAsc: 'desc',  // 降序排列，最新的在前面
      createBy: userStore.name // 添加当前登录用户名作为查询参数
    })
    if (response.code === 200 && response.rows) {
      configFileList.value = response.rows.map(config => ({
        label: config.fileName,
        value: config.fileIdentifier || config.id
      }))

      console.log('刷新后的配置文件列表:', configFileList.value)

      // 始终选择第一个（最新上传的）
      if (configFileList.value.length > 0) {
        const newValue = configFileList.value[0].value
        console.log('选中最新的配置文件:', newValue)
        emit('update:configFile', newValue)
      }
    }
  } catch (error) {
    console.error('获取配置文件列表失败:', error)
  }
}

const refreshTransferTools = async () => {
  try {
    const response = await listFile({
      fileType: 'model-transfer-tool',
      pageNum: 1,
      pageSize: 999,
      sortBy: 'createTime',
      isAsc: 'desc',
      createBy: userStore.name // 添加当前登录用户名作为查询参数
    })
    if (response.code === 200 && response.rows) {
      transferToolList.value = response.rows.map(tool => ({
        label: tool.fileName,
        value: tool.fileIdentifier || tool.id
      }))
      
      console.log('刷新后的转换工具列表:', transferToolList.value)
      
      // 如果有工具，默认选择第一个
      if (transferToolList.value.length > 0) {
        const newValue = transferToolList.value[0].value
        console.log('选中最新的转换工具:', newValue)
        emit('update:transferTool', newValue)
      }
    }
  } catch (error) {
    console.error('获取转换工具列表失败:', error)
  }
}

// 暴露方法给父组件
defineExpose({
  refreshModelFiles,
  refreshConfigFiles,
  refreshTransferTools,
  checkTaskStatus
})
</script>

<style scoped>
.action-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.action-bar:hover {
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.select-group {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.select-group:hover {
  transform: translateY(-1px);
}

.select-group:hover .label {
  color: v-bind(themeColor);
}

.select-group:hover .el-select {
  box-shadow: 0 2px 8px v-bind('`${themeColor}33`'); /* 使用主题颜色设置阴影，透明度20% */
}

.label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.select-item {
  width: 200px;
  transition: all 0.3s ease;
}

.select-btn {
  min-width: 120px;
  transition: all 0.3s ease;
}

.select-btn:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px v-bind('`${themeColor}33`'); /* 使用主题颜色设置阴影，透明度20% */
}

.convert-btn {
  margin-left: auto;
  width: 120px;
  flex: 0 0 120px;
  transition: all 0.3s ease;
}

.convert-btn:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px v-bind('`${themeColor}33`'); /* 使用主题颜色设置阴影，透明度20% */
}

.download-btn {
  width: 120px;
  flex: 0 0 120px;
  transition: all 0.3s ease;
}

.download-btn:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px v-bind('`${themeColor}33`'); /* 使用主题颜色设置阴影，透明度20% */
}

.converting-indicator {
  width: 120px;
  flex: 0 0 120px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  height: 40px;
  border-radius: 4px;
  background-color: #f0f9eb;
  color: #67c23a;
  font-size: 14px;
  animation: pulse 1.5s infinite;
}

.converting-indicator .el-icon {
  font-size: 18px;
  animation: rotating 2s linear infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 1200px) {
  .action-bar {
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;
  }
  
  .select-group {
    flex: 1 1 calc(50% - 8px);
    min-width: 280px;
    margin: 0;
  }
  
  /* 创建按钮组容器 */
  .action-bar > :nth-last-child(-n+3) {
    flex-basis: auto;
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    margin-top: 8px;
    width: 100%;
  }

  .convert-btn,
  .converting-indicator,
  .download-btn {
    margin: 0;
    flex: 0 0 120px;
  }
}

@media (max-width: 768px) {
  .action-bar {
    gap: 12px;
    padding: 12px;
  }

  .select-group {
    flex: 1 1 100%;
    margin: 0;
  }
  
  /* 小屏幕下按钮组布局 */
  .action-bar > :nth-last-child(-n+3) {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style> 