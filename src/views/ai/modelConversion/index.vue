<template>
  <div class="app-container">
    <div class="model-conversion">
      <div class="upload-section">
        <div class="upload-grid">
          <!-- 原始模型文件上传 -->
          <model-file-upload
              v-model:uploadedModels="uploadedModels"
              v-model:selectedModel="selectedModel"
              @file-uploaded="handleModelFileUploaded"
          />

          <!-- 配置文件上传 -->
          <config-file-upload
              v-model:uploadedConfigs="uploadedConfigs"
              v-model:selectedConfig="selectedConfig"
              @file-uploaded="handleConfigFileUploaded"
          />

          <!-- 图片上传 -->
          <image-upload
              v-model:uploadedImages="uploadedImages"
              ref="imageUploadRef"
              @upload-complete="handleImageUploadComplete"
          />
        </div>
      </div>

      <!-- 操作栏 -->
      <action-bar
          v-model:modelFile="selectedModelFile"
          v-model:configFile="selectedConfigFile"
          v-model:canDownload="canDownload"
          v-model:transferTool="selectedTransferTool"
          :models="uploadedModels"
          :configs="uploadedConfigs"
          :image-count="selectedImages.length"
          :image-identifiers="selectedImages.map(img => img.fileIdentifier)"
          :is-converting="isConverting"
          @select-images="openImageSelector"
          @convert="handleStartConversion"
          @download="handleDownload"
          ref="actionBarRef"
      />

      <!-- 图片选择器弹窗 -->
      <image-selector
          v-model="showImageSelector"
          :defaultSelected="selectedImages"
          @confirm="handleImageSelect"
          ref="imageSelectorRef"
      />



      <!-- 日志组件 -->
      <div class="log-section">
        <keep-alive>
          <Log ref="logRef" :taskId="currentTaskId"
               @conversion-success="handleConversionSuccess"
               @conversion-failed="handleConversionFailed" />
        </keep-alive>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onActivated, onMounted, onUnmounted } from 'vue'
import { ElNotification, ElMessageBox } from 'element-plus'
import ModelFileUpload from './components/upload/ModelFileUpload.vue'
import ConfigFileUpload from './components/upload/ConfigFileUpload.vue'
import ImageUpload from './components/upload/ImageUpload.vue'
import ActionBar from './components/convert/ActionBar.vue'
import ImageSelector from './components/modal/ImageSelector.vue'
import Log from './components/log/log.vue'
import { getDownloadUrl } from '@/api/ai/file'
import { transferStatus } from '@/api/ai/transfer'
import { addAccessRecord } from '@/api/report/index'

// 上传的文件列表
const uploadedModels = ref([])
const uploadedConfigs = ref([])
const uploadedImages = ref([])

// 选中的图片列表
const selectedImages = ref([])

// 选中的示例文件
const selectedModel = ref('')
const selectedConfig = ref('')

// 选中的文件
const selectedModelFile = ref('')
const selectedConfigFile = ref('')
const selectedTransferTool = ref('')

// 图片上传组件引用
const imageUploadRef = ref(null)
const imageSelectorRef = ref(null)
const showImageSelector = ref(false)

// 转换后的模型ID
const convertedModelId = ref(null)

// 转换状态
const isConverting = ref(false)
const canDownload = ref(false)

// 日志组件引用
const logRef = ref(null)

// 当前任务ID
const currentTaskId = ref('')

// 操作栏组件引用
const actionBarRef = ref(null)

// 组件被激活时处理
onActivated(() => {
  // 记录页面访问
  recordPageAccess()
  
  // 恢复之前的任务状态
  const savedTaskId = localStorage.getItem('current_conversion_task_id')
  
  if (savedTaskId) {
    // 检查转换状态，但不显示错误提示
    checkConversionStateQuiet(savedTaskId)
  } else {
    // 如果没有保存的任务ID，则重置状态
    resetConversionState()
  }
  
  // 自动获取并选择最新批次的图片
  autoSelectLatestImages()
})

// 检查转换状态但不显示错误提示
const checkConversionStateQuiet = async (taskId) => {
  if (!taskId) return

  try {
    const res = await transferStatus(taskId)
    if (res.code === 200 && res.data) {
      // 转换状态（0失败 1成功 2转换中）
      const status = res.data.status
      const lastStatus = localStorage.getItem('last_conversion_status')

      // 设置当前任务ID
      currentTaskId.value = taskId
      convertedModelId.value = taskId
      
      if (status === '2') { // 转换中
        // 恢复转换中状态
        isConverting.value = true
        canDownload.value = false

        // 保存状态到localStorage
        localStorage.setItem('last_conversion_status', '2')

        // 设置延时确保DOM更新后再设置taskId
        setTimeout(() => {
          // 不需要清空日志，因为log组件会自动加载历史日志
        }, 0)
      } else if (status === '1') { // 转换成功
        // 恢复成功状态
        isConverting.value = false
        canDownload.value = true



        // 保存下载状态到localStorage
        localStorage.setItem('current_conversion_can_download', 'true')
        localStorage.setItem('last_conversion_status', '1')

        // 确保ActionBar组件能够获取到最新的canDownload状态
        setTimeout(() => {
          if (actionBarRef.value) {
            // 手动触发检查任务状态
            actionBarRef.value.checkTaskStatus && actionBarRef.value.checkTaskStatus(taskId)
          }
        }, 0)
      } else if (status === '0') { // 转换失败
        // 恢复失败状态，但不显示错误提示
        isConverting.value = false
        canDownload.value = false

        // 保存下载状态到localStorage
        localStorage.setItem('current_conversion_can_download', 'false')
        localStorage.setItem('last_conversion_status', '0')
      } else {
        // 未知状态，重置
        resetConversionState()
      }
    } else {
      // API返回错误，重置状态
      resetConversionState()
    }
  } catch (error) {
    console.error('检查转换状态失败:', error)
    // 出错时不重置状态，保持之前的状态
  }
}

// 组件挂载时处理
onMounted(() => {
  // 记录页面访问
  recordPageAccess()
  
  // 恢复之前的任务状态
  const savedTaskId = localStorage.getItem('current_conversion_task_id')
  
  if (savedTaskId) {
    // 检查转换状态，但不显示错误提示
    checkConversionStateQuiet(savedTaskId)
    
    // 立即检查一次任务状态
    checkAllTasksStatus()
  } else {
    // 如果没有保存的任务ID，则重置状态
    resetConversionState()
  }

  // 设置定时器，每10秒检查一次任务状态
  const statusCheckTimer = setInterval(() => {
    checkAllTasksStatus()
  }, 10000)

  // 组件卸载时清除定时器
  onUnmounted(() => {
    if (statusCheckTimer) {
      clearInterval(statusCheckTimer)
    }
  })
  
  // 自动获取并选择最新批次的图片
  autoSelectLatestImages()
})

// 记录页面访问
const recordPageAccess = async () => {
  try {
    await addAccessRecord({
      accessType: 'modelConversion',
      accessCount: 1
    }).catch(err => {
      console.warn('访问记录添加失败，可能是防重复提交导致:', err)
      // 忽略错误，不影响页面正常加载
    })

  } catch (error) {
    console.warn('访问记录添加失败:', error)
    // 忽略错误，不影响页面正常加载
  }
}

// 自动获取并选择最新批次的图片
const autoSelectLatestImages = async () => {
  try {
    // 如果已经有选中的图片，则不需要自动选择
    if (selectedImages.value.length > 0) return
    
    // 打开图片选择器，但不显示UI
    if (imageSelectorRef.value) {
      await imageSelectorRef.value.getImageList()
      // 获取选中的图片并更新状态
      const selectedImgs = imageSelectorRef.value.selectedImages || []
      if (selectedImgs.length > 0) {
        selectedImages.value = [...selectedImgs]
      }
    }
  } catch (error) {
    console.error('自动选择图片失败:', error)
  }
}



// 检查转换状态
const checkConversionState = async (taskId) => {
  if (!taskId) return
  
  try {
    const res = await transferStatus(taskId)
    if (res.code === 200 && res.data) {
      // 转换状态（0失败 1成功 2转换中）
      const status = res.data.status
      
      // 设置当前任务ID
      currentTaskId.value = taskId
      convertedModelId.value = taskId
      
      if (status === '2') { // 转换中
        // 恢复转换中状态
        isConverting.value = true
        canDownload.value = false
        
        // 设置延时确保DOM更新后再设置taskId
        setTimeout(() => {
          // 不需要清空日志，因为log组件会自动加载历史日志
        }, 0)
      } else if (status === '1') { // 转换成功
        // 恢复成功状态
        isConverting.value = false
        canDownload.value = true
        
        console.log('转换成功，设置canDownload为true')
        
        // 保存下载状态到localStorage
        localStorage.setItem('current_conversion_can_download', 'true')
        
        // 确保ActionBar组件能够获取到最新的canDownload状态
        setTimeout(() => {
          if (actionBarRef.value) {
            // 手动触发检查任务状态
            actionBarRef.value.checkTaskStatus && actionBarRef.value.checkTaskStatus(taskId)
          }
        }, 0)
      } else if (status === '0') { // 转换失败
        // 恢复失败状态
        isConverting.value = false
        canDownload.value = false
        
        // 移除重复的错误提示
        // ElMessage.error('模型转换失败')
        
        // 保存下载状态到localStorage
        localStorage.setItem('current_conversion_can_download', 'false')
      } else {
        // 未知状态，重置
        resetConversionState()
      }
    } else {
      // API返回错误，重置状态
      resetConversionState()
    }
  } catch (error) {
    console.error('检查转换状态失败:', error)
    // 出错时不重置状态，保持之前的状态
  }
}

// 重置转换状态
const resetConversionState = () => {
  const oldTaskId = localStorage.getItem('current_conversion_task_id')

  // 清空状态
  currentTaskId.value = ''
  convertedModelId.value = null
  isConverting.value = false
  canDownload.value = false

  // 清除localStorage中的状态
  localStorage.removeItem('current_conversion_task_id')
  localStorage.removeItem('current_conversion_can_download')
  localStorage.removeItem('last_conversion_status')

  // 清除失败通知标记
  if (oldTaskId) {
    localStorage.removeItem('failure_notification_shown_' + oldTaskId)
  }

  // 清空日志
  setTimeout(() => {
    logRef.value?.clearLogs()
  }, 0)
}

// 监听uploadedImages的变化，自动更新selectedImages
watch(uploadedImages, (newImages) => {
  // 如果当前没有选中图片，则将上传的图片设为选中状态
  if (selectedImages.value.length === 0 && newImages.length > 0) {
    selectedImages.value = [...newImages]
  }
})

// 监听selectedModelFile的变化
watch(selectedModelFile, (newValue) => {
  console.log('selectedModelFile变化:', newValue)
})

// 监听selectedConfigFile的变化
watch(selectedConfigFile, (newValue) => {
  console.log('selectedConfigFile变化:', newValue)
})

// 监听selectedTransferTool的变化
watch(selectedTransferTool, (newValue) => {
  console.log('selectedTransferTool变化:', newValue)
})

// 打开图片选择器
const openImageSelector = () => {
  showImageSelector.value = true
}

// 处理图片选择
const handleImageSelect = (images) => {
  // 更新选中的图片列表
  selectedImages.value = images
}

// 开始转换
const handleStartConversion = async (data) => {
  if (isConverting.value) {
    return
  }

  try {
    isConverting.value = true
    canDownload.value = false

    // 保存taskId
    currentTaskId.value = data.taskId
    convertedModelId.value = data.taskId

    // 保存任务ID到localStorage
    localStorage.setItem('current_conversion_task_id', data.taskId)
    localStorage.setItem('current_conversion_can_download', 'false')
    localStorage.setItem('last_conversion_status', '2') // 设置为转换中状态

    // 清除之前的失败通知标记
    localStorage.removeItem('failure_notification_shown_' + data.taskId)

    // 使用requestAnimationFrame确保UI更新后再清空日志
    requestAnimationFrame(() => {
      // 清空之前的日志
      logRef.value?.clearLogs()
      
      // 启动WebSocket连接
      logRef.value?.startConversion()
      
      // 移除这里的通知，保留功能
      // ElNotification({
      //   title: '成功',
      //   message: '开始转换模型',
      //   type: 'success',
      //   duration: 5000
      // })
    })
  } catch (error) {
    ElNotification({
      title: '错误',
      message: '转换失败：' + error.message,
      type: 'error',
      duration: 5000
    })
    canDownload.value = false
    isConverting.value = false
  }
}

// 处理转换成功
const handleConversionSuccess = () => {
  canDownload.value = true
  isConverting.value = false
  
  // 保存下载状态到localStorage
  localStorage.setItem('current_conversion_can_download', 'true')
  
  // 确保ActionBar组件能够获取到最新的canDownload状态
  setTimeout(() => {
    if (actionBarRef.value && actionBarRef.value.checkTaskStatus) {
      actionBarRef.value.checkTaskStatus(currentTaskId.value)
    }
  }, 0)
}

// 处理转换失败
const handleConversionFailed = (data) => {
  canDownload.value = false
  isConverting.value = false

  // 保存下载状态到localStorage
  localStorage.setItem('current_conversion_can_download', 'false')
  localStorage.setItem('last_conversion_status', '0')
}

// 处理下载
const handleDownload = async () => {
  if (!convertedModelId.value) {
    ElNotification({
      title: '警告',
      message: '请先转换模型',
      type: 'warning',
      duration: 5000
    })
    return
  }

  try {
    // 先检查任务状态
    const statusRes = await transferStatus(convertedModelId.value)
    if (statusRes.code === 200 && statusRes.data) {
      const status = statusRes.data.status
      
      if (status !== '1') { // 如果状态不是成功(1)
        if (status === '2') { // 转换中
          ElNotification({
            title: '警告',
            message: '模型正在转换中，请稍后再试',
            type: 'warning',
            duration: 5000
          })
          return
        } else if (status === '0') { // 转换失败
          ElNotification({
            title: '错误',
            message: '模型转换失败，无法下载',
            type: 'error',
            duration: 5000
          })
          return
        } else {
          ElNotification({
            title: '警告',
            message: '模型状态异常，无法下载',
            type: 'warning',
            duration: 5000
          })
          return
        }
      }
      
      // 状态是成功的，可以下载
      const response = await getDownloadUrl(convertedModelId.value)
      if (response.code === 200 && response.data) {
        // 创建一个隐藏的a标签来触发下载
        const link = document.createElement('a')
        link.href = response.data
        link.download = `converted_model_${convertedModelId.value}.zip`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        ElNotification({
          title: '成功',
          message: '开始下载模型',
          type: 'success',
          duration: 5000
        })
        
        // 询问用户是否清除当前任务状态
        ElMessageBox.confirm(
          '下载已开始，是否清除当前转换任务状态？',
          '提示',
          {
            confirmButtonText: '清除',
            cancelButtonText: '保留',
            type: 'info'
          }
        ).then(() => {
          // 用户点击确认，清除状态
          resetConversionState()
          ElNotification({
            title: '成功',
            message: '已清除转换任务状态',
            type: 'success',
            duration: 5000
          })
        }).catch(() => {
          // 用户点击取消，保留状态
          ElNotification({
            title: '信息',
            message: '已保留转换任务状态',
            type: 'info',
            duration: 5000
          })
        })
      } else {
        ElNotification({
          title: '错误',
          message: response.msg || '获取下载链接失败',
          type: 'error',
          duration: 5000
        })
      }
    } else {
      ElNotification({
        title: '错误',
        message: statusRes.msg || '获取任务状态失败',
        type: 'error',
        duration: 5000
      })
    }
  } catch (error) {
    console.error('下载失败:', error)
    ElNotification({
      title: '错误',
      message: '下载失败：' + error.message,
      type: 'error',
      duration: 5000
    })
  }
}

// 处理模型文件上传成功
const handleModelFileUploaded = async (data) => {
  // 通知ActionBar组件刷新模型文件列表
  await actionBarRef.value?.refreshModelFiles()
}

// 处理配置文件上传成功
const handleConfigFileUploaded = async (data) => {
  // 通知ActionBar组件刷新配置文件列表
  await actionBarRef.value?.refreshConfigFiles()
}

// 刷新转换工具列表
const refreshTransferTools = async () => {
  // 通知ActionBar组件刷新转换工具列表
  await actionBarRef.value?.refreshTransferTools()
}

// 检查所有任务的状态
const checkAllTasksStatus = async () => {
  const savedTaskId = localStorage.getItem('current_conversion_task_id')

  if (!savedTaskId) return

  try {
    const res = await transferStatus(savedTaskId)

    if (res.code === 200 && res.data) {
      // 转换状态（0失败 1成功 2转换中）
      const status = res.data.status
      const lastStatus = localStorage.getItem('last_conversion_status')

      // 更新状态
      if (status === '1') { // 转换成功
        if (isConverting.value) {
          handleConversionSuccess()
        }
        localStorage.setItem('last_conversion_status', '1')
      } else if (status === '0') { // 转换失败
        // 检查是否是状态从转换中变为失败，或者是首次检测到失败
        const hasShownFailureNotification = localStorage.getItem('failure_notification_shown_' + savedTaskId)
        const shouldShowNotification = !hasShownFailureNotification || lastStatus === '2' || isConverting.value

        if (shouldShowNotification) {
          // 标记已显示通知，避免重复显示
          localStorage.setItem('failure_notification_shown_' + savedTaskId, 'true')

          // 显示转换失败的通知
          try {
            ElNotification({
              title: '转换失败',
              message: '模型转换失败，请查看详细日志',
              type: 'error',
              duration: 8000
            })
          } catch (notificationError) {
            // 备用提示方式
            alert('模型转换失败，请查看详细日志')
          }
        }

        handleConversionFailed({ message: '模型转换失败' })
        isConverting.value = false
        canDownload.value = false
        localStorage.setItem('current_conversion_can_download', 'false')
        localStorage.setItem('last_conversion_status', '0')
      } else if (status === '2') { // 转换中
        if (!isConverting.value) {
          isConverting.value = true
          canDownload.value = false
        }
        // 记录当前状态
        localStorage.setItem('last_conversion_status', '2')
      }
    }
  } catch (error) {
    console.error('检查任务状态失败:', error)
  }
}

// 处理图片上传完成
const handleImageUploadComplete = async (newImageIdentifiers) => {
  // 图片上传完成后，刷新图片选择器，它会自动选择最新批次的图片
  // 由于后端已经更新了batchNo，所以刚上传的图片会被识别为最新批次
  if (newImageIdentifiers && newImageIdentifiers.length > 0 && imageSelectorRef.value) {
    try {
      // 刷新图片选择器的图片列表，会自动选择最新批次（包含刚上传的图片）
      await imageSelectorRef.value.getImageList()

      // 获取选中的图片并更新状态
      const selectedImgs = imageSelectorRef.value.selectedImages || []
      if (selectedImgs.length > 0) {
        selectedImages.value = [...selectedImgs]
      }
    } catch (error) {
      console.error('处理图片上传完成失败:', error)
      // 如果出错，使用简单的方式更新选中图片
      selectedImages.value = newImageIdentifiers.map(identifier => ({
        fileIdentifier: identifier
      }))
    }
  }
}
</script>

<style scoped>
.model-conversion {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-section {
  margin-bottom: 24px;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.log-section {
  margin-top: 24px;
}

@media (max-width: 1200px) {
  .upload-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .upload-grid {
    grid-template-columns: 1fr;
  }
}
</style> 