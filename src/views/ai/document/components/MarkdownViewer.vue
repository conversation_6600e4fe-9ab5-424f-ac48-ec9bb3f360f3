<template>
  <div class="markdown-viewer">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span class="header-title">{{ props.currentDoc?.displayFileName || '文档内容' }}</span>
          </div>
          <div class="header-right">
            <el-button-group class="nav-buttons">
              <el-button 
                :disabled="!hasPrev" 
                @click="handlePrev"
                :icon="ArrowLeft"
                text
              >
                上一篇
              </el-button>
              <el-button 
                :disabled="!hasNext" 
                @click="handleNext"
                text
              >
                下一篇
                <el-icon class="el-icon--right"><ArrowRight /></el-icon>
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      <div class="content-container">
        <!-- 目录区域 -->
        <div class="toc-container" :class="{ 'toc-collapsed': tocCollapsed }" :style="{ width: tocCollapsed ? '36px' : tocWidth + 'px' }">
          <div class="toc-header">
            <span class="toc-title" v-if="!tocCollapsed">目录</span>
            <el-button type="text" @click="toggleToc" class="toc-toggle-btn">
              <el-icon>
                <component :is="tocCollapsed ? 'ArrowRight' : 'ArrowLeft'" />
              </el-icon>
            </el-button>
          </div>
          <div v-show="!tocCollapsed" class="toc-content">
            <el-scrollbar height="calc(100vh - 240px)">
              <div v-if="!props.content || tocItems.length === 0" class="toc-empty">
                暂无目录
              </div>
              <ul v-else class="toc-list">
                <li 
                  v-for="(item, index) in tocItems" 
                  :key="index" 
                  :class="[
                    'toc-item', 
                    `toc-level-${item.level}`,
                    { 'toc-active': item.id === activeHeadingId }
                  ]"
                  @click="scrollToHeading(item.id)"
                >
                  {{ item.text }}
                </li>
              </ul>
            </el-scrollbar>
          </div>
        </div>
        
        <!-- 分隔条 -->
        <div 
          v-show="!tocCollapsed" 
          class="toc-resizer" 
          @mousedown="startResize"
        ></div>
        
        <!-- 内容区域 -->
        <el-scrollbar ref="contentScrollbar" height="calc(100vh - 200px)" class="content-scrollbar">
          <div v-if="!props.content" class="empty-content">
            <el-empty description="请选择要查看的文档" />
          </div>
          <div v-else class="markdown-content" v-html="renderedContent"></div>
        </el-scrollbar>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ArrowLeft, ArrowRight, ArrowDown } from '@element-plus/icons-vue'
import MarkdownIt from 'markdown-it'
import 'highlight.js/styles/github.css'
import hljs from 'highlight.js'
import katex from 'katex'
import 'katex/dist/katex.min.css'
import texmath from 'markdown-it-texmath'
import markdownItSub from 'markdown-it-sub'
import markdownItSup from 'markdown-it-sup'
import markdownItTaskLists from 'markdown-it-task-lists'
import markdownItFootnote from 'markdown-it-footnote'
import markdownItImageFigures from 'markdown-it-image-figures'
import markdownItImplicitFigures from 'markdown-it-implicit-figures'
import markdownItDeflist from 'markdown-it-deflist'
import markdownItAttrs from 'markdown-it-attrs'

const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  currentDoc: {
    type: Object,
    default: () => null
  },
  docList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['switch-doc'])

// 目录折叠状态
const tocCollapsed = ref(false)
// 目录项列表
const tocItems = ref([])
// 内容滚动区域引用
const contentScrollbar = ref(null)
// 当前活动的目录项
const activeHeadingId = ref('')
// 目录宽度
const tocWidth = ref(240)
// 是否正在调整大小
const isResizing = ref(false)
// 初始宽度和鼠标位置
const initialWidth = ref(0)
const initialMouseX = ref(0)

// 是否有上一篇
const hasPrev = computed(() => {
  return props.currentDoc && props.currentDoc.index > 0
})

// 是否有下一篇
const hasNext = computed(() => {
  return props.currentDoc && props.currentDoc.index < props.currentDoc.total - 1
})

// 处理上一篇
const handlePrev = () => {
  if (hasPrev.value) {
    const prevDoc = props.docList[props.currentDoc.index - 1]
    emit('switch-doc', prevDoc)
  }
}

// 处理下一篇
const handleNext = () => {
  if (hasNext.value) {
    const nextDoc = props.docList[props.currentDoc.index + 1]
    emit('switch-doc', nextDoc)
  }
}

// 开始调整大小
const startResize = (e) => {
  isResizing.value = true
  initialWidth.value = tocWidth.value
  initialMouseX.value = e.clientX
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  
  // 添加禁止选择文本的样式
  document.body.classList.add('resizing')
}

// 处理调整大小
const handleResize = (e) => {
  if (!isResizing.value) return
  
  const dx = e.clientX - initialMouseX.value
  let newWidth = initialWidth.value + dx
  
  // 限制最小和最大宽度
  newWidth = Math.max(120, Math.min(350, newWidth))
  
  tocWidth.value = newWidth
}

// 停止调整大小
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  
  // 移除禁止选择文本的样式
  document.body.classList.remove('resizing')
}

// 在组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  
  // 清理任务列表复选框的事件监听器
  const checkboxes = document.querySelectorAll('.markdown-content input[type=checkbox].md-checkbox')
  checkboxes.forEach((checkbox) => {
    checkbox.removeEventListener('change', handleTaskCheckboxChange)
  })
})

// 创建markdown-it实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value
      } catch (__) {}
    }
    return '' // 使用默认的转义
  }
})
.use(texmath, {
  engine: katex,
  delimiters: 'dollars',
  katexOptions: { 
    macros: {},
    throwOnError: false,
    strict: false
  }
})
.use(markdownItSub)
.use(markdownItSup)
.use(markdownItTaskLists, { enabled: true, label: true, labelAfter: true })
.use(markdownItFootnote)
.use(markdownItImageFigures, {
  figcaption: true,
  lazy: true,
  async: true,
  classes: 'md-image'
})
.use(markdownItImplicitFigures, {
  figcaption: true,
  dataType: false,
  tabindex: false
})
.use(markdownItDeflist)
.use(markdownItAttrs, {
  leftDelimiter: '{',
  rightDelimiter: '}',
  allowedAttributes: ['id', 'class', 'style']
})

// 启用参考式链接和图片定义
md.enable(['reference', 'image', 'linkify'])

// 增强引用图片处理
const originalRender = md.render.bind(md)
md.render = function(src, env) {
  // 查找并处理参考式图片定义
  const refImageRegex = /\[([^\]]+)\]:\s*(https?:\/\/[^\s]+)(?:\s+"([^"]+)")?/g
  const imageRefs = {}
  let match

  while ((match = refImageRegex.exec(src)) !== null) {
    const id = match[1]
    const url = match[2]
    const title = match[3] || ''
    imageRefs[id] = { url, title }
  }
  
  // 处理内联图标引用
  const inlineIconRegex = /!\[([^\]]+)\]/g
  src = src.replace(inlineIconRegex, (match, id) => {
    if (imageRefs[id]) {
      return `<img src="${imageRefs[id].url}" alt="${id}" title="${imageRefs[id].title}" class="inline-icon" />`
    }
    return match
  })
  
  return originalRender(src, env)
}

// 提取标题生成目录
const extractToc = (content) => {
  if (!content) {
    tocItems.value = []
    return
  }
  
  const headingRegex = /^(#{1,6})\s+(.+)$/gm
  const matches = []
  let match
  
  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length
    const text = match[2].trim()
    // 生成干净且唯一的ID
    const id = `heading-${text.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\u4e00-\u9fa5-]/g, '')}`
    
    matches.push({
      level,
      text,
      id
    })
  }
  
  tocItems.value = matches
}

// 滚动到指定标题
const scrollToHeading = (id) => {
  nextTick(() => {
    const element = document.getElementById(id)
    if (element && contentScrollbar.value) {
      // 获取元素相对于滚动容器的位置
      const scrollWrapper = contentScrollbar.value.$el.querySelector('.el-scrollbar__wrap')
      
      // 计算元素相对于滚动容器的偏移量
      const offsetTop = element.offsetTop
      
      // 获取滚动容器的高度
      const scrollHeight = scrollWrapper.clientHeight
      
      // 计算滚动位置，确保标题在视口的上方1/4位置
      const targetPosition = offsetTop - scrollHeight * 0.25
      
      // 立即设置活动标题ID，避免滚动过程中错误触发updateActiveHeading
      activeHeadingId.value = id
      
      // 移除所有标题的活动样式
      document.querySelectorAll('.doc-heading-active').forEach(el => {
        el.classList.remove('doc-heading-active')
      })
      
      // 添加活动样式
      element.classList.add('doc-heading-active')
      
      // 临时禁用滚动监听，防止滚动过程中触发updateActiveHeading
      const scrollListener = scrollWrapper.onscroll
      scrollWrapper.onscroll = null
      
      // 平滑滚动到目标位置
      smoothScrollTo(scrollWrapper, scrollWrapper.scrollTop, targetPosition, 300)
      
      // 滚动完成后恢复滚动监听
      setTimeout(() => {
        // 重新绑定滚动监听
        if (scrollListener) {
          scrollWrapper.onscroll = scrollListener
        }
      }, 350)
    } else {
      console.warn(`找不到ID为 ${id} 的元素`, { allIds: document.querySelectorAll('[id]') })
    }
  })
}

// 平滑滚动函数
const smoothScrollTo = (element, start, end, duration) => {
  const startTime = performance.now()
  const change = end - start
  
  const animateScroll = (currentTime) => {
    const elapsedTime = currentTime - startTime
    
    if (elapsedTime >= duration) {
      element.scrollTop = end
      return
    }
    
    // 使用easeInOutQuad缓动函数
    const progress = elapsedTime / duration
    const easeProgress = progress < 0.5 
      ? 2 * progress * progress 
      : 1 - Math.pow(-2 * progress + 2, 2) / 2
    
    element.scrollTop = start + change * easeProgress
    requestAnimationFrame(animateScroll)
  }
  
  requestAnimationFrame(animateScroll)
}

// 监听滚动事件，更新当前活动的目录项
const setupScrollListener = () => {
  if (!contentScrollbar.value) return
  
  const scrollWrapper = contentScrollbar.value.$el.querySelector('.el-scrollbar__wrap')
  
  scrollWrapper.onscroll = () => {
    if (!props.content || tocItems.value.length === 0) return
    
    // 延迟执行以提高性能
    if (scrollWrapper.scrollTimeout) {
      clearTimeout(scrollWrapper.scrollTimeout)
    }
    
    scrollWrapper.scrollTimeout = setTimeout(() => {
      updateActiveHeading(scrollWrapper.scrollTop)
    }, 100)
  }
}

// 根据滚动位置更新当前活动的目录项
const updateActiveHeading = (scrollTop) => {
  if (!props.content || tocItems.value.length === 0) return
  
  // 获取所有标题元素
  const headings = []
  const scrollWrapper = contentScrollbar.value.$el.querySelector('.el-scrollbar__wrap')
  const containerTop = scrollWrapper.getBoundingClientRect().top
  const viewportHeight = scrollWrapper.clientHeight
  
  // 查找所有具有doc-heading类的标题元素
  document.querySelectorAll('.doc-heading').forEach(heading => {
    if (heading.id) {
      const rect = heading.getBoundingClientRect()
      // 计算标题相对于滚动容器的位置
      const relativeTop = rect.top - containerTop
      
      headings.push({
        id: heading.id,
        top: relativeTop,
        element: heading
      })
    }
  })
  
  // 按位置排序
  headings.sort((a, b) => a.top - b.top)
  
  // 找到当前位置对应的标题
  let activeId = ''
  let activeElement = null
  const threshold = viewportHeight * 0.3 // 视口高度的30%作为阈值
  
  // 优先选择视口上方1/3区域内的标题
  const visibleHeadings = headings.filter(h => 
    h.top >= 0 && h.top <= threshold
  )
  
  if (visibleHeadings.length > 0) {
    // 如果有可见标题，选择第一个可见的
    activeId = visibleHeadings[0].id
    activeElement = visibleHeadings[0].element
  } else {
    // 否则选择最后一个已经滚过的标题
    const lastPassedHeading = [...headings].reverse().find(h => h.top < 0)
    if (lastPassedHeading) {
      activeId = lastPassedHeading.id
      activeElement = lastPassedHeading.element
    } else if (headings.length > 0) {
      // 如果所有标题都在视口下方，选择第一个
      activeId = headings[0].id
      activeElement = headings[0].element
    }
  }
  
  // 如果新ID与当前不同，更新当前活动的目录项
  if (activeId !== activeHeadingId.value) {
    activeHeadingId.value = activeId
    
    // 更新文档中标题的活动样式
    document.querySelectorAll('.doc-heading-active').forEach(el => {
      el.classList.remove('doc-heading-active')
    })
    
    if (activeElement) {
      activeElement.classList.add('doc-heading-active')
    }
    
    // 将当前活动的目录项滚动到可见区域
    nextTick(() => {
      const activeItem = document.querySelector('.toc-active')
      if (activeItem && !tocCollapsed.value) { // 只在目录展开状态下滚动
        const tocScrollbar = activeItem.closest('.el-scrollbar__wrap')
        if (tocScrollbar) {
          const itemTop = activeItem.offsetTop
          const scrollTop = tocScrollbar.scrollTop
          const scrollHeight = tocScrollbar.clientHeight
          
          // 如果当前项不在可见区域，滚动到合适位置
          if (itemTop < scrollTop || itemTop > scrollTop + scrollHeight) {
            // 平滑滚动到目标位置，使项目居中
            const targetPosition = itemTop - scrollHeight / 2
            smoothScrollTo(tocScrollbar, scrollTop, targetPosition, 200)
          }
        }
      }
    })
  }
}

// 在组件挂载后设置滚动监听
onMounted(() => {
  nextTick(() => {
    setupScrollListener()
  })
})

// 在内容变化后重新设置滚动监听
watch(() => props.content, () => {
  nextTick(() => {
    // 重置活动标题
    activeHeadingId.value = ''
    // 内容加载后，重新检查当前位置，并滚动到顶部
    setTimeout(() => {
      if (contentScrollbar.value) {
        const scrollWrapper = contentScrollbar.value.$el.querySelector('.el-scrollbar__wrap')
        // 平滑滚动到顶部
        smoothScrollTo(scrollWrapper, scrollWrapper.scrollTop, 0, 300)
        // 延迟更新活动标题，确保滚动完成
        setTimeout(() => {
          updateActiveHeading(0)
        }, 350)
      }
    }, 100)
  })
})

// 修改markdown渲染，为标题添加id
const addHeadingIds = (html) => {
  // 首先，解析HTML为DOM，这样可以更可靠地处理内容
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  // 查找所有标题元素
  const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')
  
  // 创建ID映射，避免重复ID
  const idMap = {}
  
  // 为每个标题添加ID
  headings.forEach(heading => {
    const text = heading.textContent.trim()
    // 基础ID
    let baseId = `heading-${text.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\u4e00-\u9fa5-]/g, '')}`
    
    // 处理重复ID的情况
    let id = baseId
    let counter = 1
    while (idMap[id]) {
      id = `${baseId}-${counter++}`
    }
    
    // 记录ID已使用
    idMap[id] = true
    
    // 设置ID
    heading.id = id
    
    // 为了更容易定位，给标题添加一个特殊的类
    heading.classList.add('doc-heading')
  })
  
  // 处理任务列表复选框的点击事件
  const taskCheckboxes = doc.querySelectorAll('input[type=checkbox].task-list-item-checkbox')
  taskCheckboxes.forEach((checkbox) => {
    // 移除disabled属性，使复选框可点击
    checkbox.removeAttribute('disabled')
    // 添加自定义类名用于样式调整
    checkbox.classList.add('md-checkbox')
  })

  // 返回处理后的HTML内容
  return doc.body.innerHTML
}

// 为图片添加no-referrer策略
const addNoReferrerToImages = (html) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  
  // 查找所有图片元素
  const images = doc.querySelectorAll('img')
  
  // 为每个图片添加referrerpolicy属性
  images.forEach(img => {
    img.setAttribute('referrerpolicy', 'no-referrer')
    
    // 保留内联图标的类
    if (img.classList.contains('inline-icon')) {
      // 确保保留内联图标的样式
      img.style.display = 'inline-block'
      img.style.verticalAlign = 'text-bottom'
    }
  })
  
  return doc.body.innerHTML
}

// 渲染markdown内容
const renderedContent = computed(() => {
  if (!props.content) return ''
  let html = md.render(props.content)
  html = addHeadingIds(html)
  html = addNoReferrerToImages(html)
  
  // 内容渲染后，异步提取目录
  setTimeout(() => {
    extractTocFromDOM()
    // 添加任务列表复选框的事件监听
    setupTaskListListeners()
  }, 50)
  
  return html
})

// 从DOM中提取目录，这样可以确保ID与实际标题ID一致
const extractTocFromDOM = () => {
  if (!props.content) {
    tocItems.value = []
    return
  }
  
  // 获取所有具有doc-heading类的标题元素
  const headingElements = document.querySelectorAll('.doc-heading')
  const items = []
  
  headingElements.forEach(heading => {
    // 获取标题级别
    const level = parseInt(heading.tagName.substring(1))
    // 获取标题文本
    const text = heading.textContent.trim()
    // 获取标题ID
    const id = heading.id
    
    if (id) {
      items.push({
        level,
        text,
        id
      })
    }
  })
  
  tocItems.value = items
}

// 从Markdown文本中提取目录，保留老的extractToc函数作为备用，但改名为extractTocFromMarkdown
const extractTocFromMarkdown = (content) => {
  if (!content) {
    tocItems.value = []
    return
  }
  
  const headingRegex = /^(#{1,6})\s+(.+)$/gm
  const matches = []
  let match
  
  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length
    const text = match[2].trim()
    // 生成干净且唯一的ID
    const id = `heading-${text.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\u4e00-\u9fa5-]/g, '')}`
    
    matches.push({
      level,
      text,
      id
    })
  }
  
  tocItems.value = matches
}

// 切换目录折叠状态
const toggleToc = () => {
  tocCollapsed.value = !tocCollapsed.value
}

// 设置任务列表复选框的点击事件监听器
const setupTaskListListeners = () => {
  const checkboxes = document.querySelectorAll('.markdown-content input[type=checkbox].md-checkbox')
  checkboxes.forEach((checkbox) => {
    checkbox.addEventListener('change', handleTaskCheckboxChange)
  })
}

// 处理任务列表复选框的点击事件
const handleTaskCheckboxChange = (event) => {
  const checkbox = event.target
  // 这里可以添加代码来保存任务列表的状态，如果需要的话
  console.log('Task checkbox changed:', checkbox.checked, checkbox.parentElement.textContent)
  
  // 如果需要将更改持久化到Markdown内容，可以在此触发emit事件
  // emit('update-task', { text: checkbox.parentElement.textContent.trim(), checked: checkbox.checked })
}
</script>

<style scoped>
.markdown-viewer {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  height: 30px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.nav-buttons {
  margin-left: 16px;
}

.nav-buttons :deep(.el-button) {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.nav-buttons :deep(.el-button:not(:disabled):hover) {
  color: var(--el-color-primary);
}

.content-container {
  display: flex;
  height: calc(100vh - 200px);
}

/* 目录样式 */
.toc-container {
  border-right: 1px solid var(--el-border-color-light);
  transition: all 0.3s;
  flex-shrink: 0;
  overflow: hidden;
}

/* 分隔条样式 */
.toc-resizer {
  width: 5px;
  background-color: transparent;
  cursor: col-resize;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.toc-resizer:hover,
.toc-resizer:active {
  background-color: var(--el-color-primary-light-7);
}

.toc-collapsed {
  border-right: none;
}

.toc-header {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--el-border-color-light);
}

.toc-collapsed .toc-header {
  padding: 10px 0;
  border-bottom: none;
  justify-content: center;
}

.toc-toggle-btn {
  padding: 2px;
}

.toc-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.toc-content {
  padding: 10px 0;
}

.toc-empty {
  padding: 10px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  text-align: center;
}

.toc-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.toc-item {
  padding: 6px 10px 6px 20px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.2s;
}

.toc-item:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.toc-level-1 {
  padding-left: 10px;
  font-weight: 600;
}

.toc-level-2 {
  padding-left: 20px;
}

.toc-level-3 {
  padding-left: 30px;
  font-size: 13px;
}

.toc-level-4, .toc-level-5, .toc-level-6 {
  padding-left: 40px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 当前活动的目录项样式 */
.toc-active {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
  font-weight: bold;
  position: relative;
}

.toc-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--el-color-primary);
}

/* 内容区域 */
.content-scrollbar {
  flex-grow: 1;
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--el-text-color-secondary);
}

.markdown-content {
  padding: 24px;
}

:deep(.markdown-content) {
  font-size: 15px;
  line-height: 1.8;
  color: var(--el-text-color-primary);
}

:deep(.markdown-content h1) {
  font-size: 32px;
  margin: 28px 0 18px;
  font-weight: 600;
  line-height: 1.25;
  padding-top: 20px;
  scroll-margin-top: 70px;
}

:deep(.markdown-content h2) {
  font-size: 24px;
  margin: 24px 0 16px;
  font-weight: 600;
  line-height: 1.25;
  padding-top: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-light);
  scroll-margin-top: 70px;
}

:deep(.markdown-content h3) {
  font-size: 20px;
  margin: 20px 0 14px;
  font-weight: 600;
  line-height: 1.25;
  padding-top: 10px;
  scroll-margin-top: 70px;
}

:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin: 18px 0 12px;
  font-weight: 600;
  line-height: 1.25;
  padding-top: 8px;
  scroll-margin-top: 70px;
}

:deep(.markdown-content p) {
  margin: 16px 0;
  line-height: 1.8;
}

:deep(.markdown-content pre) {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  margin: 16px 0;
  overflow: auto;
}

:deep(.markdown-content code) {
  font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 14px;
  padding: 2px 6px;
  background-color: rgba(175, 184, 193, 0.2);
  border-radius: 4px;
}

:deep(.markdown-content pre code) {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
}

:deep(.markdown-content ul),
:deep(.markdown-content ol) {
  padding-left: 28px;
  margin: 16px 0;
}

:deep(.markdown-content li) {
  margin: 8px 0;
}

:deep(.markdown-content blockquote) {
  margin: 16px 0;
  padding: 0 16px;
  color: #57606a;
  border-left: 4px solid #d0d7de;
}

:deep(.markdown-content img) {
  max-width: 100%;
  margin: 16px 0;
  border-radius: 6px;
}

:deep(.markdown-content table) {
  border-collapse: collapse;
  margin: 16px 0;
  width: 100%;
}

:deep(.markdown-content table th),
:deep(.markdown-content table td) {
  padding: 8px 16px;
  border: 1px solid var(--el-border-color-lighter);
}

:deep(.markdown-content table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

:deep(.markdown-content hr) {
  height: 1px;
  background-color: var(--el-border-color-light);
  border: none;
  margin: 24px 0;
}

:deep(.markdown-content a) {
  color: var(--el-color-primary);
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

/* 任务列表样式 */
:deep(.markdown-content ul.task-list) {
  list-style-type: none;
  padding-left: 10px;
}

:deep(.markdown-content li.task-list-item) {
  position: relative;
  padding-left: 28px;
  margin: 8px 0;
}

:deep(.markdown-content .task-list-item-checkbox) {
  position: absolute;
  left: 0;
  top: 3px;
  height: 18px;
  width: 18px;
  cursor: pointer;
  margin: 0;
}

:deep(.markdown-content .task-list-item-checkbox:checked + label) {
  text-decoration: line-through;
  color: var(--el-text-color-placeholder);
}

/* 内联图标样式 */
:deep(.markdown-content .inline-icon) {
  height: 1.2em;
  width: auto;
  vertical-align: text-bottom;
  margin: 0 0.2em;
  display: inline-block;
}

:deep(.markdown-content img.md-image) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 16px 0;
  border-radius: 6px;
}

/* 为标题添加锚点图标 */
:deep(.doc-heading) {
  position: relative;
  cursor: pointer;
}

:deep(.doc-heading:hover::before) {
  /* content: '#'; */
  position: absolute;
  left: -1em;
  color: var(--el-color-primary);
  opacity: 0.6;
}

/* 文档中活动标题的样式 */
:deep(.doc-heading-active) {
  color: var(--el-color-primary);
  position: relative;
}

:deep(.doc-heading-active::before) {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--el-color-primary);
}

:deep(.doc-heading-active::after) {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 100%;
  height: 0;
  border-bottom: 1px dashed var(--el-color-primary-light-5);
}
</style>

<style>
/* 全局样式：调整大小时禁止文本选择 */
body.resizing {
  user-select: none;
  cursor: col-resize;
}

/* KaTeX 数学公式容器样式优化 */
.katex-display {
  padding: 8px 0;
  overflow-x: auto;
  overflow-y: hidden;
}
</style> 