<template>
  <div class="document-list">
    <el-card class="box-card" :class="{ 'is-resizing': isResizing }">
      <!-- 头部搜索区 -->
      <template #header>
        <div class="card-header">
          <span class="header-title">文档列表</span>
          <el-input
            v-model="queryParams.fileName"
            placeholder="请输入文档名称"
            clearable
            class="search-input"
            :prefix-icon="Search"
            @keyup.enter="handleQuery"
          />
        </div>
      </template>
      <!-- 文档列表区 -->
      <div class="card-body">
        <el-scrollbar class="scrollbar-wrapper">
          <el-empty v-if="documentList.length === 0" description="暂无文档" />
          <el-menu v-else class="doc-menu" :default-active="activeDoc">
            <el-menu-item
              v-for="(doc, index) in documentList"
              :key="doc.id"
              :index="doc.id.toString()"
              :data-id="doc.id"
              @click="handleDocumentClick(doc, index)"
            >
              <el-icon><Document /></el-icon>
              <span class="doc-title">{{ getDisplayFileName(doc.fileName) }}</span>
            </el-menu-item>
          </el-menu>
        </el-scrollbar>
        <!-- 分页区 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :total="total"
            :page-sizes="[10, 20]"
            background
            layout="prev, pager, next"
            class="pagination-mobile"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :total="total"
            :page-sizes="[10, 20, 30, 50]"
            background
            layout="total, prev, pager, next"
            class="pagination-desktop"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, defineExpose, inject } from 'vue'
import { Document, Search } from '@element-plus/icons-vue'
import { listFile, getDownloadUrl } from '@/api/admin/file'
import { ElNotification } from 'element-plus'

const emit = defineEmits(['select-document'])

// 当前选中的文档
const activeDoc = ref('')

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileType: 'LFNN-document',
  fileName: ''
})

// 总条数
const total = ref(0)

// 文档列表
const documentList = ref([])

// 是否正在调整大小
const isResizing = ref(false)

// 提供一个方法给父组件调用，用于设置调整大小状态
const setResizing = (status) => {
  isResizing.value = status
}

// 获取文档列表
const getDocumentList = async () => {
  try {
    const res = await listFile(queryParams.value)
    documentList.value = res.rows || []
    total.value = res.total
  } catch (error) {
    ElNotification.error({
      title: '错误',
      message: '获取文档列表失败'
    })
  }
}

// 处理查询
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getDocumentList()
}

// 处理每页条数改变
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
  getDocumentList()
}

// 处理页码改变
const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
  getDocumentList()
}

// 处理文件名显示，去掉后缀名
const getDisplayFileName = (fileName) => {
  if (!fileName) return ''
  // 查找最后一个点的位置
  const lastDotIndex = fileName.lastIndexOf('.')
  // 如果没有找到点或者点在开头，则返回原文件名
  if (lastDotIndex <= 0) return fileName
  // 返回不包含后缀的文件名
  return fileName.substring(0, lastDotIndex)
}

// 处理文档点击
const handleDocumentClick = async (doc, index) => {
  try {
    activeDoc.value = doc.id.toString()
    // 获取文档下载地址
    const res = await getDownloadUrl(doc.fileIdentifier, 3600, false,'LFNN-document')
    // 获取文档内容
    const response = await fetch(res.data)
    const content = await response.text()
    emit('select-document', {
      content,
      currentDoc: doc,
      currentIndex: index,
      total: documentList.value.length,
      list: documentList.value
    })
  } catch (error) {
    ElNotification.error({
      title: '错误',
      message: '获取文档内容失败'
    })
  }
}

// 根据文档ID选择文档
const selectDocumentById = async (docId) => {
  if (!docId) return
  
  try {
    // 确保文档列表已加载
    if (documentList.value.length === 0) {
      await getDocumentList()
    }
    
    // 查找匹配的文档
    const index = documentList.value.findIndex(doc => doc.id.toString() === docId.toString())
    if (index !== -1) {
      const doc = documentList.value[index]
      handleDocumentClick(doc, index)
      return true
    } else {
      console.warn('未找到指定ID的文档:', docId)
      // 如果没找到指定文档但列表不为空，选择第一个
      if (documentList.value.length > 0) {
        handleDocumentClick(documentList.value[0], 0)
        return true
      }
    }
  } catch (error) {
    console.error('选择文档失败:', error)
    ElNotification.error({
      title: '错误',
      message: '选择文档失败'
    })
  }
  
  return false
}

// 选择第一个文档
const selectFirstDocument = async () => {
  try {
    // 确保文档列表已加载
    if (documentList.value.length === 0) {
      await getDocumentList()
    }
    
    // 如果列表不为空，选择第一个文档
    if (documentList.value.length > 0) {
      handleDocumentClick(documentList.value[0], 0)
      return true
    }
  } catch (error) {
    console.error('选择第一个文档失败:', error)
    ElNotification.error({
      title: '错误',
      message: '选择第一个文档失败'
    })
  }
  
  return false
}

// 组件挂载时获取文档列表
onMounted(async () => {
  await getDocumentList()
})

// 暴露方法给父组件使用
defineExpose({
  selectDocumentById,
  getDocumentList,
  setResizing,
  selectFirstDocument
})
</script>

<style scoped>
.document-list {
  height: 100%;
  min-width: 200px; /* 设置最小宽度，可以根据需要调整这个值 */
}

.box-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;
}

.box-card.is-resizing {
  box-shadow: 0 0 0 2px var(--el-color-primary) !important;
}

.box-card :deep(.el-card__body) {
  flex: 1;
  padding: 0;
}

.card-body {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow:visible
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.search-input {
  width: 180px;
}

.search-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.search-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.scrollbar-wrapper {
  flex: 1;
  padding: 2px;
  height: 0; /* 配合flex:1使用 */
}

.doc-menu {
  border-right: none;
}

.el-menu-item {
  height: 50px;
  line-height: 50px;
  border-radius: 4px;
  margin: 4px 0;
}

.el-menu-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.el-menu-item.is-active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.el-menu-item .el-icon {
  margin-right: 12px;
  font-size: 18px;
}

.doc-title {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-container {
  padding: 12px 16px;
  background-color: var(--el-fill-color-blank);
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 移动端分页样式 */
.pagination-mobile {
  display: none;
}

/* 在宽度大于768px时显示完整分页 */
@media screen and (min-width: 768px) {
  .pagination-mobile {
    display: none;
  }
  .pagination-desktop {
    display: flex;
  }
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: var(--el-color-primary);
}

:deep(.el-pagination) {
  justify-content: center;
  white-space: nowrap;
}

/* 确保分页按钮在窄屏下也能正常显示 */
:deep(.el-pagination .el-pager) {
  margin: 0 6px;
}

:deep(.el-pagination button) {
  min-width: 24px;
}

:deep(.el-pagination .el-pager li) {
  min-width: 24px;
}
</style> 