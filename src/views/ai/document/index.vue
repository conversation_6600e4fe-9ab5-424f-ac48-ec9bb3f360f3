<template>
  <div class="app-container">
    <el-row :gutter="10" class="doc-container">
      <!-- 左侧文档列表 -->
      <el-col :span="isCollapse ? 0 : leftColSpan" class="left-panel" ref="leftPanelRef">
        <div class="resize-handle" @mousedown="startResize"></div>
        <document-list ref="documentListRef" @select-document="handleDocumentSelect" />
      </el-col>
      <!-- 收缩按钮 -->
      <div 
        class="collapse-btn"
        :class="{ 'is-collapse': isCollapse }"
        @click="toggleCollapse"
      >
        <el-icon><Fold /></el-icon>
      </div>
      <!-- 右侧文档内容 -->
      <el-col :span="isCollapse ? 24 : rightColSpan" class="right-panel">
        <markdown-viewer 
          :content="currentContent" 
          :current-doc="currentDoc"
          :doc-list="docList"
          @switch-doc="handleSwitchDoc"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="LFNNDocument">
import { ref, onMounted, onBeforeUnmount, nextTick, onActivated } from 'vue'
import { Fold } from '@element-plus/icons-vue'
import DocumentList from './components/DocumentList.vue'
import MarkdownViewer from './components/MarkdownViewer.vue'
import cache from '@/plugins/cache'
import { addAccessRecord } from '@/api/report/index'

// 是否收起左侧面板
const isCollapse = ref(false)

// 左右面板的宽度比例
const leftColSpan = ref(5)
const rightColSpan = ref(19)
const leftPanelRef = ref(null)

// 当前选中的文档内容
const currentContent = ref('')
// 当前文档信息
const currentDoc = ref(null)
// 文档列表
const docList = ref([])
// 文档列表组件引用
const documentListRef = ref(null)

// 拖拽调整宽度相关变量
let isResizing = false
let startX = 0
let startLeftWidth = 0
let totalWidth = 0

// 开始调整宽度
const startResize = (e) => {
  e.preventDefault()
  isResizing = true
  startX = e.clientX
  
  // 获取初始宽度
  const container = document.querySelector('.doc-container')
  totalWidth = container.offsetWidth
  startLeftWidth = leftPanelRef.value.$el.offsetWidth
  
  // 添加鼠标移动和松开事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', stopResize)
  
  // 添加调整时的样式
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
  
  // 设置文档列表组件的调整状态
  documentListRef.value?.setResizing(true)
}

// 处理鼠标移动
const handleMouseMove = (e) => {
  if (!isResizing) return
  
  const dx = e.clientX - startX
  const newLeftWidth = startLeftWidth + dx
  
  // 计算新的宽度比例，限制最小和最大宽度
  const minWidth = 100 // 最小宽度
  const maxWidth = totalWidth * 0.5 // 最大宽度为容器的50%
  
  if (newLeftWidth < minWidth || newLeftWidth > maxWidth) return
  
  // 计算新的栅格比例 (总共24栅格)
  const newLeftSpan = Math.round((newLeftWidth / totalWidth) * 24)
  
  // 确保最小宽度为3个格子
  const finalLeftSpan = Math.max(3, newLeftSpan)
  
  leftColSpan.value = finalLeftSpan
  rightColSpan.value = 24 - finalLeftSpan
}

// 停止调整宽度
const stopResize = () => {
  isResizing = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', stopResize)
  
  // 恢复正常样式
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
  
  // 保存当前宽度比例到本地存储
  cache.local.set('lfnn_document_left_span', leftColSpan.value.toString())
  
  // 重置文档列表组件的调整状态
  documentListRef.value?.setResizing(false)
}

// 切换收缩状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 处理文档选择
const handleDocumentSelect = async (data) => {
  if (!data || !data.currentDoc) return
  
  currentContent.value = data.content
  
  // 处理文档名，去掉后缀名
  const processedDoc = {
    ...data.currentDoc,
    displayFileName: getDisplayFileName(data.currentDoc.fileName),
    index: data.currentIndex,
    total: data.total
  }
  
  currentDoc.value = processedDoc
  
  // 处理文档列表，为每个文档添加不带后缀的显示名称
  docList.value = data.list.map(doc => ({
    ...doc,
    displayFileName: getDisplayFileName(doc.fileName)
  }))
  
  // 保存当前选中的文档ID到本地存储
  cache.local.set('lfnn_document_id', data.currentDoc.id.toString())
}

// 处理文件名显示，去掉后缀名
const getDisplayFileName = (fileName) => {
  if (!fileName) return ''
  // 查找最后一个点的位置
  const lastDotIndex = fileName.lastIndexOf('.')
  // 如果没有找到点或者点在开头，则返回原文件名
  if (lastDotIndex <= 0) return fileName
  // 返回不包含后缀的文件名
  return fileName.substring(0, lastDotIndex)
}

// 处理文档切换
const handleSwitchDoc = (doc) => {
  // 触发文档列表组件的点击事件
  const documentList = document.querySelector('.doc-menu')
  const menuItem = documentList?.querySelector(`[data-id="${doc.id}"]`)
  menuItem?.click()
}

// 组件挂载时，尝试恢复之前选中的文档和宽度比例
onMounted(async () => {
  // 记录页面访问
  recordPageAccess()
  
  // 恢复之前保存的宽度比例
  const savedLeftSpan = cache.local.get('lfnn_document_left_span')
  if (savedLeftSpan) {
    leftColSpan.value = parseInt(savedLeftSpan)
    rightColSpan.value = 24 - parseInt(savedLeftSpan)
  }
  
  // 从本地存储中获取上次选中的文档ID
  const lastDocId = cache.local.get('lfnn_document_id')
  
  // 确保文档列表组件已加载完成
  await nextTick()
  
  if (lastDocId && documentListRef.value) {
    // 尝试多次加载，确保文档列表已完全加载
    const tryLoadDocument = async (retries = 3) => {
      const success = await documentListRef.value.selectDocumentById(lastDocId)
      if (!success && retries > 0) {
        // 如果加载失败且还有重试次数，延迟后再次尝试
        setTimeout(() => tryLoadDocument(retries - 1), 300)
      } else if (!success) {
        // 所有重试都失败，尝试加载第一个文档
        documentListRef.value.selectFirstDocument()
      }
    }
    
    // 开始尝试加载文档
    setTimeout(() => tryLoadDocument(), 300)
  }
})

// 添加onActivated生命周期钩子
onActivated(() => {
  // 记录页面访问
  recordPageAccess()
})

// 记录页面访问
const recordPageAccess = async () => {
  try {
    await addAccessRecord({
      accessType: 'document',
      accessCount: 1
    }).catch(err => {
      console.warn('文档页面访问记录添加失败，可能是防重复提交导致:', err)
      // 忽略错误，不影响页面正常加载
    })
    console.log('文档页面访问记录添加成功')
  } catch (error) {
    console.warn('文档页面访问记录添加失败:', error)
    // 忽略错误，不影响页面正常加载
  }
}

// 组件卸载前移除可能残留的事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped>
.app-container {
  padding: 20px;
  height: calc(100vh - 50px);
  background-color: #f5f7fa;
}

.doc-container {
  height: 100%;
  position: relative;
}

.left-panel, .right-panel {
  height: 100%;
  transition: all 0.3s ease;
}

.left-panel {
  position: relative;
}

.resize-handle {
  position: absolute;
  top: 0;
  right: -5px;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  z-index: 10;
  background-color: transparent;
}

.resize-handle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.resize-handle:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.left-panel :deep(.el-card),
.right-panel :deep(.el-card) {
  height: 100%;
  transition: all 0.3s;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.left-panel :deep(.el-card:hover),
.right-panel :deep(.el-card:hover) {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

/* 收缩按钮样式 */
.collapse-btn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 50px;
  background-color: var(--el-color-primary);
  border-radius: 0 4px 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s ease;
  color: white;
}

.collapse-btn:hover {
  width: 20px;
  background-color: var(--el-color-primary-dark-2);
}

.collapse-btn.is-collapse {
  left: -16px;
}

.collapse-btn.is-collapse:hover {
  left: -20px;
}

.collapse-btn .el-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.collapse-btn.is-collapse .el-icon {
  transform: rotate(180deg);
}

/* 修复el-col的过渡效果 */
:deep(.el-col) {
  transition: width 0.3s ease;
}
</style> 