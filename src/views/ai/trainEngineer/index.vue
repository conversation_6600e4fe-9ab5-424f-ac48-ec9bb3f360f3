<template>
  <div class="app-container">
    <!-- 标题和描述 -->
    <div class="train-engineer-header">
      <h1 class="train-engineer-title"> 示例训练工程下载 </h1>
      <p class="train-engineer-description"> 以下训练工程均经过验证和测试，部分模型基于LFNN对应平台做了修改和优化，推荐采用提供的工程和说明进行对应模型的训练和开发。</p>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filter-container">
      <el-input
        v-model="queryParams.modelName"
        placeholder="搜索示例工程..."
        clearable
        class="search-input"
        prefix-icon="Search"
        @keyup.enter="handleQuery"
      />
      <el-input
        v-model="queryParams.chipType"
        placeholder="芯片类型"
        clearable
        class="chiptype-input"
        @keyup.enter="handleQuery"
        style="width: 180px;"
      />
      <el-select v-model="queryParams.modelType" clearable placeholder="所有分类" class="filter-select" @change="handleQuery">
        <el-option
          v-for="dict in categoryOptions"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        />
      </el-select>
    
      <el-button type="primary" class="filter-button" @click="handleQuery">筛选</el-button>
    </div>

    <!-- 训练工程列表 -->
    <div v-loading="loading" class="train-list">
      <!-- 训练工程列表项 -->
      <div v-for="item in trainList" :key="item.id" class="train-item">
        <div class="item-header">
          <div class="item-title">
            {{ item.modelName }}
            <span v-if="item.isNew" class="item-tag">最新</span>
          </div>
          <el-button 
            type="primary" 
            class="download-btn" 
            @click="handleDownload(item)" 
            :loading="item.downloading"
            :disabled="!checkDownloadPermission"
            :title="!checkDownloadPermission ? '无下载权限' : ''"
          >下载</el-button>
        </div>
        <div class="item-desc">{{ item.modelDesc }}</div>
        <div class="item-meta">
          <span class="meta-item">
            <i class="icon-time"></i> 更新时间: {{ item.createTime }}
          </span>
          <span class="meta-item">
            <i class="icon-download"></i> 下载次数: {{ item.downloadCount }}
          </span>
          <span class="meta-item">
            <i class="icon-category"></i> 分类: {{ item.modelType }}
          </span>
          <span class="meta-item">
            <el-icon><Cpu /></el-icon> 芯片类型: {{ item.chipType || '未指定' }}
          </span>
        </div>
      </div>

      <!-- 空数据提示 -->
      <div v-if="trainList.length === 0 && !loading" class="empty-data">
        暂无数据
      </div>
    </div>

    <!-- 分页组件 -->
    <pagination
      v-if="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      class="pagination-container"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onActivated } from 'vue'
import { ElNotification } from 'element-plus'
import { Search, Filter, Calendar, Download, Collection, Cpu } from '@element-plus/icons-vue'
import { listTrainEngineer, downloadTrainEngineer } from '@/api/ai/trainEngineer'
import Pagination from '@/components/Pagination/index.vue'
import useUserStore from '@/store/modules/user'
import { addAccessRecord } from '@/api/report/index'

// 训练工程列表数据
const trainList = ref([])
const total = ref(0)
const loading = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  modelName: '',
  modelType: '',
  chipType: ''
})

// 分类选项
const categoryOptions = ref([
  { value: '检测', label: '检测' },
  { value: '分割', label: '分割' },
  { value: '姿态', label: '姿态' },
  { value: 'OCR', label: 'OCR' },
  { value: '语音', label: '语音' },
  { value: 'LLM', label: 'LLM' },
  { value: 'VLM', label: 'VLM' }
])

// 检查下载权限
const checkDownloadPermission = computed(() => {
  const permissions = useUserStore().permissions
  const all_permission = "*:*:*"
  return permissions.some(permission => {
    return all_permission === permission || permission === 'ai:train:download'
  })
})

// 获取训练工程列表数据
function getList() {
  loading.value = true
  listTrainEngineer(queryParams).then(response => {
    // 添加downloading属性用于下载状态
    trainList.value = response.rows.map(item => ({
      ...item,
      downloading: false
    }))
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
    ElNotification.error({
      title: '错误',
      message: '获取训练工程列表失败'
    })
  })
}

// 查询按钮操作
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
function resetQuery() {
  queryParams.modelName = ''
  queryParams.modelType = ''
  queryParams.chipType = ''
  handleQuery()
}

// 下载训练工程文件
function handleDownload(item) {
  // 检查权限
  if (!checkDownloadPermission.value) {
    ElNotification.warning({
      title: '警告',
      message: '您没有下载权限'
    })
    return
  }
  
  // 设置下载状态
  item.downloading = true
  
  downloadTrainEngineer(item.fileIdentifier).then(response => {
    if (response.code === 200 && response.msg) {
      // 创建一个隐藏的a标签用于下载
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = response.msg;
      link.setAttribute('download', item.modelName + '_训练工程'); 
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElNotification.success({
        title: '成功',
        message: '下载成功'
      });
    } else {
      ElNotification.error({
        title: '错误',
        message: '获取下载地址失败'
      });
    }
  }).catch(error => {
    console.error('下载失败:', error);
    ElNotification.error({
      title: '错误',
      message: '下载失败，请稍后重试'
    });
  }).finally(() => {
    // 重置下载状态
    item.downloading = false;
  });
}

// 页面加载时获取数据
onMounted(() => {
  // 记录页面访问
  recordPageAccess()
  getList()
})

// 添加onActivated生命周期钩子
onActivated(() => {
  // 记录页面访问
  recordPageAccess()
})

// 记录页面访问
const recordPageAccess = async () => {
  try {
    await addAccessRecord({
      accessType: 'trainEngineer',
      accessCount: 1
    }).catch(err => {
      console.warn('示例训练工程页面访问记录添加失败，可能是防重复提交导致:', err)
      // 忽略错误，不影响页面正常加载
    })
    console.log('示例训练工程页面访问记录添加成功')
  } catch (error) {
    console.warn('示例训练工程页面访问记录添加失败:', error)
    // 忽略错误，不影响页面正常加载
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px 80px;
}

.train-engineer-header {
  margin-bottom: 20px;
}

.train-engineer-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.train-engineer-description {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.search-filter-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.search-input {
  flex: 1;
}

.filter-select {
  width: 180px;
}

.chiptype-input {
  width: 180px;
}

.filter-button {
  width: 80px;
}

/* 训练工程列表样式 */
.train-list {
  margin-bottom: 20px;
}

.train-item {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding: 20px;
  transition: all 0.3s;
}

.train-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-title {
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.item-tag {
  background-color: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
}

.download-btn {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  border-radius: 4px;
  padding: 8px 20px;
  font-size: 14px;
}

.download-btn:hover {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

.item-desc {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.6;
}

.item-meta {
  display: flex;
  color: #909399;
  font-size: 14px;
  flex-wrap: wrap;
}

.meta-item {
  margin-right: 20px;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.meta-item i {
  margin-right: 5px;
}

.icon-time:before {
  content: "📅";
}

.icon-download:before {
  content: "⬇️";
}

.icon-category:before {
  content: "🏷️";
}

.empty-data {
  padding: 40px 0;
  text-align: center;
  color: #909399;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 