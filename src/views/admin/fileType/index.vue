<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-form-item label="类型名称" prop="fileTypeName">
          <el-input
            v-model="queryParams.fileTypeName"
            placeholder="请输入类型名称"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8 toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['admin:fileType:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['admin:fileType:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" style="margin-left: auto;"></right-toolbar>
      </el-row>

      <!-- 文件类型表格 -->
      <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="类型ID" align="center" prop="id" />
        <el-table-column label="类型名称" align="left" prop="fileTypeName" :show-overflow-tooltip="true" />
        <el-table-column label="类型标识" align="center" prop="fileTypeIdentifier" />
        <el-table-column label="创建时间" align="center" prop="createTime"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['admin:fileType:edit']">
              编辑
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['admin:fileType:remove']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加/编辑文件类型对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="typeForm" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="类型名称" prop="fileTypeName">
          <el-input v-model="form.fileTypeName" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="类型标识" prop="fileTypeIdentifier">
          <el-input v-model="form.fileTypeIdentifier" placeholder="请输入类型标识" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FileType">
import { 
  listFileType, 
  getFileType, 
  delFileType, 
  addFileType, 
  updateFileType 
} from "@/api/admin/fileType";

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 文件类型表格数据
const typeList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileTypeName: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  fileTypeName: undefined,
  fileTypeIdentifier: undefined
});

// 表单校验
const rules = {
  fileTypeName: [
    { required: true, message: "类型名称不能为空", trigger: "blur" }
  ],
  fileTypeIdentifier: [
    { required: true, message: "类型标识不能为空", trigger: "blur" }
  ]
};

/** 查询文件类型列表 */
function getList() {
  loading.value = true;
  listFileType(queryParams.value).then(response => {
    typeList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    fileTypeName: undefined,
    fileTypeIdentifier: undefined
  };
  proxy.resetForm("typeForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  title.value = "添加文件类型";
  open.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id;
  getFileType(id).then(response => {
    form.value = response.data;
    title.value = "修改文件类型";
    open.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["typeForm"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateFileType(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      } else {
        addFileType({
          fileTypeName: form.value.fileTypeName,
          fileTypeIdentifier: form.value.fileTypeIdentifier
        }).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const typeIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除文件类型编号为"' + typeIds + '"的数据项？').then(() => {
    return delFileType(typeIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 10px;
  background-color: transparent;
}

.search-form {
  margin-bottom: 15px;
  background-color: #f8f9fb;
  padding: 15px 15px 5px;
  border-radius: 4px;
}

.mb8 {
  margin-bottom: 18px;
}

.toolbar-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  padding: 0;
}

:deep(.el-card) {
  border: none;
  box-shadow: none !important;
}

:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

:deep(.el-table th) {
  background-color: #f8f9fb !important;
  color: #606266;
  font-weight: 600;
  height: 45px;
}

.search-buttons {
  margin-left: auto;
}

.dialog-footer {
  text-align: center;
  padding-top: 10px;
}
</style> 