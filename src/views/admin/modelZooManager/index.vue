<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-form-item label="模型名称" prop="modelName">
          <el-input
            v-model="queryParams.modelName"
            placeholder="请输入模型名称"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="模型类型" prop="modelType">
          <el-select v-model="queryParams.modelType" placeholder="模型类型" clearable style="width: 240px">
            <el-option label="图像分类" value="图像分类" />
            <el-option label="目标检测" value="目标检测" />
            <el-option label="语义分割" value="语义分割" />
            <el-option label="自然语言处理" value="自然语言处理" />
          </el-select>
        </el-form-item>
        <el-form-item label="芯片类型" prop="chipType">
          <el-input
            v-model="queryParams.chipType"
            placeholder="请输入芯片类型"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="模型输入SIZE" prop="size">
          <el-input
            v-model="queryParams.size"
            placeholder="请输入模型输入SIZE"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8 toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['admin:modelZooManager:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['admin:modelZooManager:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" style="margin-left: auto;"></right-toolbar>
      </el-row>

      <!-- 模型列表表格 -->
      <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column label="模型ID" align="center" prop="id" width="80" />
        <el-table-column label="模型名称" align="center" prop="modelName" :show-overflow-tooltip="true" />
        <el-table-column label="模型类型" align="center" prop="modelType" />
        <el-table-column label="芯片类型" align="center" prop="chipType" />
        <el-table-column label="模型输入SIZE" align="center" prop="size" />
        <el-table-column label="前推理性能(ms)" align="center">
          <template #default="scope">
            {{ parseFloat(scope.row.beforeDedcutivePerformance).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="推理性能(ms)" align="center">
          <template #default="scope">
            {{ parseFloat(scope.row.deductivePerformance).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="后推理性能(ms)" align="center">
          <template #default="scope">
            {{ parseFloat(scope.row.afterDedcutivePerformance).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="总推理性能(ms)" align="center">
          <template #default="scope">
            {{ (parseFloat(scope.row.beforeDedcutivePerformance) + parseFloat(scope.row.deductivePerformance) + parseFloat(scope.row.afterDedcutivePerformance)).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="推理精度mAP" align="center">
          <template #default="scope">
            {{ parseFloat(scope.row.dedcutiveAccuracy).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column label="示例代码文件名" align="center" prop="codeFileName" :show-overflow-tooltip="true" />
        <el-table-column label="代码示例文件标识" align="center" prop="codeIdentififer" :show-overflow-tooltip="true" />
        <el-table-column label="创建者" align="center" prop="createBy" />
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['admin:modelZooManager:query']">
              查看
            </el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['admin:modelZooManager:edit']">
              编辑
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['admin:modelZooManager:remove']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加/编辑模型对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="modelForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="form.modelName" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="模型类型" prop="modelType">
          <el-select v-model="form.modelType" placeholder="请选择模型类型" style="width: 100%">
            <el-option label="图像分类" value="图像分类" />
            <el-option label="目标检测" value="目标检测" />
            <el-option label="语义分割" value="语义分割" />
            <el-option label="自然语言处理" value="自然语言处理" />
          </el-select>
        </el-form-item>
        <el-form-item label="芯片类型" prop="chipType">
          <el-input v-model="form.chipType" placeholder="请输入芯片类型" />
        </el-form-item>
        <el-form-item label="模型输入SIZE" prop="size">
          <el-input v-model="form.size" placeholder="请输入模型输入SIZE" />
        </el-form-item>
        <el-form-item label="前推理性能(ms)" prop="beforeDedcutivePerformance">
          <el-input-number v-model="form.beforeDedcutivePerformance" :min="0" :max="100" :precision="2" :step="0.01" placeholder="请输入前推理性能" style="width: 100%" />
        </el-form-item>
        <el-form-item label="推理性能(ms)" prop="deductivePerformance">
          <el-input-number v-model="form.deductivePerformance" :min="0" :precision="2" :step="0.01" placeholder="请输入推理性能" style="width: 100%" />
        </el-form-item>
        <el-form-item label="后推理性能(ms)" prop="afterDedcutivePerformance">
          <el-input-number v-model="form.afterDedcutivePerformance" :min="0" :precision="2" :step="0.01" placeholder="请输入后推理性能" style="width: 100%" />
        </el-form-item>
        <el-form-item label="推理精度mAP" prop="dedcutiveAccuracy">
          <el-input-number v-model="form.dedcutiveAccuracy" :min="0" :max="100" :precision="2" :step="0.01" placeholder="请输入推理精度mAP" style="width: 100%" />
        </el-form-item>
        <el-form-item label="推理示例代码" prop="codeIdentififer" class="required-field">
          <el-upload
            class="code-uploader"
            action="#"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            :on-exceed="() => ElNotification.warning({ title: '警告', message: '只能上传一个文件' })"
            :before-upload="beforeUpload"
            :on-change="handleFileChange"
            :on-remove="handleRemove"
            :file-list="uploadFile ? [uploadFile] : []"
          >
            <template #trigger>
              <el-button type="primary" :loading="uploadLoading">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                请上传推理示例代码文件，支持.py、.cpp等格式
              </div>
            </template>
          </el-upload>
          
          <!-- 上传进度详情展示 -->
          <div v-if="uploadProgress > 0" class="upload-progress mt20">
            <h3>上传进度</h3>
            <div class="progress-item">
              <div class="progress-info">
                <span class="file-name">{{ uploadFile?.name || '文件' }}</span>
                <span class="percentage">{{ Math.floor(uploadProgress) }}%</span>
              </div>
              <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
              <div class="progress-details" v-if="uploadProgress > 0 && uploadProgress < 100">
                <span>{{ uploadSpeed }} | {{ uploadRemainingTime }}</span>
              </div>
            </div>
          </div>
          
          <div v-if="fileIdentifier" class="file-identifier">
            文件标识：{{ fileIdentifier }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="uploadLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看模型详情对话框 -->
    <el-dialog title="模型详情" v-model="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="模型ID">{{ viewForm.id }}</el-descriptions-item>
        <el-descriptions-item label="模型名称">{{ viewForm.modelName }}</el-descriptions-item>
        <el-descriptions-item label="模型类型">{{ viewForm.modelType }}</el-descriptions-item>
        <el-descriptions-item label="芯片类型">{{ viewForm.chipType }}</el-descriptions-item>
        <el-descriptions-item label="模型输入SIZE">{{ viewForm.size }}</el-descriptions-item>
        <el-descriptions-item label="示例代码文件名">{{ viewForm.codeFileName }}</el-descriptions-item>
        <el-descriptions-item label="推理示例代码">{{ viewForm.codeIdentififer }}</el-descriptions-item>
        <el-descriptions-item label="前推理性能(ms)">{{ parseFloat(viewForm.beforeDedcutivePerformance).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="推理性能(ms)">{{ parseFloat(viewForm.deductivePerformance).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="后推理性能(ms)">{{ parseFloat(viewForm.afterDedcutivePerformance).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="总推理性能(ms)">{{ (parseFloat(viewForm.beforeDedcutivePerformance) + parseFloat(viewForm.deductivePerformance) + parseFloat(viewForm.afterDedcutivePerformance)).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="推理精度mAP">{{ parseFloat(viewForm.dedcutiveAccuracy).toFixed(2) }}%</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ viewForm.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新者">{{ viewForm.updateBy }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ModelZoo">
import { 
  listModelZoo, 
  getModelZoo, 
  delModelZoo, 
  addModelZoo, 
  updateModelZoo 
} from "@/api/admin/modelZooManager";
import { FileUploader } from "@/utils/upload";
import { ElNotification } from 'element-plus';

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 模型表格数据
const modelList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 是否显示查看详情弹出层
const viewOpen = ref(false);
// 文件上传加载状态
const uploadLoading = ref(false);
// 上传进度
const uploadProgress = ref(0);
// 上传状态
const uploadStatus = ref('');
// 上传速度
const uploadSpeed = ref('0 MB/s');
// 剩余时间
const uploadRemainingTime = ref('计算中...');
// 上传的文件
const uploadFile = ref(null);
// 上传的文件标识符
const fileIdentifier = ref('');

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  modelName: undefined,
  modelType: undefined,
  chipType: undefined,
  size: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  modelName: undefined,
  modelType: undefined,
  chipType: undefined,
  size: undefined,
  codeFileName: undefined,
  codeIdentififer: undefined,
  deductivePerformance: 0,
  dedcutiveAccuracy: 0,
  beforeDedcutivePerformance: 0,
  afterDedcutivePerformance: 0
});

// 查看详情表单
const viewForm = ref({
  id: undefined,
  modelName: undefined,
  modelType: undefined,
  chipType: undefined,
  size: undefined,
  codeFileName: undefined,
  codeIdentififer: undefined,
  deductivePerformance: 0,
  dedcutiveAccuracy: 0,
  beforeDedcutivePerformance: 0,
  afterDedcutivePerformance: 0,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined
});

// 表单校验
const rules = {
  modelName: [
    { required: true, message: "模型名称不能为空", trigger: "blur" }
  ],
  modelType: [
    { required: true, message: "模型类型不能为空", trigger: "change" }
  ],
  chipType: [
    { required: true, message: "芯片类型不能为空", trigger: "blur" }
  ],
  size: [
    { required: true, message: "模型输入SIZE不能为空", trigger: "blur" }
  ],
  deductivePerformance: [
    { required: true, message: "推理性能不能为空", trigger: "blur" }
  ],
  dedcutiveAccuracy: [
    { required: true, message: "推理精度不能为空", trigger: "blur" }
  ],
  beforeDedcutivePerformance: [
    { required: true, message: "前推理性能不能为空", trigger: "blur" }
  ],
  afterDedcutivePerformance: [
    { required: true, message: "后推理性能不能为空", trigger: "blur" }
  ]
};

/** 查询模型列表 */
function getList() {
  loading.value = true;
  listModelZoo(queryParams.value).then(response => {
    modelList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    modelName: undefined,
    modelType: undefined,
    chipType: undefined,
    size: undefined,
    codeFileName: undefined,
    codeIdentififer: undefined,
    deductivePerformance: 0,
    dedcutiveAccuracy: 0,
    beforeDedcutivePerformance: 0,
    afterDedcutivePerformance: 0
  };
  uploadFile.value = null;
  fileIdentifier.value = '';
  uploadProgress.value = 0;
  proxy.resetForm("modelForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  title.value = "添加模型";
  open.value = true;
}

/** 查看按钮操作 */
function handleView(row) {
  const modelId = row.id;
  getModelZoo(modelId).then(response => {
    viewForm.value = response.data;
    viewOpen.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const modelId = row.id;
  getModelZoo(modelId).then(response => {
    form.value = response.data;
    // 如果有代码示例文件标识，设置上传文件显示
    if (form.value.codeIdentififer) {
      const fileName = form.value.codeFileName || form.value.codeIdentififer.substring(form.value.codeIdentififer.lastIndexOf('/') + 1);
      uploadFile.value = { name: fileName, url: form.value.codeIdentififer };
      fileIdentifier.value = form.value.codeIdentififer;
    }
    title.value = "修改模型";
    open.value = true;
  });
}

/** 文件上传前的钩子 */
function beforeUpload(file) {
  // 只在这里设置文件，但不阻止上传，因为我们需要触发on-change事件
  return true;
}

/** 文件变化的钩子 */
function handleFileChange(file) {
  // 在文件变化时保存文件对象
  if (file && file.raw) {
    uploadFile.value = file;
    console.log('文件已选择:', uploadFile.value);
  }
  return false; // 阻止自动上传
}

/** 移除文件 */
function handleRemove() {
  uploadFile.value = null;
  fileIdentifier.value = '';
  uploadProgress.value = 0;
  uploadStatus.value = '';
  uploadSpeed.value = '0 MB/s';
  uploadRemainingTime.value = '计算中...';
  form.value.codeIdentififer = undefined;
  form.value.codeFileName = undefined;
}

/** 上传文件 */
async function uploadCodeFile() {
  console.log('准备上传文件:', uploadFile.value);
  if (!uploadFile.value) {
    ElNotification.warning({
      title: '警告',
      message: '请先选择文件'
    });
    return false;
  }
  
  try {
    uploadLoading.value = true;
    uploadStatus.value = 'primary';
    
    // 获取正确的文件对象
    const fileToUpload = uploadFile.value.raw || uploadFile.value;
    
    // 先计算文件的MD5作为identifier
    const tempUploader = new FileUploader(fileToUpload);
    const identifier = await tempUploader.calculateMD5();
    console.log('文件MD5计算完成:', identifier);
    
    // 创建FileUploader时指定identifier
    const uploader = new FileUploader(fileToUpload, identifier);
    
    // 上传文件，文件类型设为'modelZoo-demo-code'
    const uploadedIdentifier = await uploader.upload('modelZoo-demo-code', (progress) => {
      uploadProgress.value = progress.percent;
      uploadSpeed.value = progress.speed ? progress.speed + ' MB/s' : '计算中...';
      uploadRemainingTime.value = progress.remainingTime || '计算中...';
      
      // 更新状态
      if (progress.percent === 100) {
        uploadStatus.value = 'success';
      } else if (progress.percent < 100) {
        uploadStatus.value = 'primary';
      }
    });
    
    fileIdentifier.value = uploadedIdentifier;
    form.value.codeIdentififer = uploadedIdentifier;
    uploadStatus.value = 'success';
    ElNotification.success({
      title: '成功',
      message: '文件上传成功'
    });
    return true;
  } catch (error) {
    console.error('文件上传失败:', error);
    uploadStatus.value = 'exception';
    ElNotification.error({
      title: '错误',
      message: '文件上传失败: ' + error.message
    });
    return false;
  } finally {
    uploadLoading.value = false;
  }
}

/** 提交按钮 */
async function submitForm() {
  proxy.$refs["modelForm"].validate(async (valid) => {
    if (valid) {
      // 如果有新文件需要上传，先上传文件
      if (uploadFile.value && uploadFile.value.raw) {
        const uploadSuccess = await uploadCodeFile();
        if (!uploadSuccess) return;
        // 设置文件名
        form.value.codeFileName = uploadFile.value.name;
      }
      
      // 确保文件标识被正确赋值到表单
      if (fileIdentifier.value) {
        form.value.codeIdentififer = fileIdentifier.value;
      }
      
      if (form.value.id != undefined) {
        updateModelZoo(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      } else {
        addModelZoo(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const modelIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除模型编号为"' + modelIds + '"的数据项？').then(() => {
    return delModelZoo(modelIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

// 初始化数据
getList();
</script>

<style scoped>
/* app-container样式修改 */
:deep(.app-container) {
  padding: 10px;
  background-color: transparent;
}

:deep(.el-card) {
  border: none;
  box-shadow: none !important;
}

:deep(.el-card__header) {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 10px 0;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 15px;
  background-color: #f8f9fb;
  padding: 15px 15px 5px;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.mb8 {
  margin-bottom: 18px;
}

/* 搜索表单样式 */
.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 15px;
}

.search-form :deep(.el-form-item__label) {
  color: #606266;
}

.search-buttons {
  margin-left: auto;
}

/* 工具栏样式 */
.toolbar-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  padding: 0;
}

.toolbar-row .el-col {
  margin-bottom: 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  table-layout: fixed;
  margin-top: 8px;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table--border) th, :deep(.el-table--border) td {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table th) {
  background-color: #f8f9fb !important;
  color: #606266;
  font-weight: 600;
  height: 45px;
  text-align: center;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

:deep(.el-table__row:hover td) {
  background-color: #f2f6fc !important;
}

:deep(.el-table__header-wrapper) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__body-wrapper) {
  overflow: auto;
}

:deep(.el-table .cell) {
  line-height: 1.5;
}

/* 操作列按钮样式 */
:deep(.el-button--link) {
  margin: 0 4px;
  font-size: 13px;
  height: 24px;
  line-height: 24px;
}

/* 状态标签样式优化 */
:deep(.el-tag) {
  border-radius: 12px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  border: none;
}

:deep(.el-tag--success) {
  background-color: #f0f9eb;
  color: #67c23a;
}

:deep(.el-tag--warning) {
  background-color: #fdf6ec;
  color: #e6a23c;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  color: #f56c6c;
}

:deep(.el-tag--info) {
  background-color: #f4f4f5;
  color: #909399;
}

/* 卡片样式优化 */
:deep(.el-card) {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8vh !important;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  background-color: #f8f9fb;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 4px;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 15px;
  justify-content: flex-end;
}

.no-wrap-label {
  white-space: nowrap;
}

.code-uploader {
  width: 100%;
  margin-bottom: 10px;
}

.file-identifier {
  margin-top: 10px;
  font-size: 13px;
  color: #606266;
  word-break: break-all;
}

/* 为推理示例代码添加必填标识 */
.required-field :deep(.el-form-item__label)::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 上传进度相关样式 */
.progress-item {
  margin-bottom: 8px;
  background-color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  width: 100%;
}

.progress-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
  width: 100%;
}

.progress-info .file-name {
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  color: #303133;
}

.progress-info .percentage {
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

.upload-progress {
  background-color: #f8f9fb;
  padding: 12px 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  margin: 8px 0;
}

.upload-progress h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #303133;
  font-size: 15px;
  font-weight: 600;
}

.progress-details {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  padding: 0 2px;
}

:deep(.el-progress) {
  margin-top: 2px;
}

:deep(.el-progress-bar) {
  width: 100%;
}

:deep(.el-progress-bar__outer) {
  height: 8px !important;
  border-radius: 4px;
  background-color: #f0f2f5;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
  transition: all 0.3s ease;
}

:deep(.el-progress__text) {
  font-size: 13px !important;
  color: #606266;
}

.mt20 {
  margin-top: 12px;
}
</style> 