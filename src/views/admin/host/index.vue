<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-form-item label="主机名称" prop="hostName">
          <el-input
            v-model="queryParams.hostName"
            placeholder="请输入主机名称"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="主机IP" prop="hostIp">
          <el-input
            v-model="queryParams.hostIp"
            placeholder="请输入主机IP"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="主机状态" clearable style="width: 240px">
            <el-option label="空闲" value="0" />
            <el-option label="执行中" value="1" />
            <el-option label="离线" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8 toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['admin:aiHost:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['admin:aiHost:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" style="margin-left: auto;"></right-toolbar>
      </el-row>

      <!-- 主机列表表格 -->
      <el-table v-loading="loading" :data="hostList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column label="主机ID" align="center" prop="hostId" width="80" />
        <el-table-column label="主机名称" align="left" prop="hostName" :show-overflow-tooltip="true" width="180"/>
        <el-table-column label="主机IP" align="center" prop="hostIp" width="140" />
        <el-table-column label="SSH端口" align="center" prop="sshPort" width="90" />
        <el-table-column label="最大队列" align="center" prop="maxQueueSize" width="90" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" effect="plain">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="可执行路径" align="left" prop="executablePath" min-width="200" :show-overflow-tooltip="true" />
        <el-table-column label="备注" align="left" prop="remark" min-width="150" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Upload" @click="handleUpload(scope.row)" v-hasPermi="['admin:aiHost:uploadExecutable']">
              上传可执行文件
            </el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['admin:aiHost:edit']">
              编辑
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['admin:aiHost:remove']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加/编辑主机对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="hostForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="主机名称" prop="hostName">
          <el-input v-model="form.hostName" placeholder="请输入主机名称" />
        </el-form-item>
        <el-form-item label="主机IP" prop="hostIp">
          <el-input v-model="form.hostIp" placeholder="请输入主机IP" />
        </el-form-item>
        <el-form-item label="SSH端口" prop="sshPort">
          <el-input v-model="form.sshPort" placeholder="请输入SSH端口" />
        </el-form-item>
        <el-form-item label="SSH用户名" prop="sshUsername">
          <el-input v-model="form.sshUsername" placeholder="请输入SSH用户名" />
        </el-form-item>
        <el-form-item label="SSH密码" prop="sshPassword">
          <el-input v-model="form.sshPassword" type="password" placeholder="请输入SSH密码" show-password />
        </el-form-item>
        <el-form-item label="可执行路径" prop="executablePath">
          <el-input v-model="form.executablePath" placeholder="请输入可执行文件路径" />
        </el-form-item>
        <el-form-item label="最大队列" prop="maxQueueSize">
          <el-input-number v-model="form.maxQueueSize" :min="1" :max="100" placeholder="请输入最大同时执行任务数" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择主机状态">
            <el-option label="空闲" value="0" />
            <el-option label="执行中" value="1" />
            <el-option label="离线" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AiHost">
import { 
  listHost, 
  getHost, 
  delHost, 
  addHost, 
  updateHost,
  uploadExecutable 
} from "@/api/admin/aiHost";

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 主机表格数据
const hostList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  hostName: undefined,
  hostIp: undefined,
  status: undefined
});

// 表单参数
const form = ref({
  hostId: undefined,
  hostName: undefined,
  hostIp: undefined,
  sshPort: undefined,
  sshUsername: undefined,
  sshPassword: undefined,
  executablePath: undefined,
  status: "0",
  maxQueueSize: 1,
  remark: undefined
});

// 表单校验
const rules = {
  hostName: [
    { required: true, message: "主机名称不能为空", trigger: "blur" }
  ],
  hostIp: [
    { required: true, message: "主机IP不能为空", trigger: "blur" },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: "请输入正确的IP地址", trigger: "blur" }
  ],
  sshPort: [
    { required: true, message: "SSH端口不能为空", trigger: "blur" },
    { pattern: /^\d+$/, message: "端口必须为数字", trigger: "blur" },
    { pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, message: "端口范围为1-65535", trigger: "blur" }
  ],
  sshUsername: [
    { required: true, message: "SSH用户名不能为空", trigger: "blur" }
  ],
  sshPassword: [
    { required: true, message: "SSH密码不能为空", trigger: "blur" }
  ],
  executablePath: [
    { required: true, message: "可执行文件路径不能为空", trigger: "blur" }
  ],
  maxQueueSize: [
    { required: true, message: "最大队列不能为空", trigger: "blur" }
  ]
};

/** 获取状态类型 */
function getStatusType(status) {
  if (status === "0") return "success";
  if (status === "1") return "warning";
  if (status === "2") return "danger";
  return "info";
}

/** 获取状态文本 */
function getStatusText(status) {
  if (status === "0") return "空闲";
  if (status === "1") return "执行中";
  if (status === "2") return "离线";
  return "未知";
}

/** 查询主机列表 */
function getList() {
  loading.value = true;
  listHost(queryParams.value).then(response => {
    hostList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    hostId: undefined,
    hostName: undefined,
    hostIp: undefined,
    sshPort: undefined,
    sshUsername: undefined,
    sshPassword: undefined,
    executablePath: undefined,
    status: "0",
    maxQueueSize: 1,
    remark: undefined
  };
  proxy.resetForm("hostForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.hostId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  title.value = "添加主机";
  open.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const hostId = row.hostId;
  getHost(hostId).then(response => {
    form.value = response.data;
    title.value = "修改主机";
    open.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["hostForm"].validate(valid => {
    if (valid) {
      if (form.value.hostId != undefined) {
        updateHost(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      } else {
        addHost(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const hostIds = row.hostId || ids.value;
  proxy.$modal.confirm('是否确认删除主机编号为"' + hostIds + '"的数据项？').then(() => {
    return delHost(hostIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 上传可执行文件操作 */
function handleUpload(row) {
  proxy.$modal.confirm('是否确认上传可执行文件到主机"' + row.hostName + '"？').then(() => {
    return uploadExecutable(row);
  }).then(() => {
    proxy.$modal.msgSuccess("上传成功");
    getList();
  }).catch(() => {});
}

// 初始化数据
getList();
</script>

<style scoped>
/* app-container样式修改 */
:deep(.app-container) {
  padding: 10px;
  background-color: transparent;
}

:deep(.el-card) {
  border: none;
  box-shadow: none !important;
}

:deep(.el-card__header) {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 10px 0;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 15px;
  background-color: #f8f9fb;
  padding: 15px 15px 5px;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.mb8 {
  margin-bottom: 18px;
}

/* 搜索表单样式 */
.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 15px;
}

.search-form :deep(.el-form-item__label) {
  color: #606266;
}

.search-buttons {
  margin-left: auto;
}

/* 工具栏样式 */
.toolbar-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  padding: 0;
}

.toolbar-row .el-col {
  margin-bottom: 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  table-layout: fixed;
  margin-top: 8px;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table--border) th, :deep(.el-table--border) td {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table th) {
  background-color: #f8f9fb !important;
  color: #606266;
  font-weight: 600;
  height: 45px;
  text-align: center;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

:deep(.el-table__row:hover td) {
  background-color: #f2f6fc !important;
}

:deep(.el-table__header-wrapper) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__body-wrapper) {
  overflow: auto;
}

:deep(.el-table .cell) {
  line-height: 1.5;
}

/* 操作列按钮样式 */
:deep(.el-button--link) {
  margin: 0 4px;
  font-size: 13px;
  height: 24px;
  line-height: 24px;
}

/* 状态标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  transition: none;
  transform: none;
  backface-visibility: visible;
}

:deep(.el-table .el-tag) {
  transition: none !important;
  transform: none !important;
  animation: none !important;
  backface-visibility: visible !important;
}

:deep(.el-tag--success) {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

:deep(.el-tag--warning) {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #faecd8;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fde2e2;
}

:deep(.el-tag--info) {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #e9e9eb;
}

/* 卡片样式优化 */
:deep(.el-card) {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8vh !important;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  background-color: #f8f9fb;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 4px;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 15px;
  justify-content: flex-end;
}
</style> 