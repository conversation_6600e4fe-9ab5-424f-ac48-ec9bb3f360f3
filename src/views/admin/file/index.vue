<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-form-item label="文件名称" prop="fileName">
          <el-input
            v-model="queryParams.fileName"
            placeholder="请输入文件名称"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="文件类型" prop="fileType">
          <el-select v-model="queryParams.fileType" placeholder="文件类型" clearable filterable style="width: 180px">
            <el-option
              v-for="item in fileTypeList"
              :key="item.id"
              :label="item.fileTypeName"
              :value="item.fileTypeIdentifier"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="批次号" prop="batchNo">
          <el-input
            v-model="queryParams.batchNo"
            placeholder="请输入批次号"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="上传用户" prop="createBy">
          <el-input
            v-model="queryParams.createBy"
            placeholder="请输入用户名"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8 toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['admin:file:add']"
          >上传文件</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['admin:file:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" style="margin-left: auto;"></right-toolbar>
      </el-row>

      <!-- 文件表格 -->
      <el-table v-loading="loading" :data="fileList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="任务ID" align="center" prop="id" min-width="40" />
        <el-table-column label="文件名称" align="left" prop="fileName" min-width="150" :show-overflow-tooltip="true">
          <template #default="scope">
            <div class="file-name-cell">
              <el-icon class="file-icon" :class="getFileIconClass(scope.row.fileType)">
                <component :is="getFileTypeIcon(scope.row.fileType)"></component>
              </el-icon>
              <span>{{ scope.row.fileName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="文件标识" align="center" prop="fileIdentifier" min-width="180" :show-overflow-tooltip="true" />
        <el-table-column label="文件类型" align="center" prop="fileType" min-width="120">
          <template #default="scope">
            <el-tag
              :class="'tag-' + scope.row.fileType"
              :type="getTagType(scope.row.fileType)"
              effect="plain"
            >
              {{ getFileTypeText(scope.row.fileType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="批次号" align="center" prop="batchNo" min-width="100" :show-overflow-tooltip="true" />
        <el-table-column label="文件大小" align="center" prop="totalSize" min-width="100">
          <template #default="scope">
            {{ formatFileSize(scope.row.totalSize) }}
          </template>
        </el-table-column>
        <el-table-column label="上传用户" align="center" prop="createBy" min-width="100" :show-overflow-tooltip="true" />
        <el-table-column label="创建时间" align="center" prop="createTime" min-width="120" />
        <el-table-column label="操作" align="center" min-width="150" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['admin:file:query']">
              详情
            </el-button>
            <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)" v-hasPermi="['admin:file:query']">
              下载
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['admin:file:remove']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 文件详情对话框 -->
    <el-dialog title="文件详情" v-model="viewOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="文件名称">{{ viewData.fileName }}</el-descriptions-item>
        <el-descriptions-item label="文件类型">{{ getFileTypeText(viewData.fileType) }}</el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(viewData.totalSize) }}</el-descriptions-item>
        <el-descriptions-item label="批次号">{{ viewData.batchNo }}</el-descriptions-item>
        <el-descriptions-item label="上传用户">{{ viewData.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="文件标识" :span="2">{{ viewData.fileIdentifier }}</el-descriptions-item>
        <el-descriptions-item label="存储桶" :span="2">{{ viewData.bucketName }}</el-descriptions-item>
        <el-descriptions-item label="对象路径" :span="2">{{ viewData.objectKey }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewData.remark }}</el-descriptions-item>
      </el-descriptions>
      <div class="file-progress mt20" v-if="viewData.totalChunks > 0">
        <h4>上传进度: {{ Math.floor((viewData.uploadedChunks / viewData.totalChunks) * 100) }}%</h4>
        <el-progress :percentage="Math.floor((viewData.uploadedChunks / viewData.totalChunks) * 100)" :stroke-width="15" />
        <p class="mt10">已上传分片: {{ viewData.uploadedChunks }} / {{ viewData.totalChunks }}</p>
      </div>
    </el-dialog>

    <!-- 文件上传对话框 -->
    <el-dialog title="文件上传" v-model="uploadOpen" width="800px" append-to-body>
      <file-uploader 
        ref="fileUploaderRef" 
        @upload-success="handleUploadSuccess" 
        @upload-error="handleUploadError"
        @upload-complete="handleUploadComplete"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelUpload">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AdminFile">
import { 
  listFile, 
  getFileInfo, 
  addUploadTask,  
  delFile, 
  batchDelFile,
  getDownloadUrl
} from "@/api/admin/file";
import { listFileType } from "@/api/admin/fileType";
import FileUploader from './components/FileUploader.vue';
import { ElLoading } from 'element-plus';

// 组件挂载点
const { proxy } = getCurrentInstance();

// 文件类型选项
const fileTypeOptions = [
  { value: 'model', label: '模型' },
  { value: 'config', label: '配置' },
  { value: 'image', label: '图片' }
];

// 文件类型列表
const fileTypeList = ref([]);

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 文件表格数据
const fileList = ref([]);
// 是否显示详情弹窗
const viewOpen = ref(false);
// 详情数据
const viewData = ref({});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fileName: undefined,
  fileType: undefined,
  batchNo: undefined,
  createBy: undefined
});

// 文件上传相关
const uploadOpen = ref(false);
const fileUploaderRef = ref(null);

/** 获取文件类型描述 */
function getFileTypeText(typeCode) {
  const fileType = fileTypeList.value.find(item => item.fileTypeIdentifier === typeCode);
  return fileType ? fileType.fileTypeName : typeCode;
}

/** 获取文件类型图标样式 */
function getFileIconClass(typeCode) {
  return typeCode || 'other';
}

/** 获取文件类型图标 */
function getFileTypeIcon(typeCode) {
  const fileType = fileTypeList.value.find(item => item.fileTypeIdentifier === typeCode);
  const iconMap = {
    'model': 'Cpu',
    'config': 'Files',
    'image': 'Picture',
    'other': 'Document'
  };
  return fileType?.icon || iconMap[typeCode] || 'Document';
}

/** 格式化文件大小 */
function formatFileSize(size) {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let index = 0;
  let fileSize = size;
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  return `${fileSize.toFixed(2)} ${units[index]}`;
}

/** 查询文件列表 */
async function getList() {
  loading.value = true;
  // listFile(queryParams.value).then(response => {
  //   fileList.value = response.rows;
  //   total.value = response.total;
  //   loading.value = false;
  // });
  const data = await listFile(queryParams.value);
  fileList.value = data.rows;
  total.value = data.total;
  loading.value = false;
  console.log(fileList.value);

}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  uploadOpen.value = true;
}

/** 查看文件详情 */
function handleView(row) {
  viewData.value = {};
  getFileInfo(row.id).then(response => {
    console.log('文件详情数据:', response.data);
    viewData.value = response.data;
    viewOpen.value = true;
  }).catch(error => {
    console.error('获取文件详情失败:', error);
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const fileIds = row.id || ids.value;
  let confirmMessage = '是否确认删除文件编号为"' + fileIds + '"的数据项?';
  
  // 如果是批量删除且选择的文件较多，添加等待提示
  if (Array.isArray(fileIds) && fileIds.length > 10) {
    confirmMessage = `您选择了${fileIds.length}个文件进行删除，批量删除可能需要较长时间，请耐心等待。确认继续吗?`;
  }
  
  proxy.$modal.confirm(confirmMessage).then(() => {
    // 如果是批量删除且数量大于10，显示全屏加载提示
    let loadingInstance;
    if (Array.isArray(fileIds) && fileIds.length > 10) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: `正在删除${fileIds.length}个文件，请耐心等待...`,
        background: 'rgba(0, 0, 0, 0.7)',
      });
    }
    
    loading.value = true; // 显示表格加载中状态
    const deletePromise = Array.isArray(fileIds) ? batchDelFile(fileIds) : delFile(fileIds);
    
    return deletePromise.finally(() => {
      if (loadingInstance) {
        loadingInstance.close(); // 关闭全屏加载
      }
    });
  }).then(() => {
    loading.value = false; // 隐藏加载中状态
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch((error) => {
    loading.value = false; // 确保出错时也隐藏加载中状态
    console.error('删除操作出错:', error);
  });
}

/** 取消上传 */
function cancelUpload() {
  uploadOpen.value = false;
  getList(); // 刷新列表
}

/** 上传成功处理 */
function handleUploadSuccess(file) {
  // 上传成功后刷新列表
  getList();
}

/** 上传失败处理 */
function handleUploadError(file, error) {
  console.error('上传失败:', file.name, error);
}

/** 上传完成处理 */
function handleUploadComplete(allSuccess) {
  if (allSuccess) {
    uploadOpen.value = false;  // 关闭模态框
    getList();  // 刷新列表
  }
}

/** 获取文件类型列表 */
async function getFileTypeList() {
  try {
    const response = await listFileType({
      pageNum: 1,
      pageSize: 999
    });
    fileTypeList.value = response.rows || [];
  } catch (error) {
    console.error('获取文件类型列表失败:', error);
  }
}

/** 获取标签类型 */
function getTagType(typeCode) {
  // 根据文件类型标识返回不同的标签类型
  const typeMap = {
    'model': 'primary',           // 蓝色 - 模型文件
    'config': 'warning',          // 黄色 - 配置文件
    'image': 'success',           // 绿色 - 图片
    'other': 'info',              // 灰色 - 其它
    'upload-model': 'cyan',       // 青色 - 文件管理-模型
    'model-original-file': 'orange', // 橙色 - 模型转换-示例原始文件
    'model-config-file': 'purple',  // 紫色 - 模型转换-示例配置文件
    'model-transfer-tool': 'danger', // 红色 - 模型转换工具
    'transfer-model': 'primary',     // 蓝色 - 模型转换-转换后模型
    'LFNN-document': 'orange',       // 橙色 - LFNN使用文档
    'transfer-model-log': 'teal',    // 青绿色 - 模型转换-转换日志
    'modelZoo-demo-code': 'brown',    // 棕色 - ModelZoo-示例代码
    'lfnn-version': 'indigo'        // 靛蓝色 - LFNN版本下载
  };
  
  return typeMap[typeCode] || 'info';
}

/** 下载文件 */
function handleDownload(row) {
  const loading = proxy.$loading({
    lock: true,
    text: '获取下载地址中...',
    background: 'rgba(0, 0, 0, 0.7)',
  });
  
  getDownloadUrl(row.fileIdentifier, 3600, true, row.fileType).then(response => {
    if (response.code === 200 && response.data) {
      // 创建一个隐藏的a标签用于下载
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = response.data;
      link.setAttribute('download', row.fileName); // 设置下载文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      proxy.$modal.msgError('获取下载地址失败');
    }
  }).catch(error => {
    console.error('下载失败:', error);
    proxy.$modal.msgError('下载失败，请稍后重试');
  }).finally(() => {
    loading.close();
  });
}

// 页面加载时获取数据
onMounted(() => {
  getList();
  getFileTypeList();
});
</script>

<style scoped>
/* app-container样式修改 */
:deep(.app-container) {
  padding: 10px;
  background-color: transparent;
}

:deep(.el-card) {
  border: none;
  box-shadow: none !important;
}

:deep(.el-card__header) {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 10px 0;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 15px;
  background-color: #f8f9fb;
  padding: 15px 15px 5px;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.file-progress {
  padding: 15px;
  background-color: #f8f9fb;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.right-menu {
  display: flex;
  gap: 10px;
}

.mb8 {
  margin-bottom: 18px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

/* 搜索表单样式 */
.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 15px;
}

.search-form :deep(.el-form-item__label) {
  color: #606266;
}

.search-buttons {
  margin-left: auto;
}

/* 工具栏样式 */
.toolbar-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  padding: 0;
}

.toolbar-row .el-col {
  margin-bottom: 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  table-layout: fixed;
  margin-top: 8px;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table--border) th, :deep(.el-table--border) td {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table th) {
  background-color: #f8f9fb !important;
  color: #606266;
  font-weight: 600;
  height: 45px;
  text-align: center;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

:deep(.el-table__row:hover td) {
  background-color: #f2f6fc !important;
}

:deep(.el-table__header-wrapper) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__body-wrapper) {
  overflow: auto;
}

:deep(.el-table .cell) {
  line-height: 1.5;
}

/* 操作列按钮样式 */
:deep(.el-button--link) {
  margin: 0 4px;
  font-size: 13px;
  height: 24px;
  line-height: 24px;
}

/* 状态标签居中 */
:deep(.el-table .el-tag) {
  display: inline-block;
}

/* 文件名称单元格样式 */
.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-left: 5px;
}

.file-icon {
  font-size: 18px;
  color: #909399;
  flex-shrink: 0;
}

.file-icon.image {
  color: #67c23a;
}

.file-icon.model {
  color: #409eff;
}

.file-icon.config {
  color: #e6a23c;
}

.file-icon.other {
  color: #909399;
}

/* 卡片样式优化 */
:deep(.el-card) {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8vh !important;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  background-color: #f8f9fb;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 4px;
}

/* 详情页样式 */
:deep(.el-descriptions) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

:deep(.el-descriptions__title) {
  font-weight: 600;
  margin-bottom: 15px;
}

:deep(.el-descriptions__label) {
  background-color: #f8f9fb;
  font-weight: 500;
}

/* 状态标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  transition: none;
  transform: none;
  backface-visibility: visible;
  font-weight: 500;
}

:deep(.el-table .el-tag) {
  transition: none !important;
  transform: none !important;
  animation: none !important;
  backface-visibility: visible !important;
}

/* 基础标签颜色 - 更加鲜明 */
:deep(.el-tag--primary) {
  background-color: #e6f1ff;
  color: #1a73e8;
  border: 1px solid #b3d4ff;
}

:deep(.el-tag--success) {
  background-color: #e7f8e9;
  color: #2ea043;
  border: 1px solid #c0e7c6;
}

:deep(.el-tag--warning) {
  background-color: #fff6e6;
  color: #e67700;
  border: 1px solid #ffe0b2;
}

:deep(.el-tag--danger) {
  background-color: #ffebe9;
  color: #d73a49;
  border: 1px solid #ffcecb;
}

:deep(.el-tag--info) {
  background-color: #f1f2f4;
  color: #606266;
  border: 1px solid #dcdfe6;
}

/* 添加更多颜色变体 - 更加鲜明 */
:deep(.el-tag--purple) {
  background-color: #f5eeff;
  color: #6f42c1;
  border: 1px solid #e2d2ff;
}

:deep(.el-tag--cyan) {
  background-color: #e0f7fa;
  color: #00838f;
  border: 1px solid #b2ebf2;
}

:deep(.el-tag--orange) {
  background-color: #fff0e5;
  color: #e8590c;
  border: 1px solid #ffd0b2;
}

:deep(.el-tag--teal) {
  background-color: #e0f2f1;
  color: #00796b;
  border: 1px solid #b2dfdb;
}

:deep(.el-tag--brown) {
  background-color: #f0e6dd;
  color: #8b4513;
  border: 1px solid #e0ccb8;
}

:deep(.el-tag--magenta) {
  background-color: #fce4ec;
  color: #c2185b;
  border: 1px solid #f8bbd0;
}

:deep(.el-tag--lime) {
  background-color: #f1f8e9;
  color: #689f38;
  border: 1px solid #dcedc8;
}

:deep(.el-tag--indigo) {
  background-color: #e8eaf6;
  color: #3949ab;
  border: 1px solid #c5cae9;
}

:deep(.el-tag--pink) {
  background-color: #fce4ec;
  color: #d81b60;
  border: 1px solid #f8bbd0;
}

:deep(.el-tag--blue) {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

/* 为每种文件类型添加特定样式 */
:deep(.tag-model) {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

:deep(.tag-config) {
  background-color: #fff6e6;
  color: #e67700;
  border: 1px solid #ffe0b2;
}

:deep(.tag-image) {
  background-color: #e7f8e9;
  color: #2ea043;
  border: 1px solid #c0e7c6;
}

:deep(.tag-other) {
  background-color: #f1f2f4;
  color: #606266;
  border: 1px solid #dcdfe6;
}

:deep(.tag-upload-model) {
  background-color: #e0f7fa;
  color: #00838f;
  border: 1px solid #b2ebf2;
}

:deep(.tag-model-original-file) {
  background-color: #fff0e5;
  color: #e8590c;
  border: 1px solid #ffd0b2;
}

:deep(.tag-model-config-file) {
  background-color: #f5eeff;
  color: #6f42c1;
  border: 1px solid #e2d2ff;
}

:deep(.tag-model-transfer-tool) {
  background-color: #ffebe9;
  color: #d73a49;
  border: 1px solid #ffcecb;
}

:deep(.tag-transfer-model) {
  background-color: #e6f1ff;
  color: #1a73e8;
  border: 1px solid #b3d4ff;
}

:deep(.tag-LFNN-document) {
  background-color: #fce4ec;
  color: #c2185b;
  border: 1px solid #f8bbd0;
}

:deep(.tag-transfer-model-log) {
  background-color: #e0f2f1;
  color: #00796b;
  border: 1px solid #b2dfdb;
}

:deep(.tag-modelZoo-demo-code) {
  background-color: #f0e6dd;
  color: #8b4513;
  border: 1px solid #e0ccb8;
}

:deep(.tag-lfnn-version) {
  background-color: #e8eaf6;
  color: #3949ab;
  border: 1px solid #c5cae9;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 15px;
  justify-content: flex-end;
}
</style>