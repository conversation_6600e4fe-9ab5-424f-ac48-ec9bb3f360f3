<template>
  <div class="file-uploader">
    <!-- 文件类型选择 -->
    <div class="file-type-select mb20">
      <el-select
        v-model="selectedFileType"
        placeholder="请选择文件类型"
        class="file-type-select__input"
      >
        <el-option
          v-for="item in fileTypeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <el-upload
      ref="uploadRef"
      :action="upload.url"
      :auto-upload="false"
      :file-list="upload.fileList"
      :on-change="handleFileChange"
      :http-request="customUpload"
      drag
      multiple
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip">
          支持上传任意类型文件，无大小限制，文件大于2G时速度较慢，请耐心等待
        </div>
      </template>
    </el-upload>

    <div class="upload-control mt20">
      <el-button type="primary" @click="submitUpload" :disabled="!upload.fileList.length">上传文件</el-button>
      <el-button @click="clearFiles">清空文件</el-button>
    </div>

    <!-- 上传进度 -->
    <div v-if="uploadTasks.length > 0" class="upload-progress mt20">
      <h3>上传进度</h3>
      <div v-for="(task, index) in uploadTasks" :key="index" class="progress-item">
        <div class="progress-info">
          <span class="file-name">{{ task.fileName }}</span>
          <span class="percentage">{{ task.percentage }}%</span>
        </div>
        <el-progress :percentage="task.percentage" :status="task.status"></el-progress>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { FileUploader } from '@/utils/upload';
import { ElNotification } from 'element-plus';

const props = defineProps({
  // 文件类型限制
  fileTypes: {
    type: Array,
    default: () => ['model', 'config', 'image']
  },
  // 最大文件大小(MB)
  maxSize: {
    type: Number,
    default: Infinity
  }
});

const emit = defineEmits(['upload-success', 'upload-error', 'upload-complete']);
const { proxy } = getCurrentInstance();

// 上传相关数据
const upload = reactive({
  fileList: []
});

// 上传任务列表
const uploadTasks = ref([]);
const uploadRef = ref(null);

// 文件类型列表 - 直接写死
const fileTypeList = ref([
  { value: 'LFNN-document', label: 'LFNN使用文档' },
  { value: 'model-config-file', label: '模型转换-示例配置文件' },
  { value: 'model-original-file', label: '模型转换-示例原始文件' },
  { value: 'model-transfer-tool', label: '模型转换工具' }
]);

// 选中的文件类型，默认选中第一个
const selectedFileType = ref('LFNN-document');

// 文件变更
function handleFileChange(file, fileList) {
  upload.fileList = fileList;
}

// 自定义上传方法
async function customUpload({ file }) {
  try {
    // 检查是否选择了文件类型
    if (!selectedFileType.value) {
      ElNotification.error({
        title: '错误',
        message: '请选择文件类型！'
      });
      return;
    }

    // 文件大小不做限制

    // 创建上传器实例
    const uploader = new FileUploader(file);
    
    // 添加到上传任务列表
    uploadTasks.value.push({
      fileName: file.name,
      percentage: 0,
      status: 'primary',
      speed: '0 MB/s',
      remainingTime: '计算中...'
    });
    
    const taskIndex = uploadTasks.value.length - 1;
    
    // 开始上传，使用选择的文件类型
    const identifier = await uploader.upload(selectedFileType.value, (progress) => {
      uploadTasks.value[taskIndex] = {
        ...uploadTasks.value[taskIndex],
        percentage: progress.percent,
        speed: progress.speed + ' MB/s',
        remainingTime: progress.remainingTime,
        status: progress.percent === 100 ? 'success' : 'primary'
      };
    });

    // 上传成功
    if (identifier) {
      uploadTasks.value[taskIndex].status = 'success';
      uploadTasks.value[taskIndex].percentage = 100;
      ElNotification.success({
        title: '成功',
        message: `${file.name} 上传成功`
      });
      emit('upload-success', { file, identifier });
      
      // 检查是否所有文件都上传完成
      const allCompleted = uploadTasks.value.every(task => 
        task.status === 'success' || task.status === 'exception'
      );
      
      if (allCompleted) {
        const allSuccess = uploadTasks.value.every(task => task.status === 'success');
        emit('upload-complete', allSuccess);
        // 所有文件上传完成后，延迟一段时间清空上传任务列表
        setTimeout(() => {
          uploadTasks.value = [];
          upload.fileList = [];
          uploadRef.value.clearFiles();
        }, 2000); // 2秒后清空，让用户能看到完成状态
      }
    }
  } catch (error) {
    const taskIndex = uploadTasks.value.findIndex(task => task.fileName === file.name);
    if (taskIndex !== -1) {
      uploadTasks.value[taskIndex].status = 'exception';
    }
    console.error('上传失败:', error);
    ElNotification.error({
      title: '错误',
      message: `文件上传失败：${error.message}`
    });
    emit('upload-error', file, error);
    
    // 检查是否所有文件都已处理完成
    const allCompleted = uploadTasks.value.every(task => 
      task.status === 'success' || task.status === 'exception'
    );
    
    if (allCompleted) {
      const allSuccess = uploadTasks.value.every(task => task.status === 'success');
      emit('upload-complete', allSuccess);
    }
  }
}

// 提交上传
function submitUpload() {
  if (!selectedFileType.value) {
    ElNotification.error({
      title: '错误',
      message: '请选择文件类型！'
    });
    return;
  }
  
  if (upload.fileList.length === 0) {
    ElNotification.warning({
      title: '警告',
      message: '请选择要上传的文件！'
    });
    return;
  }

  // 清空之前的上传任务
  uploadTasks.value = [];

  // 遍历文件列表进行上传
  upload.fileList.forEach(file => {
    customUpload({ file: file.raw });
  });
}

// 清空文件列表
function clearFiles() {
  upload.fileList = [];
  uploadTasks.value = []; // 清空上传任务列表
  uploadRef.value.clearFiles();
}

// 导出方法供父组件使用
defineExpose({
  clearFiles,
  submitUpload
});
</script>

<style scoped>
.file-uploader {
  margin-bottom: 20px;
}

.mb20 {
  margin-bottom: 20px;
}

.file-type-select {
  display: flex;
  align-items: center;
}

.file-type-select__input {
  width: 100%;
}

.progress-item {
  margin-bottom: 15px;
  background-color: #ffffff;
  padding: 12px 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.progress-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.mt20 {
  margin-top: 20px;
}

.upload-progress {
  background-color: #f8f9fb;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.progress-info .file-name {
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  color: #303133;
}

.progress-info .percentage {
  font-weight: 600;
}

.upload-control {
  display: flex;
  gap: 10px;
}

:deep(.el-upload-dragger) {
  padding: 30px;
  height: auto;
  background-color: #f8f9fb;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  box-sizing: border-box;
  transition: all 0.3s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background-color: #f5f7fa;
}

:deep(.el-upload-dragger .el-icon--upload) {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

:deep(.el-upload__text) {
  color: #606266;
  font-size: 16px;
}

:deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
  font-weight: 500;
}

:deep(.el-upload__tip) {
  text-align: center;
  color: #909399;
  margin-top: 10px;
}

:deep(.el-upload-list__item) {
  transition: all 0.3s ease;
  margin-top: 10px;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #f8f9fb;
}

:deep(.el-upload-list__item:hover) {
  background-color: #f2f6fc;
}

:deep(.el-upload-list__item-name) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-progress) {
  margin-top: 2px;
}

.upload-progress h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}
</style> 