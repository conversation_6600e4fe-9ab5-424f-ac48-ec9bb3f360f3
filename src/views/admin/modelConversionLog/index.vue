<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="转换状态" clearable style="width: 240px">
            <el-option label="失败" value="0" />
            <el-option label="成功" value="1" />
            <el-option label="处理中" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="queryParams.username"
            placeholder="请输入用户名"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="日期范围" style="width: 308px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          ></el-date-picker>
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8 toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['admin:transferLog:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" style="margin-left: auto;"></right-toolbar>
      </el-row>

      <!-- 日志列表表格 -->
      <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="50" align="center"/>
        <el-table-column label="日志编号" align="center" prop="id" />
        <el-table-column label="配置文件" align="left" :show-overflow-tooltip="false">
          <template #default="scope">
            <div class="model-cell" v-if="scope.row.configIdentifier">
              <el-tooltip :content="scope.row.configFileName || '加载中...'" placement="top" :show-after="200">
                <span class="file-name">{{ scope.row.configFileName || '加载中...' }}</span>
              </el-tooltip>
              <el-button link type="primary" icon="Download" @click.stop="handleDownloadFile(scope.row.configIdentifier)">
                下载
              </el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="原始模型" align="left" :show-overflow-tooltip="false">
          <template #default="scope">
            <div class="model-cell" v-if="scope.row.modelIdentifier">
              <el-tooltip :content="scope.row.fileName || '加载中...'" placement="top" :show-after="200">
                <span class="file-name">{{ scope.row.fileName || '加载中...' }}</span>
              </el-tooltip>
              <el-button link type="primary" icon="Download" @click.stop="handleDownloadFile(scope.row.modelIdentifier)">
                下载
              </el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="转换日志" align="left" :show-overflow-tooltip="false">
          <template #default="scope">
            <div class="model-cell" v-if="scope.row.logIdentifier">
              <el-tooltip :content="scope.row.logFileName || '加载中...'" placement="top" :show-after="200">
                <span class="file-name">{{ scope.row.logFileName || '加载中...' }}</span>
              </el-tooltip>
              <el-button link type="primary" icon="Download" @click.stop="handleDownloadFile(scope.row.logIdentifier)">
                下载
              </el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="图片" align="left" :show-overflow-tooltip="false">
          <template #default="scope">
            <div class="model-cell" v-if="scope.row.imageIdentifiers && scope.row.imageIdentifiers.length > 0">
              <el-tooltip :content="scope.row.imageCount || '加载中...'" placement="top" :show-after="200">
                <span class="file-name">{{ scope.row.imageCount || '加载中...' }}</span>
              </el-tooltip>
              <el-button link type="primary" icon="Download" @click.stop="handleBatchDownload(scope.row.imageIdentifiers)">
                下载
              </el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="转换后模型" align="left" :show-overflow-tooltip="false">
          <template #default="scope">
            <div class="model-cell" v-if="scope.row.transferModelIdentifier">
              <el-tooltip :content="scope.row.transferFileName || '加载中...'" placement="top" :show-after="200">
                <span class="file-name">{{ scope.row.transferFileName || '加载中...' }}</span>
              </el-tooltip>
              <el-button link type="primary" icon="Download" @click.stop="handleDownloadFile(scope.row.transferModelIdentifier)">
                下载
              </el-button>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="转换状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" effect="plain">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="用户" align="center" prop="username" :show-overflow-tooltip="true" />
        <el-table-column label="开始时间" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="耗时(秒)" align="center" prop="costs" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['admin:transferLog:query']">
              详情
            </el-button>
            <el-button link type="primary" icon="Download" @click="handlePackageDownload(scope.row)" v-hasPermi="['admin:transferLog:query']">
              打包下载
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['admin:transferLog:remove']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 查看日志详情对话框 -->
    <el-dialog title="模型转换日志详细" v-model="open" width="650px" append-to-body>
      <el-descriptions :column="2" border size="small">
        <el-descriptions-item label="日志编号">{{ form.id }}</el-descriptions-item>
        <el-descriptions-item label="任务ID">{{ form.taskId }}</el-descriptions-item>
        <el-descriptions-item label="配置文件">
          <div class="model-info" v-if="form.configIdentifier">
            <el-tooltip :content="form.configFileName || '-'" placement="top" :show-after="200">
              <span class="file-name">{{ form.configFileName || '-' }}</span>
            </el-tooltip>
            <el-button link type="primary" icon="Download" @click="handleDownloadFile(form.configIdentifier)">
              下载
            </el-button>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="原始模型">
          <div class="model-info" v-if="form.modelIdentifier">
            <el-tooltip :content="form.fileName || '-'" placement="top" :show-after="200">
              <span class="file-name">{{ form.fileName || '-' }}</span>
            </el-tooltip>
            <el-button link type="primary" icon="Download" @click="handleDownloadFile(form.modelIdentifier)">
              下载
            </el-button>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="转换日志">
          <div class="model-info" v-if="form.logIdentifier">
            <el-tooltip :content="form.logFileName || '-'" placement="top" :show-after="200">
              <span class="file-name">{{ form.logFileName || '-' }}</span>
            </el-tooltip>
            <el-button link type="primary" icon="Download" @click="handleDownloadFile(form.logIdentifier)">
              下载
            </el-button>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="图片">
          <div class="model-info" v-if="form.imageIdentifiers && form.imageIdentifiers.length > 0">
            <el-tooltip :content="form.imageCount || '-'" placement="top" :show-after="200">
              <span class="file-name">{{ form.imageCount || '-' }}</span>
            </el-tooltip>
            <el-button link type="primary" icon="Download" @click="handleBatchDownload(form.imageIdentifiers)">
              下载
            </el-button>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="转换后模型">
          <div class="model-info" v-if="form.transferModelIdentifier">
            <el-tooltip :content="form.transferFileName || '-'" placement="top" :show-after="200">
              <span class="file-name">{{ form.transferFileName || '-' }}</span>
            </el-tooltip>
            <el-button link type="primary" icon="Download" @click="handleDownloadFile(form.transferModelIdentifier)">
              下载
            </el-button>
          </div>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="转换状态">
          <el-tag :type="getStatusType(form.status)" effect="plain">
            {{ getStatusText(form.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户">{{ form.username }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ parseTime(form.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="耗时(秒)">{{ form.costs }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ form.createBy }}</el-descriptions-item>
        <el-descriptions-item label="错误信息" :span="2" v-if="form.status === '0'">
          <el-tooltip :content="form.errorInfo" placement="top" :show-after="200" :disabled="!form.errorInfo">
            <div class="error-info">{{ form.errorInfo }}</div>
          </el-tooltip>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" size="small" icon="Download" @click="handlePackageDownload(form)">打包下载</el-button>
          <el-button size="small" @click="open = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ModelConversionLog">
import { 
  listModelConversionLog, 
  getModelConversionLog,
  delModelConversionLog
} from "@/api/admin/modelConversionLog";
import { getDownloadUrl, batchDownloadFiles } from "@/api/admin/file";

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 日志表格数据
const logList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示详情弹出层
const open = ref(false);
// 日期范围
const dateRange = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  status: undefined,
  username: undefined,
  startTime: undefined,
  endTime: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  taskId: undefined,
  status: undefined,
  createTime: undefined,
  endTime: undefined,
  costs: undefined,
  errorInfo: undefined,
  createBy: undefined,
  modelIdentifier: undefined,
  fileName: undefined,
  transferModelIdentifier: undefined,
  transferFileName: undefined,
  configIdentifier: undefined,
  configFileName: undefined,
  logIdentifier: undefined,
  logFileName: undefined,
  imageIdentifiers: [],
  imageCount: 0
});

/** 获取状态类型 */
function getStatusType(status) {
  if (status === "1") return "success";
  if (status === "0") return "danger";
  if (status === "2") return "warning";
  return "info";
}

/** 获取状态文本 */
function getStatusText(status) {
  if (status === "1") return "成功";
  if (status === "0") return "失败";
  if (status === "2") return "处理中";
  return "未知";
}

/** 查询模型转换日志列表 */
function getList(pagination) {
  loading.value = true;
  // 创建请求参数对象
  const params = {
    ...queryParams.value
  };
  
  // 如果传入了分页参数，更新查询参数
  if (pagination) {
    queryParams.value.pageNum = pagination.page;
    queryParams.value.pageSize = pagination.limit;
  }
  
  // 如果dateRange有值，使用dateRange的值覆盖startTime和endTime
  if (dateRange.value && dateRange.value.length === 2) {
    params.startTime = dateRange.value[0];
    params.endTime = dateRange.value[1];
  }
  
  listModelConversionLog(params).then(response => {
    logList.value = response.rows;
    total.value = response.total;
    loading.value = false;
    
    // 从返回数据中直接获取文件名称
    logList.value.forEach(item => {
      // 获取原始模型文件名
      if (item.modelIdentifier && item.modelIdentifierTask) {
        item.fileName = item.modelIdentifierTask.fileName;
      }
      
      // 获取转换后模型文件名
      if (item.transferModelIdentifier && item.transferModelIdentifierTask) {
        item.transferFileName = item.transferModelIdentifierTask.fileName;
      }
      
      // 获取配置文件名
      if (item.configIdentifier && item.configIdentifierTask) {
        item.configFileName = item.configIdentifierTask.fileName;
      }
      
      // 获取日志文件名
      if (item.logIdentifier && item.logIdentifierTask) {
        item.logFileName = item.logIdentifierTask.fileName;
      }
      
      // 处理图片数量
      if (item.imageIdentifiers && item.imageIdentifiers.length > 0) {
        item.imageCount = `${item.imageIdentifiers.length}张图片`;
      }
    });
  });
}

/** 日期范围变化时的处理函数 */
function handleDateChange(val) {
  if (val) {
    queryParams.value.startTime = val[0];
    queryParams.value.endTime = val[1];
  } else {
    queryParams.value.startTime = undefined;
    queryParams.value.endTime = undefined;
  }
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  queryParams.value.startTime = undefined;
  queryParams.value.endTime = undefined;
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 详细按钮操作 */
function handleView(row) {
  // 先保存选中行的文件名信息，以便在详情模态框中使用
  const fileNames = {
    fileName: row.fileName || '',
    transferFileName: row.transferFileName || '',
    configFileName: row.configFileName || '',
    logFileName: row.logFileName || '',
    imageCount: row.imageCount || ''
  };
  
  open.value = true;
  getModelConversionLog(row.id).then(response => {
    form.value = response.data;
    console.log('详情数据:', response.data);
    
    // 从返回数据中直接获取文件名称
    // 获取原始模型文件名
    if (form.value.modelIdentifier) {
      if (form.value.modelIdentifierTask) {
        form.value.fileName = form.value.modelIdentifierTask.fileName;
      } else {
        // 如果详情接口没有返回modelIdentifierTask，则使用选中行的文件名
        form.value.fileName = fileNames.fileName || '未知文件';
      }
    }
    
    // 获取转换后模型文件名
    if (form.value.transferModelIdentifier) {
      if (form.value.transferModelIdentifierTask) {
        form.value.transferFileName = form.value.transferModelIdentifierTask.fileName;
      } else {
        // 如果详情接口没有返回transferModelIdentifierTask，则使用选中行的文件名
        form.value.transferFileName = fileNames.transferFileName || '未知文件';
      }
    }
    
    // 获取配置文件名
    if (form.value.configIdentifier) {
      if (form.value.configIdentifierTask) {
        form.value.configFileName = form.value.configIdentifierTask.fileName;
      } else {
        // 如果详情接口没有返回configIdentifierTask，则使用选中行的文件名
        form.value.configFileName = fileNames.configFileName || '未知文件';
      }
    }
    
    // 获取日志文件名
    if (form.value.logIdentifier) {
      if (form.value.logIdentifierTask) {
        form.value.logFileName = form.value.logIdentifierTask.fileName;
      } else {
        // 如果详情接口没有返回logIdentifierTask，则使用选中行的文件名
        form.value.logFileName = fileNames.logFileName || '未知文件';
      }
    }
    
    // 处理图片数量
    if (form.value.imageIdentifiers && form.value.imageIdentifiers.length > 0) {
      if (fileNames.imageCount) {
        form.value.imageCount = fileNames.imageCount;
      } else {
        form.value.imageCount = `${form.value.imageIdentifiers.length}张图片`;
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const logIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除日志编号为"' + logIds + '"的数据项？').then(() => {
    return delModelConversionLog(logIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 下载文件 */
function handleDownloadFile(identifier) {
  getDownloadUrl(identifier, 3600, true, 'transfer-model-log').then(res => {
    if (res.code === 200 && res.data) {
      // 创建隐藏的a标签进行下载
      const link = document.createElement('a');
      link.href = res.data;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      proxy.$modal.msgError('获取下载链接失败');
    }
  }).catch(() => {
    proxy.$modal.msgError('获取下载链接失败');
  });
}

/** 批量下载文件 */
function handleBatchDownload(identifiers) {
  if (!identifiers || identifiers.length === 0) {
    return;
  }
  batchDownloadFiles(identifiers).then(res => {
    // 创建Blob对象，明确指定类型为zip
    const blob = new Blob([res], { type: 'application/zip' });
    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    // 创建a标签并模拟点击下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = 'images.zip'; // 设置默认文件名
    document.body.appendChild(link);
    link.click();
    // 释放URL对象
    window.URL.revokeObjectURL(downloadUrl);
    // 移除临时创建的a标签
    document.body.removeChild(link);
  }).catch(() => {
    proxy.$modal.msgError('批量下载失败');
  });
}

/** 打包下载当前行所有文件 */
function handlePackageDownload(row) {
  // 收集所有需要下载的文件标识符
  const identifiers = [];
  
  // 添加原始模型文件
  if (row.modelIdentifier) {
    identifiers.push(row.modelIdentifier);
  }
  
  // 添加配置文件
  if (row.configIdentifier) {
    identifiers.push(row.configIdentifier);
  }
  
  // 添加转换后模型文件
  if (row.transferModelIdentifier) {
    identifiers.push(row.transferModelIdentifier);
  }
  
  // 添加日志文件
  if (row.logIdentifier) {
    identifiers.push(row.logIdentifier);
  }
  
  // 添加图片文件
  if (row.imageIdentifiers && row.imageIdentifiers.length > 0) {
    identifiers.push(...row.imageIdentifiers);
  }
  
  // 如果没有文件可下载，提示用户
  if (identifiers.length === 0) {
    proxy.$modal.msgInfo('没有可下载的文件');
    return;
  }
  
  // 调用批量下载接口
  batchDownloadFiles(identifiers).then(res => {
    // 创建Blob对象，明确指定类型为zip
    const blob = new Blob([res], { type: 'application/zip' });
    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob);
    // 创建a标签并模拟点击下载
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `模型转换_${row.id}.zip`; // 使用id替代taskId作为文件名
    document.body.appendChild(link);
    link.click();
    // 释放URL对象
    window.URL.revokeObjectURL(downloadUrl);
    // 移除临时创建的a标签
    document.body.removeChild(link);
  }).catch(() => {
    proxy.$modal.msgError('打包下载失败');
  });
}

// 初始化数据
getList();
</script>

<style scoped>
/* app-container样式修改 */
:deep(.app-container) {
  padding: 10px;
  background-color: transparent;
}

:deep(.el-card) {
  border: none;
  box-shadow: none !important;
}

:deep(.el-card__header) {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 10px 0;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 15px;
  background-color: #f8f9fb;
  padding: 15px 15px 5px;
  border-radius: 4px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.right-menu {
  display: flex;
  gap: 10px;
}

.mb8 {
  margin-bottom: 18px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

/* 搜索表单样式 */
.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 15px;
}

.search-form :deep(.el-form-item__label) {
  color: #606266;
}

.search-buttons {
  margin-left: auto;
}

/* 工具栏样式 */
.toolbar-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  padding: 0;
}

.toolbar-row .el-col {
  margin-bottom: 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  table-layout: fixed;
  margin-top: 8px;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table--border) th, :deep(.el-table--border) td {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table th) {
  background-color: #f8f9fb !important;
  color: #606266;
  font-weight: 600;
  height: 45px;
  text-align: center;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

:deep(.el-table__row:hover td) {
  background-color: #f2f6fc !important;
}

:deep(.el-table__header-wrapper) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__body-wrapper) {
  overflow: auto;
}

:deep(.el-table .cell) {
  line-height: 1.5;
}

/* 操作列按钮样式 */
:deep(.el-button--link) {
  margin: 0 4px;
  font-size: 13px;
  height: 24px;
  line-height: 24px;
}

/* 状态标签居中 */
:deep(.el-table .el-tag) {
  display: inline-block;
}

/* 日志名称单元格样式 */
.log-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-left: 5px;
}

.log-icon {
  font-size: 18px;
  color: #409eff;
  flex-shrink: 0;
}

/* 卡片样式优化 */
:deep(.el-card) {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8vh !important;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  background-color: #f8f9fb;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 15px 20px;
}

:deep(.el-dialog__footer) {
  padding: 8px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

.dialog-footer {
  text-align: right;
}

.error-info {
  max-height: 80px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.5;
  word-break: break-all;
}

/* 按钮样式优化 */
:deep(.el-button) {
  border-radius: 4px;
}

/* 详情页样式 */
:deep(.el-descriptions) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

:deep(.el-descriptions__title) {
  font-weight: 600;
  margin-bottom: 15px;
}

:deep(.el-descriptions__label) {
  background-color: #f8f9fb;
  font-weight: 500;
}

/* 状态标签样式优化 */
:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  border: none;
  transition: none;
  transform: none;
  backface-visibility: visible;
}

:deep(.el-table .el-tag) {
  transition: none !important;
  transform: none !important;
  animation: none !important;
  backface-visibility: visible !important;
}

:deep(.el-tag--primary) {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

:deep(.el-tag--success) {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

:deep(.el-tag--warning) {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #faecd8;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fde2e2;
}

:deep(.el-tag--info) {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #e9e9eb;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 15px;
  justify-content: flex-end;
}

/* 文件名和下载按钮样式 */
.model-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 5px;
}

/* 详情对话框中的模型信息样式 */
.model-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.model-info span {
  margin-right: 10px;
}

.model-info :deep(.el-button--link) {
  padding: 0 5px;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* 详情对话框样式优化 */
:deep(.el-descriptions--small .el-descriptions__body) {
  padding: 0;
}

:deep(.el-descriptions--small .el-descriptions__label) {
  padding: 6px 8px;
  font-size: 13px;
}

:deep(.el-descriptions--small .el-descriptions__content) {
  padding: 6px 8px;
  font-size: 13px;
}
</style> 