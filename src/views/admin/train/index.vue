<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-form-item label="模型名称" prop="modelName">
          <el-input
            v-model="queryParams.modelName"
            placeholder="请输入模型名称"
            clearable
            @keyup.enter="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="模型类别" prop="modelType">
          <el-select
            v-model="queryParams.modelType"
            placeholder="请选择模型类别"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in modelTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="芯片类型" prop="chipType">
          <el-input
            v-model="queryParams.chipType"
            placeholder="请输入芯片类型"
            clearable
            @keyup.enter="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8 toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['admin:train:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['admin:train:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" style="margin-left: auto;"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="trainList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" />
        <el-table-column label="模型名称" align="center" prop="modelName" :show-overflow-tooltip="true" />
        <el-table-column label="模型类别" align="center" prop="modelType" />
        <el-table-column label="芯片类型" align="center" prop="chipType" />
        <el-table-column label="模型描述" align="center" prop="modelDesc" min-width="180" class-name="description-column">
          <template #default="scope">
            <el-tooltip 
              placement="top" 
              :content="scope.row.modelDesc" 
              :max-width="600"
            >
              <template #content>
                <div style="max-width: 600px; word-break: break-word; white-space: pre-wrap; line-height: 1.5; padding: 8px 12px;">
                  {{ scope.row.modelDesc }}
                </div>
              </template>
              <div class="description-text">{{ scope.row.modelDesc }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="下载次数" align="center" prop="downloadCount" />
        <el-table-column label="创建时间" align="center" prop="createTime" >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['admin:train:query']">
              查看
            </el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['admin:train:edit']">
              编辑
            </el-button>
            <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)" v-hasPermi="['admin:train:download']">
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改训练工程对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="trainForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="form.modelName" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="模型类别" prop="modelType">
          <el-select v-model="form.modelType" placeholder="请选择模型类别" style="width: 100%">
            <el-option
              v-for="dict in modelTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模型描述" prop="modelDesc">
          <el-input v-model="form.modelDesc" type="textarea" placeholder="请输入模型描述" :rows="6" />
        </el-form-item>
        <el-form-item label="芯片类型" prop="chipType">
          <el-input v-model="form.chipType" placeholder="请输入芯片类型" />
        </el-form-item>
        <el-form-item label="训练工程文件" class="nowrap-label required-label">
          <el-upload
            class="file-uploader"
            action="#"
            :auto-upload="false"
            :show-file-list="true"
            :limit="1"
            :on-exceed="() => proxy.$modal.msgWarning('只能上传一个文件')"
            :before-upload="beforeUpload"
            :on-change="handleFileChange"
            :on-remove="handleRemove"
            :file-list="uploadFile ? [uploadFile] : []"
          >
            <template #trigger>
              <el-button type="primary" :loading="uploadLoading">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                请上传训练工程文件
              </div>
            </template>
          </el-upload>
          
          <!-- 上传进度详情展示 -->
          <div v-if="uploadProgress > 0" class="upload-progress mt20">
            <h3>上传进度</h3>
            <div class="progress-item">
              <div class="progress-info">
                <span class="file-name">{{ uploadFile?.name || '文件' }}</span>
                <span class="percentage">{{ Math.floor(uploadProgress) }}%</span>
              </div>
              <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
              <div class="progress-details" v-if="uploadProgress > 0 && uploadProgress < 100">
                <span>{{ uploadSpeed }} | {{ uploadRemainingTime }}</span>
              </div>
            </div>
          </div>
          
          <div v-if="fileIdentifier" class="file-identifier">
            文件标识：{{ fileIdentifier }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="uploadLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看版本详情对话框 -->
    <el-dialog title="训练工程详情" v-model="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="1" border size="large" class="version-details">
        <el-descriptions-item label="工程ID">{{ viewForm.id }}</el-descriptions-item>
        <el-descriptions-item label="模型名称">{{ viewForm.modelName }}</el-descriptions-item>
        <el-descriptions-item label="模型类别">{{ viewForm.modelType }}</el-descriptions-item>
        <el-descriptions-item label="模型描述" class="description-item">
          <div class="description-content">{{ viewForm.modelDesc }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="芯片类型">{{ viewForm.chipType }}</el-descriptions-item>
        <el-descriptions-item label="文件标识">
          <div class="file-identifier-view">{{ viewForm.fileIdentifier }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="下载次数">{{ viewForm.downloadCount }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ viewForm.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新者">{{ viewForm.updateBy }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
          <el-button type="primary" icon="Download" @click="handleDownloadView">下 载</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Train">
import { ref, reactive, onMounted, toRefs, getCurrentInstance } from 'vue'
import { ElMessageBox, ElLoading } from 'element-plus'
import { listTrain, getTrain, delTrain, addTrain, updateTrain, downloadTrain } from "@/api/admin/train";
import { parseTime } from '@/utils/ruoyi'
import { FileUploader } from "@/utils/upload";

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 训练工程表格数据
const trainList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 是否显示查看详情弹出层
const viewOpen = ref(false);
// 文件上传加载状态
const uploadLoading = ref(false);
// 上传进度
const uploadProgress = ref(0);
// 上传状态
const uploadStatus = ref('');
// 上传速度
const uploadSpeed = ref('0 MB/s');
// 剩余时间
const uploadRemainingTime = ref('计算中...');
// 上传的文件
const uploadFile = ref(null);
// 上传的文件标识符
const fileIdentifier = ref('');

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  modelName: undefined,
  modelType: undefined,
  chipType: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  modelType: undefined,
  modelName: undefined,
  modelDesc: undefined,
  chipType: undefined,
  fileIdentifier: undefined
});

// 查看详情表单
const viewForm = ref({
  id: undefined,
  modelType: undefined,
  modelName: undefined,
  modelDesc: undefined,
  chipType: undefined,
  fileIdentifier: undefined,
  downloadCount: 0,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined
});

// 表单校验
const rules = {
  modelType: [
    { required: true, message: "模型类别不能为空", trigger: "blur" }
  ],
  modelName: [
    { required: true, message: "模型名称不能为空", trigger: "blur" }
  ],
  modelDesc: [
    { required: true, message: "模型描述不能为空", trigger: "blur" }
  ],
  chipType: [
    { required: true, message: "芯片类型不能为空", trigger: "blur" }
  ]
};

// 模型类别选项
const modelTypeOptions = ref([
  { value: '检测', label: '检测' },
  { value: '分割', label: '分割' },
  { value: '姿态', label: '姿态' },
  { value: 'OCR', label: 'OCR' },
  { value: '语音', label: '语音' },
  { value: 'LLM', label: 'LLM' },
  { value: 'VLM', label: 'VLM' }
]);

/** 查询训练工程列表 */
function getList() {
  loading.value = true;
  listTrain(queryParams.value).then(response => {
    trainList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    modelType: undefined,
    modelName: undefined,
    modelDesc: undefined,
    chipType: undefined,
    fileIdentifier: undefined
  };
  uploadFile.value = null;
  fileIdentifier.value = '';
  uploadProgress.value = 0;
  uploadStatus.value = '';
  uploadSpeed.value = '0 MB/s';
  uploadRemainingTime.value = '计算中...';
  proxy.resetForm("trainForm");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  title.value = "添加训练工程";
  open.value = true;
}

/** 查看按钮操作 */
function handleView(row) {
  const trainId = row.id;
  getTrain(trainId).then(response => {
    viewForm.value = response.data;
    viewOpen.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const trainId = row.id || ids.value[0];
  getTrain(trainId).then(response => {
    form.value = response.data;
    if (form.value.fileIdentifier) {
      const fileName = form.value.modelName || form.value.fileIdentifier.substring(form.value.fileIdentifier.lastIndexOf('/') + 1);
      uploadFile.value = { name: fileName, url: form.value.fileIdentifier };
      fileIdentifier.value = form.value.fileIdentifier;
    }
    title.value = "修改训练工程";
    open.value = true;
  });
}

/** 文件上传前的钩子 */
function beforeUpload(file) {
  return true;
}

/** 文件变化的钩子 */
function handleFileChange(file) {
  if (file && file.raw) {
    uploadFile.value = file.raw;
    // 重置上传进度
    uploadProgress.value = 0;
    uploadStatus.value = '';
    uploadSpeed.value = '0 MB/s';
    uploadRemainingTime.value = '计算中...';
  }
  return false; // 阻止自动上传
}

/** 移除文件 */
function handleRemove() {
  uploadFile.value = null;
  fileIdentifier.value = '';
  uploadProgress.value = 0;
  uploadStatus.value = '';
  uploadSpeed.value = '0 MB/s';
  uploadRemainingTime.value = '计算中...';
  form.value.fileIdentifier = undefined;
}

/** 上传文件 */
async function uploadTrainFile() {
  if (!uploadFile.value) {
    proxy.$modal.msgWarning('请先选择文件');
    return false;
  }
  
  try {
    uploadLoading.value = true;
    uploadStatus.value = 'primary';
    
    const tempUploader = new FileUploader(uploadFile.value);
    const identifier = await tempUploader.calculateMD5();
    
    const uploader = new FileUploader(uploadFile.value, identifier);
    
    const uploadedIdentifier = await uploader.upload('train-engineer', (progress) => {
      uploadProgress.value = progress.percent;
      uploadSpeed.value = progress.speed ? progress.speed + ' MB/s' : '计算中...';
      uploadRemainingTime.value = progress.remainingTime || '计算中...';
      
      // 更新状态
      if (progress.percent === 100) {
        uploadStatus.value = 'success';
      } else if (progress.percent < 100) {
        uploadStatus.value = 'primary';
      }
    });
    
    fileIdentifier.value = uploadedIdentifier;
    form.value.fileIdentifier = uploadedIdentifier;
    uploadStatus.value = 'success';
    proxy.$modal.msgSuccess('文件上传成功');
    return true;
  } catch (error) {
    console.error('文件上传失败:', error);
    uploadStatus.value = 'exception';
    proxy.$modal.msgError('文件上传失败: ' + error.message);
    return false;
  } finally {
    uploadLoading.value = false;
  }
}

/** 提交按钮 */
async function submitForm() {
  proxy.$refs.trainForm.validate(async (valid) => {
    if (valid) {
      // 检查是否选择了文件（新增时）或已有文件标识（编辑时）
      if (!uploadFile.value && !fileIdentifier.value) {
        proxy.$modal.msgWarning('请选择训练工程文件');
        return;
      }
      
      // 如果有文件需要上传，先上传文件
      if (uploadFile.value && !fileIdentifier.value) {
        const uploadSuccess = await uploadTrainFile();
        if (!uploadSuccess) return;
      }
      
      // 确保文件标识被正确赋值到表单
      if (fileIdentifier.value) {
        form.value.fileIdentifier = fileIdentifier.value;
      }
      
      if (form.value.id != undefined) {
        updateTrain(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      } else {
        addTrain(form.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          } else {
            proxy.$modal.msgError(response.msg);
          }
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const trainIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除训练工程编号为"' + trainIds + '"的数据项？').then(() => {
    return delTrain(trainIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 下载按钮操作 */
function handleDownload(row) {
  const identifier = row.fileIdentifier;
  if (!identifier) {
    proxy.$modal.msgError("文件标识符不存在，无法下载");
    return;
  }
  
  const loading = proxy.$loading({
    lock: true,
    text: '获取下载地址中...',
    background: 'rgba(0, 0, 0, 0.7)',
  });
  
  downloadTrain(identifier).then(response => {
    if (response.code === 200 && response.msg) {
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = response.msg;
      link.setAttribute('download', row.modelName); 
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      proxy.$modal.msgSuccess("下载成功");
    } else {
      proxy.$modal.msgError('获取下载地址失败');
    }
  }).catch(error => {
    console.error('下载失败:', error);
    // proxy.$modal.msgError('下载失败，请稍后重试');
  }).finally(() => {
    loading.close();
  });
}

/** 从详情页下载 */
function handleDownloadView() {
  const identifier = viewForm.value.fileIdentifier;
  if (!identifier) {
    proxy.$modal.msgError("文件标识符不存在，无法下载");
    return;
  }
  
  const loading = proxy.$loading({
    lock: true,
    text: '获取下载地址中...',
    background: 'rgba(0, 0, 0, 0.7)',
  });
  
  downloadTrain(identifier).then(response => {
    if (response.code === 200 && response.msg) {
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = response.msg;
      link.setAttribute('download', viewForm.value.modelName); 
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      proxy.$modal.msgSuccess("下载成功");
    } else {
      proxy.$modal.msgError('获取下载地址失败');
    }
  }).catch(error => {
    console.error('下载失败:', error);
    proxy.$modal.msgError('下载失败，请稍后重试');
  }).finally(() => {
    loading.close();
  });
}

// 初始化数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 基础容器样式 */
:deep(.app-container) {
  padding: 10px;
  background-color: transparent;
}

/* 卡片样式优化 */
:deep(.el-card) {
  border: none;
  box-shadow: none !important;
  border-radius: 4px;
}

:deep(.el-card__header) {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 10px 0;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 15px;
  background-color: #f8f9fb;
  padding: 15px 15px 5px;
  border-radius: 4px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 15px;
}

.search-form :deep(.el-form-item__label) {
  color: #606266;
}

.search-buttons {
  margin-left: auto;
}

/* 工具栏样式 */
.mb8 {
  margin-bottom: 18px;
}

.toolbar-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  padding: 0;
}

.toolbar-row .el-col {
  margin-bottom: 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  table-layout: fixed;
  margin-top: 8px;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table--border) th, :deep(.el-table--border) td {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table th) {
  background-color: #f8f9fb !important;
  color: #606266;
  font-weight: 600;
  height: 45px;
  text-align: center;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table .cell) {
  line-height: 1.5;
}

/* 描述列样式 */
.description-column :deep(.cell) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.description-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 操作列按钮样式 */
:deep(.el-button--link) {
  margin: 0 4px;
  font-size: 13px;
  height: 24px;
  line-height: 24px;
}

:deep(.el-button) {
  border-radius: 4px;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8vh !important;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  background-color: #f8f9fb;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 文本域样式 */
:deep(.el-textarea__inner) {
  min-height: 140px !important;
  line-height: 1.6;
  padding: 12px;
}

/* 版本详情样式 */
.version-details :deep(.el-descriptions__label) {
  width: 120px;
  font-weight: 500;
}

.version-details :deep(.el-descriptions__content) {
  padding: 12px 15px;
}

.description-item :deep(.el-descriptions__content) {
  white-space: pre-line;
  line-height: 1.6;
}

.description-content {
  white-space: pre-line;
  line-height: 1.6;
  max-height: 200px;
  overflow-y: auto;
  padding: 5px;
}

.file-identifier-view {
  word-break: break-all;
  line-height: 1.5;
}

/* 分页样式 */
:deep(.el-pagination) {
  margin-top: 15px;
  justify-content: flex-end;
}

/* 文件上传相关样式 */
.file-uploader {
  width: 100%;
}

.file-identifier {
  margin-top: 10px;
  font-size: 13px;
  color: #606266;
  word-break: break-all;
}
/* 不换行标签样式 */
.nowrap-label :deep(.el-form-item__label) {
  white-space: nowrap;
}

/* 必填标签样式 */
.required-label :deep(.el-form-item__label)::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 上传进度样式 */
.upload-progress {
  background-color: #f8f9fb;
  padding: 12px 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  margin: 12px 0;
}

.upload-progress h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #303133;
  font-size: 15px;
  font-weight: 600;
}

.progress-item {
  margin-bottom: 8px;
  background-color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  width: 100%;
}

.progress-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
  width: 100%;
}

.progress-info .file-name {
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  color: #303133;
}

.progress-info .percentage {
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

.progress-details {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  padding: 0 2px;
}

:deep(.el-progress) {
  margin-top: 2px;
}

:deep(.el-progress-bar) {
  width: 100%;
}

:deep(.el-progress-bar__outer) {
  height: 8px !important;
  border-radius: 4px;
  background-color: #f0f2f5;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
  transition: all 0.3s ease;
}

:deep(.el-progress__text) {
  font-size: 13px !important;
  color: #606266;
}

.mt20 {
  margin-top: 20px;
}
</style> 