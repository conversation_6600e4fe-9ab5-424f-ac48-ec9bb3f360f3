<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px" class="search-form">
        <el-form-item label="文件名称" prop="downloadFileName">
          <el-input
            v-model="queryParams.downloadFileName"
            placeholder="请输入下载文件名"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="下载类型" prop="downloadType">
          <el-input
            v-model="queryParams.downloadType"
            placeholder="请输入下载类型"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="下载用户" prop="username">
          <el-input
            v-model="queryParams.username"
            placeholder="请输入下载用户"
            clearable
            @keyup.enter.native="handleQuery"
            style="width: 180px"
          />
        </el-form-item>
        <el-form-item label="下载时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作工具栏 -->
      <el-row :gutter="10" class="mb8 toolbar-row">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['admin:downloadRecord:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" style="margin-left: auto;"></right-toolbar>
      </el-row>

      <!-- 下载记录表格 -->
      <el-table v-loading="loading" :data="downloadRecordList" @selection-change="handleSelectionChange" border stripe>
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="60" />
        <el-table-column label="下载文件名" align="left" prop="downloadFileName" min-width="16.6%" :show-overflow-tooltip="true" />
        <el-table-column label="下载类型" align="center" prop="downloadType" min-width="16.6%" :show-overflow-tooltip="true" />
        <el-table-column label="下载用户" align="center" prop="username" min-width="16.6%" :show-overflow-tooltip="true" />
        <el-table-column label="下载时间" align="center" prop="downloadTime" min-width="16.6%">
          <template #default="scope">
            <span>{{ parseTime(scope.row.downloadTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" min-width="16.6%">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="16.6%" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['admin:downloadRecord:query']">
              详情
            </el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['admin:downloadRecord:remove']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 下载记录详情对话框 -->
    <el-dialog title="下载记录详情" v-model="viewOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">{{ viewData.id }}</el-descriptions-item>
        <el-descriptions-item label="下载文件名">{{ viewData.downloadFileName }}</el-descriptions-item>
        <el-descriptions-item label="下载类型">{{ viewData.downloadType }}</el-descriptions-item>
        <el-descriptions-item label="下载用户">{{ viewData.username }}</el-descriptions-item>
        <el-descriptions-item label="下载时间">{{ viewData.downloadTime }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ viewData.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewData.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewData.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="DownloadRecord">
import { listDownloadRecord, getDownloadRecord, delDownloadRecord } from "@/api/admin/downloadRecord";
import { parseTime } from "@/utils/ruoyi";

// 组件挂载点
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 下载记录表格数据
const downloadRecordList = ref([]);
// 是否显示详情弹窗
const viewOpen = ref(false);
// 详情数据
const viewData = ref({});
// 日期范围
const dateRange = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  downloadFileName: undefined,
  downloadType: undefined,
  username: undefined
});

/** 查询下载记录列表 */
function getList() {
  loading.value = true;
  
  // 处理日期范围
  let params = {...queryParams.value};
  if (dateRange.value && dateRange.value.length === 2) {
    params.startTime = dateRange.value[0];
    params.endTime = dateRange.value[1];
    console.log("日期范围参数:", params.startTime, params.endTime);
  }
  
  listDownloadRecord(params).then(response => {
    downloadRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(error => {
    console.error("获取下载记录列表失败:", error);
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryForm");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 查看详情按钮操作 */
function handleView(row) {
  viewData.value = {};
  getDownloadRecord(row.id).then(response => {
    viewData.value = response.data;
    viewOpen.value = true;
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const recordIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除下载记录编号为"' + recordIds + '"的数据项？').then(() => {
    return delDownloadRecord(recordIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
/* app-container样式修改 */
:deep(.app-container) {
  padding: 10px;
  background-color: transparent;
}

:deep(.el-card) {
  border: none;
  box-shadow: none !important;
}

:deep(.el-card__header) {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  background-color: transparent;
}

:deep(.el-card__body) {
  padding: 10px 0;
}

/* 搜索表单样式 */
.search-form {
  margin-bottom: 15px;
  background-color: #f8f9fb;
  padding: 15px 15px 5px;
  border-radius: 4px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 15px;
}

.search-form :deep(.el-form-item__label) {
  color: #606266;
}

.search-buttons {
  margin-left: auto;
}

/* 工具栏样式 */
.toolbar-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
  padding: 0;
}

.toolbar-row .el-col {
  margin-bottom: 8px;
}

.mb8 {
  margin-bottom: 18px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  width: 100%;
  table-layout: fixed;
  margin-top: 8px;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table--border) th, :deep(.el-table--border) td {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table th) {
  background-color: #f8f9fb !important;
  color: #606266;
  font-weight: 600;
  height: 45px;
  text-align: center;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

:deep(.el-table__row:hover td) {
  background-color: #f2f6fc !important;
}

/* 操作列按钮样式 */
:deep(.el-button--link) {
  margin: 0 4px;
  font-size: 13px;
  height: 24px;
  line-height: 24px;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8vh !important;
}

:deep(.el-dialog__header) {
  padding: 15px 20px;
  background-color: #f8f9fb;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f8f9fb;
}

/* 详情页样式 */
:deep(.el-descriptions) {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

:deep(.el-descriptions__label) {
  background-color: #f8f9fb;
  font-weight: 500;
}
</style> 