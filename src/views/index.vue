<template>
  <div class="fullpage-container">
    <div class="home-background">
      <div class="content-overlay">
        <div class="top-section">
          <div class="welcome-section">
            <h1 class="welcome-title">欢迎使用 LFNN 开发者社区</h1>
            <p class="welcome-desc">一站式AI芯片开发支持社区平台</p>
          </div>
        </div>
        
        <div class="main-section">
          <div class="features-container">
            <div 
              class="feature-card" 
              v-for="(feature, index) in features" 
              :key="index" 
              @click="handleCardClick(feature)"
            >
              <div class="permission-tip" v-if="!feature.hasPermission">
                <span>暂无权限访问</span>
                <el-icon><Lock /></el-icon>
              </div>
              <div class="not-available-tip" v-if="feature.notAvailable">
                <span>功能暂未开放</span>
                <el-icon><InfoFilled /></el-icon>
              </div>
              <div class="feature-icon">
                <div class="icon-wrapper">
                  <el-icon :size="50"><component :is="feature.elIcon" /></el-icon>
                </div>
              </div>
              <div class="feature-content">
                <h3 class="feature-title">{{ feature.title }}</h3>
                <p class="feature-desc">{{ feature.description }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bottom-section">
          <!-- 底部内容区域，确保延伸到底部 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Index">
import { useRouter } from 'vue-router'
import useSettingsStore from '@/store/modules/settings'
import { computed, reactive } from 'vue'
import { checkPermi } from '@/utils/permission'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  Collection, 
  Download, 
  Document, 
  Monitor, 
  Cpu,
  Lock,
  InfoFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const settingsStore = useSettingsStore()

// 获取主题色
const themeColor = computed(() => settingsStore.theme)

const features = reactive([
  {
    icon: 'el-icon-refresh',
    iconSvg: 'component',
    aliIcon: 'iconfont-conversion',
    elIcon: Refresh,
    title: '模型转换工具',
    description: '支持在线模型转换，提供便捷的模型转换服务，支持多种主流模型格式',
    route: '/ai/model-conversion/index',
    permission: ['ai:modelConversion:list'],
    hasPermission: checkPermi(['ai:modelConversion:list'])
  },
  {
    icon: 'el-icon-collection',
    iconSvg: 'model',
    aliIcon: 'iconfont-model',
    elIcon: Collection,
    title: 'Model Zoo',
    description: '丰富的预训练模型库，提供多场景示例和性能参考数据',
    route: '/ai/modelZoo/index',
    permission: ['ai:modelZooManager:list'],
    hasPermission: checkPermi(['ai:modelZooManager:list'])
  },
  {
    icon: 'el-icon-download',
    iconSvg: 'download',
    aliIcon: 'iconfont-download',
    elIcon: Download,
    title: 'SDK 下载',
    description: '提供最新的LFNN SDK版本下载，包含完整的开发工具包',
    route: '/ai/lfnn/index',
    permission: ['ai:lfnn:list'],
    hasPermission: checkPermi(['ai:lfnn:list'])
  },
  {
    icon: 'el-icon-document',
    iconSvg: 'documentation',
    aliIcon: 'iconfont-document',
    elIcon: Document,
    title: '在线文档',
    description: '详尽的技术文档，包括快速入门、API参考、最佳实践等',
    route: '/ai/document/index',
    permission: ['ai:document:list'],
    hasPermission: checkPermi(['ai:document:list'])
  },
  {
    icon: 'el-icon-monitor',
    iconSvg: 'example',
    aliIcon: 'iconfont-example',
    elIcon: Monitor,
    title: '示例训练工程',
    description: '官方训练工程示例，助您快速掌握开发流程',
    route: '/ai/trainEngineer/index',
    permission: ['ai:trainEngineer:list'],
    hasPermission: checkPermi(['ai:trainEngineer:list'])
  },
  {
    icon: 'el-icon-cpu',
    iconSvg: 'code',
    aliIcon: 'iconfont-code',
    elIcon: Cpu,
    title: 'AI编程助手',
    description: '智能编程辅助，提供代码生成和优化建议',
    route: '',
    permission: [],
    hasPermission: true,
    notAvailable: true
  }
])

const handleCardClick = (feature) => {
  if (feature.notAvailable) {
    ElMessage.info('该功能暂未开放，敬请期待')
    return
  }
  
  if (feature.route && feature.hasPermission) {
    router.push(feature.route)
  } else if (feature.route && !feature.hasPermission) {
    // 可以添加提示，告知用户没有权限
    ElMessage.warning('您没有访问该功能的权限')
  }
}
</script>

<style scoped lang="scss">
.fullpage-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  z-index: 1;
  
  .home-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-image: url('../assets/images/leapfive01.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    
    .content-overlay {
      width: 100%;
      height: 100%;
      flex: 1;
      background-color: rgba(0, 0, 0, 0.35);
      padding: 0;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }
  }

  .top-section {
    padding: 5px 20px 0;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
  }
  
  .main-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 15px;
    justify-content: center;
    position: relative;
    z-index: 2;
  }
  
  .bottom-section {
    min-height: 20px;
    flex-shrink: 0;
    padding-bottom: 5px;
    position: relative;
    z-index: 2;
  }

  .welcome-section {
    text-align: center;
    padding: 0 0 5px;
    color: white;
    margin-bottom: 5px;

    .welcome-title {
      font-size: 2.2em;
      margin-bottom: 4px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .welcome-desc {
      font-size: 1.1em;
      opacity: 0.9;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      margin-bottom: 0;
    }
  }

  .features-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 18px;
    padding: 5px;
    max-width: 1350px;
    margin: 0 auto;
    width: 100%;
    grid-auto-rows: 1fr;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    .feature-card {
      background: rgba(255, 255, 255, 0.75);
      padding: 25px 15px;
      border-radius: 12px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      text-align: center;
      backdrop-filter: blur(10px);
      display: flex;
      flex-direction: column;
      height: 280px;
      width: 100%;
      box-sizing: border-box;
      justify-content: space-evenly;
      cursor: pointer;
      border: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;

      .permission-tip {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 4px 10px;
        border-radius: 15px;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        font-size: 12px;
        color: #f56c6c;
        
        span {
          margin-right: 4px;
        }
        
        .el-icon {
          font-size: 14px;
          color: #f56c6c;
        }
      }
      
      .not-available-tip {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 4px 10px;
        border-radius: 15px;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        font-size: 12px;
        color: #e6a23c;
        
        span {
          margin-right: 4px;
        }
        
        .el-icon {
          font-size: 14px;
          color: #e6a23c;
        }
      }

      html.dark & {
        background: rgba(48, 48, 48, 0.75);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.05);
        
        .feature-title {
          color: #e0e0e0;
        }
        
        .feature-desc {
          color: #b0b0b0;
        }
        
        .icon-wrapper {
          color: var(--el-color-primary);
          background: rgba(var(--el-color-primary-rgb), 0.15);
          border-color: rgba(var(--el-color-primary-rgb), 0.2);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
          
          .el-icon {
            filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.4));
          }
        }
        
        &:hover {
          background: rgba(48, 48, 48, 0.9);
          border-color: var(--el-color-primary-light-5);
          
          .icon-wrapper {
            color: #fff;
            background: var(--el-color-primary);
            border-color: transparent;
            box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.4);
          }
        }
        
        .permission-tip {
          background-color: rgba(48, 48, 48, 0.9);
          color: #f56c6c;
          
          .el-icon {
            color: #f56c6c;
          }
        }
        
        .not-available-tip {
          background-color: rgba(48, 48, 48, 0.9);
          color: #e6a23c;
          
          .el-icon {
            color: #e6a23c;
          }
        }
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.9);
        border-color: var(--el-color-primary-light-3);
        
        .icon-wrapper {
          transform: scale(1.1);
          color: #fff;
          background: var(--el-color-primary);
          box-shadow: 0 8px 20px rgba(var(--el-color-primary-rgb), 0.5);
          border-color: transparent;
        }
      }

      .feature-icon {
        flex: 0 0 120px;
        margin-bottom: 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        
        .icon-wrapper {
          color: var(--el-color-primary);
          background: var(--el-color-primary-light-9);
          padding: 0;
          border-radius: 50%;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          width: 100px;
          height: 100px;
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
          border: 2px solid rgba(var(--el-color-primary-rgb), 0.1);
          
          .el-icon {
            font-size: 50px;
            color: inherit;
            z-index: 2;
            filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
          }
          
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%);
            z-index: 1;
            border-radius: 50%;
          }
        }
      }

      .feature-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 5px 0;
      }

      .feature-title {
        font-size: 1.2em;
        margin-bottom: 5px;
        color: #333;
        font-weight: 600;
        padding: 0 5px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .feature-desc {
        color: #555;
        line-height: 1.4;
        font-size: 1em;
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 0;
        height: auto;
        flex: 1;
      }
    }
  }
}
</style>

