import * as echarts from 'echarts'
import chinaJson from './china.json'

// 中国省份坐标信息
export const chinaGeoCoordMap = {
  '北京': [116.4551, 40.2539],
  '天津': [117.4219, 39.4189],
  '上海': [121.4648, 31.2891],
  '重庆': [107.7539, 30.1904],
  '河北': [114.4995, 38.1006],
  '河南': [113.4668, 34.6234],
  '云南': [102.9199, 25.4663],
  '辽宁': [123.1238, 42.1216],
  '黑龙江': [127.9688, 45.368],
  '湖南': [113.0823, 28.2568],
  '安徽': [117.29, 32.0581],
  '山东': [117.1582, 36.8701],
  '新疆': [87.9236, 43.5883],
  '江苏': [118.8062, 31.9208],
  '浙江': [119.5313, 29.8773],
  '江西': [116.0046, 28.6633],
  '湖北': [114.3896, 30.6628],
  '广西': [108.479, 23.1152],
  '甘肃': [103.5901, 36.3043],
  '山西': [112.3352, 37.9413],
  '内蒙古': [111.4124, 40.4901],
  '陕西': [109.1162, 34.2004],
  '吉林': [125.8154, 44.2584],
  '福建': [119.4543, 25.9222],
  '贵州': [106.6992, 26.7682],
  '广东': [113.5107, 23.2196],
  '青海': [101.4038, 36.8207],
  '西藏': [91.1865, 30.1465],
  '四川': [103.9526, 30.7617],
  '宁夏': [106.3586, 38.1775],
  '海南': [110.3893, 19.8516],
  '台湾': [121.509062, 25.044332],
  '香港': [114.173355, 22.320048],
  '澳门': [113.54909, 22.198951]
};

// 注册地图数据
export function registerMap() {
  try {
    if (!echarts.getMap('china')) {
      echarts.registerMap('china', chinaJson);
    }
  } catch (error) {
    console.error('注册中国地图数据失败:', error);
  }
}

export default {
  registerMap,
  chinaGeoCoordMap
} 