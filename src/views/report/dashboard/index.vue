<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 顶部卡片统计信息 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb-20">
        <div class="filter-container">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
            style="width: 300px;"
          />
        </div>
      </el-col>
    </el-row>
    
    <!-- 统计卡片行 - 使用24格栅格系统布局 -->
    <el-row class="stat-cards-row" :gutter="20">
      <el-col :span="4" :xs="24" :sm="12" :md="8" :lg="4" class="stat-card-col">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>注册用户总数</span>
              <el-tag type="success" size="small" effect="plain" v-if="statsTrends.userTrend">增长 {{statsTrends.userTrend}}%</el-tag>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">
              {{ stats.totalUsers ? stats.totalUsers.toLocaleString() : '-' }}
            </div>
            <div class="card-icon">
              <el-icon><User /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="4" :xs="24" :sm="12" :md="8" :lg="4" class="stat-card-col">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>用户访问次数</span>
              <el-tag type="success" size="small" effect="plain" v-if="statsTrends.visitTrend">增长 {{statsTrends.visitTrend}}%</el-tag>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">
              {{ stats.totalVisits ? stats.totalVisits.toLocaleString() : '-' }}
            </div>
            <div class="card-icon">
              <el-icon><View /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="4" :xs="24" :sm="12" :md="8" :lg="4" class="stat-card-col">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>SDK下载次数</span>
              <el-tag type="success" size="small" effect="plain" v-if="statsTrends.sdkDownloadTrend">增长 {{statsTrends.sdkDownloadTrend}}%</el-tag>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">
              {{ stats.sdkDownloadCount ? stats.sdkDownloadCount.toLocaleString() : '-' }}
            </div>
            <div class="card-icon">
              <el-icon><Download /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="4" :xs="24" :sm="12" :md="8" :lg="4" class="stat-card-col">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>示例工程下载次数</span>
              <el-tag type="success" size="small" effect="plain" v-if="statsTrends.trainEngineerDownloadTrend">增长 {{statsTrends.trainEngineerDownloadTrend}}%</el-tag>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">
              {{ stats.trainEngineerDownloadCount ? stats.trainEngineerDownloadCount.toLocaleString() : '-' }}
            </div>
            <div class="card-icon">
              <el-icon><Folder /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="4" :xs="24" :sm="12" :md="8" :lg="4" class="stat-card-col">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>原始模型数量</span>
              <el-tag type="success" size="small" effect="plain" v-if="statsTrends.originalModelTrend">增长 {{statsTrends.originalModelTrend}}%</el-tag>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">
              {{ stats.originalModelCount ? stats.originalModelCount.toLocaleString() : '-' }}
            </div>
            <div class="card-icon">
              <el-icon><Files /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="4" :xs="24" :sm="12" :md="8" :lg="4" class="stat-card-col">
        <el-card shadow="hover" class="stat-card">
          <template #header>
            <div class="card-header">
              <span>模型转换次数</span>
              <el-tag type="warning" size="small" effect="plain" v-if="statsTrends.conversionTrend">增长 {{statsTrends.conversionTrend}}%</el-tag>
            </div>
          </template>
          <div class="card-body">
            <div class="card-value">
              {{ stats.totalConversions ? stats.totalConversions.toLocaleString() : '-' }}
            </div>
            <div class="card-icon">
              <el-icon><Share /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">

      <!-- 用户类型分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">用户类型分布</span>
            </div>
          </template>
          <div ref="userTypeChart" class="chart-container">
            <div v-if="loading.userType" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>      
      <!-- 各模块访问分布 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">各模块访问分布</span>
            </div>
          </template>
          <div ref="moduleVisitChart" class="chart-container">
            <div v-if="loading.module" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 转换成功/失败比例 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">模型转换成功率</span>
            </div>
          </template>
          <div ref="conversionRateChart" class="chart-container">
            <div v-if="loading.conversionRate" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <!-- 平台使用趋势图 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <el-card shadow="hover" class="chart-card trend-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">平台使用趋势</span>
              <div class="trend-filter">
                <el-radio-group v-model="trendTimeUnit" size="small" @change="getTrendData">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                  <el-radio-button label="year">年</el-radio-button>
                </el-radio-group>
                <el-button 
                  type="text" 
                  @click="openTrendFullscreen" 
                  class="fullscreen-btn"
                >
                  <el-icon><FullScreen /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div ref="trendChart" class="chart-container trend-chart">
            <div v-if="loading.trend" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 模型转换使用分布热力图 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <el-card shadow="hover" class="chart-card map-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">模型转换地区分布</span>
              <div class="map-filter">
                <el-dropdown @command="handleMapDataTypeChange" trigger="click">
                  <el-button size="small" type="primary" plain style="width: 130px; text-align: left;">
                    {{ getMapDataTypeLabel(mapDataType) }} <el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item 
                        v-for="option in mapDataOptions" 
                        :key="option.value" 
                        :command="option.value"
                        :class="{ 'is-active': mapDataType === option.value }"
                      >
                        {{ option.label }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button 
                  type="text" 
                  @click="openMapFullscreen" 
                  class="fullscreen-btn"
                >
                  <el-icon><FullScreen /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div ref="mapChart" class="chart-container map-chart">
            <div v-if="loading.map" class="chart-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 全屏趋势图对话框 -->
    <el-dialog
      v-model="trendFullscreenVisible"
      title="平台使用趋势"
      width="90%"
      :destroy-on-close="false"
      :close-on-click-modal="false"
      class="fullscreen-dialog"
    >
      <div ref="fullscreenTrendChart" class="fullscreen-chart"></div>
    </el-dialog>
    
    <!-- 全屏地图对话框 -->
    <el-dialog
      v-model="mapFullscreenVisible"
      title="模型转换地区分布"
      width="90%"
      :destroy-on-close="false"
      :close-on-click-modal="false"
      class="fullscreen-dialog"
    >
      <div ref="fullscreenMapChart" class="fullscreen-chart"></div>
    </el-dialog>
  </div>
</template>

<script setup name="ReportDashboard">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { User, View, Share, Loading, ArrowDown, Download, Folder, FullScreen, Files } from '@element-plus/icons-vue'
import { getRegionDistribution, getTrendData as fetchTrendData, getModuleDistribution, getUserTypeDistribution, getConversionRate, getBasicStats } from '@/api/report'
import { registerMap, chinaGeoCoordMap } from './map-data/index.js'

// 统计数值
const stats = reactive({
  totalUsers: 0,
  totalVisits: 0,
  totalConversions: 0,
  sdkDownloadCount: 0,
  trainEngineerDownloadCount: 0,
  originalModelCount: 0
})

// 统计趋势
const statsTrends = reactive({
  userTrend: 0,
  visitTrend: 0,
  conversionTrend: 0,
  sdkDownloadTrend: 0,
  trainEngineerDownloadTrend: 0,
  originalModelTrend: 0
})

// 图表实例
let conversionRateChartInstance = null
let userTypeChartInstance = null
let moduleVisitChartInstance = null
let trendChartInstance = null
let mapChartInstance = null
let fullscreenTrendChartInstance = null
let fullscreenMapChartInstance = null

// 图表DOM引用
const conversionRateChart = ref(null)
const userTypeChart = ref(null)
const moduleVisitChart = ref(null)
const trendChart = ref(null)
const mapChart = ref(null)
const fullscreenTrendChart = ref(null)
const fullscreenMapChart = ref(null)

// 全屏对话框控制
const trendFullscreenVisible = ref(false)
const mapFullscreenVisible = ref(false)

// 趋势图过滤器
const trendTimeUnit = ref('day')
// 地图数据类型选项
const mapDataOptions = [
  { label: '模型转换次数', value: 'conversion' },
  { label: 'SDK下载次数', value: 'download' }
]
// 地图数据类型
const mapDataType = ref(mapDataOptions[0].value)

// 计算属性：当前选中的选项
const currentMapDataOption = computed(() => {
  return mapDataOptions.find(option => option.value === mapDataType.value) || mapDataOptions[0]
})

// 日期范围
const dateRange = ref(null)

// 加载状态
const loading = reactive({
  conversionRate: false,
  userType: false,
  module: false,
  trend: false,
  map: false
})

// 初始化图表
onMounted(() => {
  nextTick(() => {
    // 初始化图表实例
    initCharts()
    
    // 加载数据
    handleDateRangeChange()
    
    window.addEventListener('resize', handleResize)
  })
})

// 销毁图表实例
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  conversionRateChartInstance?.dispose()
  userTypeChartInstance?.dispose()
  moduleVisitChartInstance?.dispose()
  trendChartInstance?.dispose()
  mapChartInstance?.dispose()
  fullscreenTrendChartInstance?.dispose()
  fullscreenMapChartInstance?.dispose()
})

// 初始化所有图表实例
const initCharts = () => {
  conversionRateChartInstance = echarts.init(conversionRateChart.value)
  userTypeChartInstance = echarts.init(userTypeChart.value)
  moduleVisitChartInstance = echarts.init(moduleVisitChart.value)
  trendChartInstance = echarts.init(trendChart.value, null, { height: 'auto' })
  mapChartInstance = echarts.init(mapChart.value, null, { height: 'auto' })
  
  // 注册地图
  registerMap()
}

// 处理窗口大小变化，调整图表
const handleResize = () => {
  conversionRateChartInstance?.resize()
  userTypeChartInstance?.resize()
  moduleVisitChartInstance?.resize()
  trendChartInstance?.resize()
  mapChartInstance?.resize()
  fullscreenTrendChartInstance?.resize()
  fullscreenMapChartInstance?.resize()
}

// 添加日期变化处理函数
const handleDateRangeChange = () => {
  // 当日期范围改变时，重新加载所有数据
  getBasicStatsData()
  getConversionRateData()
  getUserTypeData()
  getModuleData()
  getTrendData()
  getMapData()
}

// 获取基础统计数据
const getBasicStatsData = async () => {
  try {
    const startTime = dateRange.value ? dateRange.value[0] : undefined
    const endTime = dateRange.value ? dateRange.value[1] : undefined
    
    const res = await getBasicStats(startTime, endTime)
    if (res && res.code === 200 && res.data) {
      const data = res.data
      
      // 更新统计数据
      stats.totalUsers = data.totalUsers || 0
      stats.totalVisits = data.totalVisits || 0
      stats.sdkDownloadCount = data.sdkDownloadCount || 0
      stats.trainEngineerDownloadCount = data.trainEngineerDownloadCount || 0
      stats.originalModelCount = data.originalModelCount || 0
      
      // 更新趋势数据，如果接口返回了趋势数据
      if (data.userTrend !== undefined) {
        statsTrends.userTrend = data.userTrend
      }
      
      if (data.visitTrend !== undefined) {
        statsTrends.visitTrend = data.visitTrend
      }
      
      if (data.sdkDownloadTrend !== undefined) {
        statsTrends.sdkDownloadTrend = data.sdkDownloadTrend
      }
      
      if (data.trainEngineerDownloadTrend !== undefined) {
        statsTrends.trainEngineerDownloadTrend = data.trainEngineerDownloadTrend
      }
      
      if (data.originalModelTrend !== undefined) {
        statsTrends.originalModelTrend = data.originalModelTrend
      }
    }
  } catch (error) {
    console.error('获取基础统计数据失败', error)
  }
}

// 获取转换成功率数据
const getConversionRateData = async () => {
  loading.conversionRate = true
  try {
    const startTime = dateRange.value ? dateRange.value[0] : undefined
    const endTime = dateRange.value ? dateRange.value[1] : undefined
    
    const res = await getConversionRate(startTime, endTime)
    if (res && res.code === 200 && res.data) {
      const data = res.data
      
      // 更新统计数据
      // 从成功率接口中获取转换总数
      let totalCount = 0;
      
      if (data.data && Array.isArray(data.data)) {
        // 新格式：计算所有数据的value总和
        totalCount = data.data.reduce((sum, item) => sum + (item.value || 0), 0);
      } else {
        // 旧格式：使用successCount和failCount之和
        totalCount = (data.successCount || 0) + (data.failCount || 0);
      }
      
      stats.totalConversions = data.total || totalCount || data.totalCount || 0;
      statsTrends.conversionTrend = data.trend || 0;
      
      // 构建图表数据
      let chartData = []
      const colorMap = {
        '成功': '#67C23A',
        '失败': '#F56C6C'
      }
      
      // 适配不同的数据格式
      if (data.data && Array.isArray(data.data)) {
        // 新格式：直接使用data字段中的数据
        chartData = data.data.map(item => {
          return {
            value: item.value,
            name: item.name,
            itemStyle: { color: colorMap[item.name] || '#909399' }
          }
        })
      } else {
        // 旧格式：使用successCount和failCount
        chartData = [
          { value: data.successCount || 0, name: '成功', itemStyle: { color: '#67C23A' } },
          { value: data.failCount || 0, name: '失败', itemStyle: { color: '#F56C6C' } }
        ]
      }
      
      // 更新图表
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: data.name || '转换结果',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 16,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }
      
      conversionRateChartInstance.setOption(option)
    } else {
      // 显示空数据
      showEmptyChart(conversionRateChartInstance, '转换成功率')
    }
  } catch (error) {
    console.error('获取转换成功率数据失败', error)
    // 显示空数据
    showEmptyChart(conversionRateChartInstance, '转换成功率')
  } finally {
    loading.conversionRate = false
  }
}

// 显示空数据图表
const showEmptyChart = (chartInstance, title) => {
  if (!chartInstance) return
  
  const option = {
    title: {
      text: '暂无数据',
      subtext: `${title}数据加载失败或暂无数据`,
      left: 'center',
      top: 'center',
      textStyle: {
        color: '#999',
        fontSize: 16,
        fontWeight: 'normal'
      },
      subtextStyle: {
        color: '#ccc',
        fontSize: 12
      }
    },
    series: []
  }
  
  chartInstance.setOption(option)
}

// 获取用户类型分布数据
const getUserTypeData = async () => {
  loading.userType = true
  try {
    const startTime = dateRange.value ? dateRange.value[0] : undefined
    const endTime = dateRange.value ? dateRange.value[1] : undefined
    
    const res = await getUserTypeDistribution(startTime, endTime)
    if (res && res.code === 200 && res.data) {
      const data = res.data
      
      // 不再更新统计数据，由getBasicStatsData方法提供
      // stats.totalUsers = data.totalCount || 0
      // statsTrends.userTrend = data.trend || 0
      
      // 构建图表数据
      let chartData = []
      const colorMap = {
        '个人': '#409EFF',
        '高校': '#E6A23C',
        '公司': '#67C23A',
        '商业评估': '#F56C6C',
        '商业合同': '#FF7F50',
        '管理员': '#9370DB',
        '其他': '#909399'
      }
      
      // 适配不同的数据格式
      if (data.data && Array.isArray(data.data)) {
        // 新格式：直接使用data字段中的数据
        chartData = data.data.map(item => {
          return {
            value: item.value,
            name: item.name,
            itemStyle: { color: colorMap[item.name] || '#909399' }
          }
        })
      } else if (data.items && Array.isArray(data.items)) {
        // 旧格式：使用items数组
        chartData = data.items.map(item => {
          return {
            value: item.count,
            name: item.typeName,
            itemStyle: { color: colorMap[item.typeName] || '#909399' }
          }
        })
      }
      
      // 更新图表
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: data.name || '用户类型',
            type: 'pie',
            radius: '70%',
            center: ['50%', '45%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      userTypeChartInstance.setOption(option)
    } else {
      // 显示空数据
      showEmptyChart(userTypeChartInstance, '用户类型')
    }
  } catch (error) {
    console.error('获取用户类型分布数据失败', error)
    // 显示空数据
    showEmptyChart(userTypeChartInstance, '用户类型')
  } finally {
    loading.userType = false
  }
}

// 获取模块访问分布数据
const getModuleData = async () => {
  loading.module = true
  try {
    const startTime = dateRange.value ? dateRange.value[0] : undefined
    const endTime = dateRange.value ? dateRange.value[1] : undefined
    
    const res = await getModuleDistribution(startTime, endTime)
    if (res && res.code === 200 && res.data) {
      const data = res.data
      
      // 不再更新统计数据，由getBasicStatsData方法提供
      // stats.totalVisits = data.totalCount || 0
      // statsTrends.visitTrend = data.trend || 0
      
      // 构建图表数据
      let chartData = []
      const colorMap = {
        '模型转换': '#409EFF',
        '文档': '#67C23A',
        'Model Zoo': '#E6A23C',
        'SDK下载': '#F56C6C',
        '其他': '#909399'
      }
      
      // 适配不同的数据格式
      if (data.data && Array.isArray(data.data)) {
        // 新格式：直接使用data字段中的数据
        chartData = data.data.map(item => {
          return {
            value: item.value,
            name: item.name,
            itemStyle: { color: colorMap[item.name] || '#909399' }
          }
        })
      } else if (data.items && Array.isArray(data.items)) {
        // 旧格式：使用items数组
        chartData = data.items.map(item => {
          return {
            value: item.count,
            name: item.moduleName,
            itemStyle: { color: colorMap[item.moduleName] || '#909399' }
          }
        })
      }
      
      // 更新图表
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '5%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: data.name || '模块访问',
            type: 'pie',
            radius: '70%',
            center: ['50%', '45%'],
            data: chartData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      moduleVisitChartInstance.setOption(option)
    } else {
      // 显示空数据
      showEmptyChart(moduleVisitChartInstance, '模块访问分布')
    }
  } catch (error) {
    console.error('获取模块访问分布数据失败', error)
    // 显示空数据
    showEmptyChart(moduleVisitChartInstance, '模块访问分布')
  } finally {
    loading.module = false
  }
}

// 获取趋势数据
const getTrendData = async () => {
  loading.trend = true
  try {
    const startTime = dateRange.value ? dateRange.value[0] : undefined
    const endTime = dateRange.value ? dateRange.value[1] : undefined
    
    const res = await fetchTrendData(trendTimeUnit.value, startTime, endTime)
    if (res && res.code === 200 && res.data) {
      const data = res.data
      
      // 构建图表数据
      const xAxisData = data.timeLabels || []
      
      // 从series数组中获取数据
      let visitData = []
      let conversionData = []
      
      if (data.series && data.series.length > 0) {
        // 找到访问次数和模型转换次数的数据
        const visitSeries = data.series.find(item => item.name === '访问次数')
        const conversionSeries = data.series.find(item => item.name === '模型转换次数')
        
        if (visitSeries) visitData = visitSeries.data || []
        if (conversionSeries) conversionData = conversionSeries.data || []
      } else {
        // 兼容旧格式
        visitData = data.visitData || []
        conversionData = data.conversionData || []
      }
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['访问次数', '模型转换次数'],
          top: 10,
          right: 10
        },
        grid: {
          left: '3%',
          right: '5%',
          bottom: '6%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLine: {
            onZero: false
          },
          axisLabel: {
            rotate: 45,
            interval: 'auto',
            fontSize: 10,
            margin: 10,
            hideOverlap: true,
            align: 'right',
            padding: [0, 0, 0, 0]
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '访问次数',
            type: 'line',
            smooth: true,
            data: visitData,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ])
            }
          },
          {
            name: '模型转换次数',
            type: 'line',
            smooth: true,
            data: conversionData,
            itemStyle: {
              color: '#67C23A'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)'
                }
              ])
            }
          }
        ]
      }
      
      trendChartInstance.setOption(option)
    } else {
      // 当没有数据时，清空图表
      trendChartInstance.clear()
    }
  } catch (error) {
    console.error('获取趋势数据失败', error)
    // 当发生错误时，清空图表
    trendChartInstance.clear()
  } finally {
    loading.trend = false
  }
}

// 获取地图数据类型显示文本
const getMapDataTypeLabel = (value) => {
  const option = mapDataOptions.find(opt => opt.value === value)
  return option ? option.label : mapDataOptions[0].label
}

// 打开趋势图全屏
const openTrendFullscreen = () => {
  trendFullscreenVisible.value = true
  nextTick(() => {
    if (!fullscreenTrendChartInstance && fullscreenTrendChart.value) {
      fullscreenTrendChartInstance = echarts.init(fullscreenTrendChart.value)
    }
    // 复制原图表配置
    if (fullscreenTrendChartInstance && trendChartInstance) {
      const option = trendChartInstance.getOption()
      fullscreenTrendChartInstance.setOption(option)
      fullscreenTrendChartInstance.resize()
    }
  })
}

// 打开地图全屏
const openMapFullscreen = () => {
  mapFullscreenVisible.value = true
  nextTick(() => {
    if (!fullscreenMapChartInstance && fullscreenMapChart.value) {
      fullscreenMapChartInstance = echarts.init(fullscreenMapChart.value)
    }
    // 复制原图表配置
    if (fullscreenMapChartInstance && mapChartInstance) {
      const option = mapChartInstance.getOption()
      fullscreenMapChartInstance.setOption(option)
      fullscreenMapChartInstance.resize()
    }
  })
}

// 处理地图数据类型变化
const handleMapDataTypeChange = (value) => {
  mapDataType.value = value
  getMapData()
}

// 获取地图数据
const getMapData = async () => {
  loading.map = true
  try {
    const startTime = dateRange.value ? dateRange.value[0] : undefined
    const endTime = dateRange.value ? dateRange.value[1] : undefined
    
    const res = await getRegionDistribution(mapDataType.value, startTime, endTime)
    if (res && res.code === 200 && res.data) {
      const dataType = getMapDataTypeLabel(mapDataType.value)
      
      // 处理新的API响应格式
      if (res.data.regions && Array.isArray(res.data.regions)) {
        updateMapOption(res.data.regions, dataType)
      } else if (res.data.items && Array.isArray(res.data.items)) {
        // 兼容旧格式
        updateMapOption(res.data.items, dataType)
      } else {
        showEmptyChart(mapChartInstance, '地区分布')
      }
    } else {
      showEmptyChart(mapChartInstance, '地区分布')
    }
  } catch (error) {
    console.error('获取地图数据失败', error)
    showEmptyChart(mapChartInstance, '地区分布')
  } finally {
    loading.map = false
  }
}

// 更新地图配置
const updateMapOption = (rawDataItems, dataType) => {
  // rawDataItems is an array like 
  // 1. 新格式: [{name: '北京', value: 123}, ...] 
  // 2. 旧格式: [{name: '北京', count: 123}, ...] 
  // 3. 从dataView: [{name: '北京', value: 123}, ...]

  // 1. 将不同来源的数据统一为 {name, value} 格式的原始数值数组
  const rawValues = rawDataItems.map(item => ({
      name: item.name,
      value: Number(item.count || item.value) || 0
  }));
  
  // 2. 从原始数值中计算最大值和总和
  const maxValue = Math.max(...rawValues.map(item => item.value));
  const effectiveMaxValue = maxValue > 0 ? maxValue : 100;
  const totalValue = rawValues.reduce((sum, item) => sum + item.value, 0);

  // 3. 根据最大值计算每个数据点的百分比用于颜色映射，并计算占总和的百分比用于显示
  const processedData = rawValues.map(item => {
      const colorPercentValue = (item.value / effectiveMaxValue) * 100; // 用于颜色映射
      const totalPercentValue = totalValue > 0 ? (item.value / totalValue) * 100 : 0; // 占总和的百分比
      return {
          name: item.name,
          value: colorPercentValue,       // for color mapping
          rawValue: item.value,           // 原始值
          percentValue: totalPercentValue // 占总和的百分比
      };
  });

  console.log('原始地图数据:', rawDataItems);
  console.log('处理后的地图数据:', processedData);
  console.log('数据总量:', totalValue);

  const option = {
    title: {
      text: `总数: ${totalValue.toLocaleString()}`,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#606266'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: params => {
        const data = params.data || {};
        return `${data.name}<br/>${data.rawValue} (${dataType})<br/>占比: ${data.percentValue.toFixed(2)}%`;
      }
    },
    visualMap: {
      min: 0,
      max: 100, // 固定为0-100，因为我们用百分比来映射颜色
      text: ['高 (100%)', '低 (0%)'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#C6FFDD', '#A7E9AF', '#89D8A7', '#6BC69F', '#FBD786', '#FAA585', '#f7797d']
      },
      formatter: value => `${value.toFixed(0)}%`,
      left: 'left',
      top: 'bottom'
    },
    toolbox: {
      show: true,
      orient: 'vertical',
      left: 'right',
      top: 'center',
      feature: {
        dataView: {
          readOnly: false,
          optionToContent: function (opt) {
            const seriesData = opt.series[0].data;
            let text = '地区,数值,占比(%)\n';
            seriesData.forEach(item => {
              text += `${item.name},${item.rawValue},${item.percentValue.toFixed(2)}\n`;
            });
            return `<textarea style="width:100%; height:200px; font-family:monospace; font-size:12px; border:1px solid #ccc; border-radius:4px;">${text}</textarea>`;
          },
          contentToOption: function (view, opt) {
            const text = view.querySelector('textarea').value;
            const lines = text.trim().split('\n').slice(1); // 忽略表头

            const newRawValues = lines.map(line => {
              const parts = line.split(',');
              return {
                name: parts[0].trim(),
                value: Number(parts[1]) || 0
              };
            });
            
            // 重新计算总值和最大值
            const newTotalValue = newRawValues.reduce((sum, item) => sum + item.value, 0);
            const newMaxValue = Math.max(...newRawValues.map(item => item.value));
            const newEffectiveMaxValue = newMaxValue > 0 ? newMaxValue : 100;

            const newProcessedData = newRawValues.map(item => {
              const colorPercentValue = (item.value / newEffectiveMaxValue) * 100;
              const totalPercentValue = newTotalValue > 0 ? (item.value / newTotalValue) * 100 : 0;
              return {
                name: item.name,
                value: colorPercentValue,
                rawValue: item.value,
                percentValue: totalPercentValue
              };
            });

            // 更新标题中的总数
            opt.title.text = `总数: ${newTotalValue.toLocaleString()}`;
            opt.series[0].data = newProcessedData;
            return opt;
          }
        },
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        name: dataType,
        type: 'map',
        map: 'china',
        roam: true,
        emphasis: {
          label: { show: true },
          itemStyle: { areaColor: '#79a1eb' }
        },
        data: processedData,
        itemStyle: {
          borderColor: '#909399',
          borderWidth: 1,
          areaColor: '#f3f3f3'
        }
      }
    ]
  };

  if (mapChartInstance) {
    // 确保每次都完全覆盖旧的配置
    mapChartInstance.setOption(option, true);
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.stat-card {
  height: 160px;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.card-title {
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.card-icon {
  font-size: 40px;
  color: #909399;
  opacity: 0.3;
}

.chart-card {
  transition: all 0.3s;
  height: 400px;
}

.chart-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.chart-container {
  width: 100%;
  height: 320px;
  position: relative;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}

.chart-loading .el-icon {
  font-size: 32px;
  color: #409EFF;
}

/* 特殊卡片高度调整 */
.trend-card, .map-card {
  height: 550px;
}

.trend-chart, .map-chart {
  height: 470px;
}

.trend-filter, .map-filter {
  margin-left: auto;
  display: flex;
  align-items: center;
  flex-wrap: wrap; /* 允许元素在空间不足时换行 */
  gap: 8px; /* 元素之间的间距 */
}

.map-filter .el-select {
  width: 130px !important; /* 确保下拉框宽度固定 */
  flex-shrink: 0; /* 防止下拉框被压缩 */
}

.date-filter {
  margin-left: auto;
  min-width: 300px; /* 确保日期选择器有足够的宽度 */
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .chart-card {
    height: 450px;
  }
  
  .chart-container {
    height: 370px;
  }
  
  .trend-card, .map-card {
    height: 600px;
  }
  
  .trend-chart, .map-chart {
    height: 520px;
  }
}

@media (max-width: 1200px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .card-title {
    margin-bottom: 8px;
  }
  
  .date-filter, .trend-filter, .map-filter {
    margin-left: 0;
    margin-top: 8px;
    width: 100%;
  }
  
  .date-filter {
    min-width: unset;
  }
  
  .date-filter .el-date-editor {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .stat-card {
    margin-bottom: 20px;
    height: auto;
    min-height: 160px;
  }
  
  .chart-card {
    margin-bottom: 20px;
    height: 400px;
  }
  
  .chart-container {
    height: 320px;
  }
  
  .trend-card, .map-card {
    height: 500px;
  }
  
  .trend-chart, .map-chart {
    height: 420px;
  }
  
  .trend-filter, .map-filter {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  .el-radio-group {
    margin-bottom: 8px;
  }
  
  .el-date-editor.el-input, 
  .el-date-editor.el-input__wrapper {
    width: 100%;
  }
  
  /* 确保地图过滤器下拉框在移动端保持固定宽度 */
  .map-filter .el-select {
    width: 130px !important;
  }
}

@media (max-width: 576px) {
  .app-container {
    padding: 10px;
  }
  
  .chart-card {
    height: 350px;
  }
  
  .chart-container {
    height: 270px;
  }
  
  .trend-card, .map-card {
    height: 450px;
  }
  
  .trend-chart, .map-chart {
    height: 370px;
  }
  
  .card-value {
    font-size: 24px;
  }
}

.filter-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

/* 统计卡片行样式 */
.stat-cards-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

/* 统计卡片列样式 */
.stat-card-col {
  margin-bottom: 20px;
}

@media (max-width: 1199px) {
  .el-row .stat-card-col {
    width: 33.333%;
    flex: 0 0 33.333%;
  }
}

@media (max-width: 991px) {
  .el-row .stat-card-col {
    width: 33.333%;
    flex: 0 0 33.333%;
  }
}

@media (max-width: 768px) {
  .filter-container {
    justify-content: flex-start;
  }
  
  .el-row .stat-card-col {
    width: 50%;
    flex: 0 0 50%;
  }
}

@media (max-width: 576px) {
  .el-row .stat-card-col {
    width: 100%;
    flex: 0 0 100%;
  }
}

.fullscreen-btn {
  margin-left: 10px;
  font-size: 16px;
}

.fullscreen-chart {
  width: 100%;
  height: 80vh;
}

.fullscreen-dialog :deep(.el-dialog__body) {
  padding: 10px;
}
</style>