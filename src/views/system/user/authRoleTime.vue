<template>
   <div class="app-container">
      <h4 class="form-header h4">基本信息</h4>
      <el-form :model="form" label-width="80px">
         <el-row>
            <el-col :span="8" :offset="2">
               <el-form-item label="用户昵称" prop="nickName">
                  <el-input v-model="form.nickName" disabled />
               </el-form-item>
            </el-col>
            <el-col :span="8" :offset="2">
               <el-form-item label="登录账号" prop="userName">
                  <el-input v-model="form.userName" disabled />
               </el-form-item>
            </el-col>
         </el-row>
      </el-form>

      <h4 class="form-header h4">角色信息</h4>
      <el-table v-loading="loading" :row-key="getRowKey" @row-click="clickRow" ref="roleRef" @selection-change="handleSelectionChange" :data="roles.slice((pageNum - 1) * pageSize, pageNum * pageSize)">
         <el-table-column label="序号" width="55" type="index" align="center">
            <template #default="scope">
               <span>{{ (pageNum - 1) * pageSize + scope.$index + 1 }}</span>
            </template>
         </el-table-column>
         <el-table-column type="selection" :reserve-selection="true" :selectable="checkSelectable" width="55"></el-table-column>
         <el-table-column label="角色编号" align="center" prop="roleId" />
         <el-table-column label="角色名称" align="center" prop="roleName" />
         <el-table-column label="权限字符" align="center" prop="roleKey" />
         <el-table-column label="有效期设置" align="center" width="400">
            <template #default="scope">
              <div v-if="isRoleSelected(scope.row.roleId)" @click.stop>
                <el-radio-group v-model="scope.row.timeType" @change="handleTimeTypeChange(scope.row)" size="small">
                  <el-radio value="permanent">永久有效</el-radio>
                  <el-radio value="custom">自定义有效期</el-radio>
                  <el-radio value="temporary">临时权限</el-radio>
                </el-radio-group>

                <div v-if="scope.row.timeType === 'custom'" style="margin-top: 8px;">
                  <el-date-picker
                    v-model="scope.row.startTime"
                    type="datetime"
                    placeholder="开始时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    size="small"
                    style="width: 160px; margin-right: 8px;">
                  </el-date-picker>
                  <span>至</span>
                  <el-date-picker
                    v-model="scope.row.endTime"
                    type="datetime"
                    placeholder="结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    size="small"
                    style="width: 160px; margin-left: 8px;">
                  </el-date-picker>
                </div>

                <div v-if="scope.row.timeType === 'temporary'" style="margin-top: 8px;">
                  <el-input-number
                    v-model="scope.row.tempHours"
                    :min="1"
                    :max="8760"
                    placeholder="小时数"
                    size="small"
                    style="width: 120px;"
                    @change="handleTempHoursChange(scope.row)">
                  </el-input-number>
                  <span style="margin-left: 8px;">小时</span>
                </div>
              </div>
              <div v-else style="color: #c0c4cc; font-size: 12px;">
                请先选中此角色
              </div>
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="pageNum" v-model:limit="pageSize" />

      <el-form label-width="100px">
         <div style="text-align: center;margin-left:-120px;margin-top:30px;">
            <el-button type="primary" @click="submitForm()">提交</el-button>
            <el-button @click="close()">返回</el-button>
         </div>
      </el-form>
   </div>
</template>

<script setup name="AuthRoleTime">
import { ref, reactive, nextTick, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getUser, getAuthRole, getAuthRoleWithTime, authUserRoleWithTime } from "@/api/system/user";

const route = useRoute()
const { proxy } = getCurrentInstance()

const loading = ref(true)
const total = ref(0)
const pageNum = ref(1)
const pageSize = ref(10)
const roleIds = ref([])
const roles = ref([])
const form = ref({
  nickName: undefined,
  userName: undefined,
  userId: undefined
})

/** 单击选中行数据 */
function clickRow(row) {
  if (checkSelectable(row)) {
    proxy.$refs["roleRef"].toggleRowSelection(row)
  }
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  roleIds.value = selection.map(item => item.roleId)
}

/** 保存选中的数据编号 */
function getRowKey(row) {
  return row.roleId
}

// 检查角色状态
function checkSelectable(row) {
  return row.status === "0" ? true : false
}

// 判断角色是否被选中
const isRoleSelected = (roleId) => {
  return roleIds.value.includes(roleId)
}

// 时间类型改变
const handleTimeTypeChange = (row) => {
  if (row.timeType === 'permanent') {
    row.startTime = null
    row.endTime = null
  } else if (row.timeType === 'temporary') {
    const now = new Date()
    row.startTime = formatDateTime(now)
    const endTime = new Date(now.getTime() + (row.tempHours || 24) * 60 * 60 * 1000)
    row.endTime = formatDateTime(endTime)
  }
}

// 临时权限小时数改变
const handleTempHoursChange = (row) => {
  if (row.timeType === 'temporary' && row.tempHours > 0) {
    const now = new Date()
    row.startTime = formatDateTime(now)
    const endTime = new Date(now.getTime() + row.tempHours * 60 * 60 * 1000)
    row.endTime = formatDateTime(endTime)
  }
}

// 格式化日期时间
const formatDateTime = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/** 关闭按钮 */
function close() {
  const obj = { path: "/system/user" }
  proxy.$tab.closeOpenPage(obj)
}

/** 提交按钮 */
function submitForm() {
  const selectedRoles = roles.value.filter(role => roleIds.value.includes(role.roleId))
  
  // 验证时间设置
  for (let role of selectedRoles) {
    if (role.timeType === 'custom') {
      if (!role.startTime || !role.endTime) {
        proxy.$modal.msgError(`角色 ${role.roleName} 的自定义时间不能为空`)
        return
      }
      if (new Date(role.startTime) >= new Date(role.endTime)) {
        proxy.$modal.msgError(`角色 ${role.roleName} 的开始时间不能大于或等于结束时间`)
        return
      }
      // 检查结束时间不能是过去时间
      if (new Date(role.endTime) <= new Date()) {
        proxy.$modal.msgError(`角色 ${role.roleName} 的结束时间不能是过去时间`)
        return
      }
    } else if (role.timeType === 'temporary') {
      if (!role.tempHours || role.tempHours <= 0) {
        proxy.$modal.msgError(`角色 ${role.roleName} 的临时权限时间必须大于0`)
        return
      }
      // 重新计算结束时间
      const now = new Date()
      role.startTime = formatDateTime(now)
      const endTime = new Date(now.getTime() + role.tempHours * 60 * 60 * 1000)
      role.endTime = formatDateTime(endTime)
    }
  }
  
  const userId = form.value.userId
  const roleIdList = selectedRoles.map(role => role.roleId)
  const startTimes = selectedRoles.map(role => role.timeType === 'permanent' ? null : role.startTime)
  const endTimes = selectedRoles.map(role => role.timeType === 'permanent' ? null : role.endTime)
  
  const data = {
    userId: userId,
    roleIds: roleIdList,
    startTimes: startTimes,
    endTimes: endTimes
  }
  
  loading.value = true
  authUserRoleWithTime(data).then(response => {
    proxy.$modal.msgSuccess("授权成功")
    close()
  }).catch(error => {
    proxy.$modal.msgError("授权失败：" + (error.msg || error.message || "未知错误"))
  }).finally(() => {
    loading.value = false
  })
}

// 判断时间类型
const getTimeType = (startTime, endTime) => {
  if (!startTime && !endTime) {
    return 'permanent'
  }

  // 检查是否是临时权限（从当前时间开始的固定小时数）
  if (startTime && endTime) {
    const start = new Date(startTime)
    const end = new Date(endTime)
    const now = new Date()

    // 如果开始时间接近当前时间（误差在5分钟内），可能是临时权限
    const timeDiff = Math.abs(start.getTime() - now.getTime())
    if (timeDiff <= 5 * 60 * 1000) { // 5分钟内
      const hoursDiff = Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60))
      if (hoursDiff > 0 && hoursDiff <= 8760) { // 1年内的小时数
        return 'temporary'
      }
    }
  }

  return 'custom'
}

// 计算临时权限的小时数
const getTempHours = (startTime, endTime) => {
  if (!startTime || !endTime) return 24
  const start = new Date(startTime)
  const end = new Date(endTime)
  return Math.round((end.getTime() - start.getTime()) / (1000 * 60 * 60))
}

(() => {
  const userId = route.params && route.params.userId
  if (userId) {
    loading.value = true
    getAuthRoleWithTime(userId).then(response => {
      form.value = response.user
      roles.value = response.roles.map(role => {
        const timeType = getTimeType(role.startTime, role.endTime)
        return {
          ...role,
          timeType: timeType,
          startTime: role.startTime,
          endTime: role.endTime,
          tempHours: timeType === 'temporary' ? getTempHours(role.startTime, role.endTime) : 24
        }
      })
      total.value = roles.value.length
      nextTick(() => {
        roles.value.forEach(row => {
          if (row.flag) {
            proxy.$refs["roleRef"].toggleRowSelection(row)
          }
        })
      })
      loading.value = false
    }).catch(error => {
      console.error('获取角色信息失败:', error)
      // 如果新接口失败，回退到旧接口
      getAuthRole(userId).then(response => {
        form.value = response.user
        roles.value = response.roles.map(role => ({
          ...role,
          timeType: 'permanent', // 默认永久有效
          startTime: null,
          endTime: null,
          tempHours: 24 // 默认24小时
        }))
        total.value = roles.value.length
        nextTick(() => {
          roles.value.forEach(row => {
            if (row.flag) {
              proxy.$refs["roleRef"].toggleRowSelection(row)
            }
          })
        })
        loading.value = false
      })
    })
  }
})()
</script>
