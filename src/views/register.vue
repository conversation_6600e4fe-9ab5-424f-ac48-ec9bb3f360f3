<template>
  <div class="register">
    <el-form ref="registerRef" :model="registerForm" :rules="registerRules" class="register-form" :validate-on-rule-change="false" :show-message="showValidateMessage">
      <h3 class="title">{{ title }}</h3>
      <el-form-item prop="username">
        <el-input 
          v-model="registerForm.username" 
          type="text" 
          size="large" 
          auto-complete="off" 
          placeholder="用户名"
        >
          <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="phoneNumber">
        <el-input
          v-model="registerForm.phoneNumber"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="手机号"
        >
          <template #prefix><svg-icon icon-class="phone" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="smsCode">
        <el-input
          v-model="registerForm.smsCode"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter="verifySmsCode"
        >
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <el-button
          type="primary"
          class="get-code-btn"
          :disabled="smsCodeTimer > 0"
          @click="getSmsCode"
        >
          {{ smsCodeTimer > 0 ? `${smsCodeTimer}秒后重试` : '获取验证码' }}
        </el-button>
      </el-form-item>
      <el-form-item prop="email">
        <el-input 
          v-model="registerForm.email" 
          type="text" 
          size="large" 
          auto-complete="off" 
          placeholder="邮箱"
        >
          <template #prefix><svg-icon icon-class="email" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="userType">
        <el-select v-model="registerForm.userType" placeholder="用户类型" style="width: 100%;">
          <el-option label="个人" value="personal" />
          <el-option label="高校" value="university" />
          <el-option label="公司" value="company" />
          <el-option label="商业评估" value="business_evaluation" />
          <el-option label="商业合同" value="business_contract" />
          <el-option label="管理员" value="admin" />
        </el-select>
      </el-form-item>
      <template v-if="showCompanyFields">
        <el-form-item prop="companyName">
          <el-input 
            v-model="registerForm.companyName" 
            type="text" 
            size="large" 
            auto-complete="off" 
            :placeholder="companyNameLabel"
          >
            <template #prefix><svg-icon icon-class="peoples" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="companyWebsite" v-if="showCompanyOnlyFields">
          <el-input 
            v-model="registerForm.companyWebsite" 
            type="text" 
            size="large" 
            auto-complete="off" 
            placeholder="公司官网"
          >
            <template #prefix><svg-icon icon-class="link" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <!-- 商业合同类型特有字段 -->
        <el-form-item prop="contractNumber" v-if="showBusinessContractFields">
          <el-input 
            v-model="registerForm.contractNumber" 
            type="text" 
            size="large" 
            auto-complete="off" 
            placeholder="合作合同号"
          >
            <template #prefix><svg-icon icon-class="documentation" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
      </template>
      <el-form-item prop="password">
        <el-input
          v-model="registerForm.password"
          type="password"
          size="large" 
          auto-complete="off"
          placeholder="密码"
        >
          <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword">
        <el-input
          v-model="registerForm.confirmPassword"
          type="password"
          size="large" 
          auto-complete="off"
          placeholder="确认密码"
        >
          <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <template v-if="showCompanyOnlyFields">
        <el-form-item prop="companyRegistration">
          <el-button 
            type="primary" 
            class="upload-btn" 
            @click="handleCompanyRegistrationClick"
          >
            选择文件
          </el-button>
          <span class="file-name" v-if="registerForm.companyRegistrationName">{{ registerForm.companyRegistrationName }}</span>
          <span class="file-label" v-else>公司注册信息</span>
          <input 
            type="file" 
            ref="companyRegistrationRef" 
            style="display: none;" 
            @change="handleCompanyRegistrationChange"
          />
        </el-form-item>
        <el-form-item prop="ndaContract">
          <el-button 
            type="primary" 
            class="upload-btn" 
            @click="handleNdaContractClick"
          >
            选择文件
          </el-button>
          <span class="file-name" v-if="registerForm.ndaContractName">{{ registerForm.ndaContractName }}</span>
          <span class="file-label" v-else>NDA合同电子档</span>
          <input 
            type="file" 
            ref="ndaContractRef" 
            style="display: none;" 
            @change="handleNdaContractChange"
          />
        </el-form-item>
      </template>
      <el-form-item prop="agreement">
        <el-checkbox v-model="registerForm.agreement">
          阅读并同意：跃昉<a href="javascript:void(0);" @click="showUserAgreement">《用户协议》</a>
        </el-checkbox>
      </el-form-item>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="large" 
          type="primary"
          style="width:100%;"
          @click.prevent="handleRegister"
          :disabled="!registerForm.agreement"
        >
          <span v-if="!loading">确 定</span>
          <span v-else>提 交 中...</span>
        </el-button>
        <div style="float: right;">
          <router-link class="link-type" :to="'/login'">使用已有账户登录</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-register-footer">
      <span>Copyright © 2018-2025 www.leapfive.com All Rights Reserved.</span>
    </div>
    
    <!-- 用户协议对话框 -->
    <el-dialog
      title="用户注册协议"
      v-model="dialogVisible"
      width="70%"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="agreement-content" v-html="agreementContent"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="acceptAgreement">同意并继续</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from "element-plus"
import { getCodeImg, register, registerWithFile, sendSmsCode } from "@/api/login"
import { computed, watch, ref, onMounted } from 'vue'
import userAgreement from '@/assets/doc/用户协议.txt?raw'

const title = import.meta.env.VITE_APP_TITLE
const router = useRouter()
const { proxy } = getCurrentInstance()

const registerForm = ref({
  username: "",
  phoneNumber: "",
  smsCode: "",
  email: "",
  companyName: "",
  companyWebsite: "",
  userType: "personal",
  password: "",
  confirmPassword: "",
  code: "",
  uuid: "",
  agreement: false,
  companyRegistration: null,
  companyRegistrationName: "",
  ndaContract: null,
  ndaContractName: "",
  contractNumber: "" // 添加合作合同号字段
})

const dialogVisible = ref(false)
const agreementContent = ref('')
const smsCodeTimer = ref(0)
const isSmsCodeVerified = ref(false)
const companyRegistrationRef = ref(null)
const ndaContractRef = ref(null)
const registerRef = ref(null)
const showValidateMessage = ref(false)

// 根据用户类型判断是否显示公司相关字段
const showCompanyFields = computed(() => {
  return ['company', 'business_evaluation', 'business_contract', 'university'].includes(registerForm.value.userType)
})

// 根据用户类型获取不同的标签文本
const companyNameLabel = computed(() => {
  return registerForm.value.userType === 'university' ? '高校名称' : '公司名称'
})

// 判断是否显示公司特有字段（官网、注册信息、NDA合同）
const showCompanyOnlyFields = computed(() => {
  return ['company', 'business_evaluation', 'business_contract'].includes(registerForm.value.userType)
})

// 判断是否显示商业合同特有字段
const showBusinessContractFields = computed(() => {
  return registerForm.value.userType === 'business_contract'
})

// 监听用户类型变化，重置相关字段
watch(() => registerForm.value.userType, (newType) => {
  // 重置相关字段
  if (!['company', 'business_evaluation', 'business_contract', 'university'].includes(newType)) {
    registerForm.value.companyName = ""
    registerForm.value.companyWebsite = ""
    registerForm.value.companyRegistration = null
    registerForm.value.companyRegistrationName = ""
    registerForm.value.ndaContract = null
    registerForm.value.ndaContractName = ""
    registerForm.value.contractNumber = ""
  } else if (newType !== 'business_contract') {
    registerForm.value.contractNumber = ""
  }
  
  // 重置验证消息显示状态
  showValidateMessage.value = false
  
  // 重置表单验证状态
  if (registerRef.value) {
    // 延迟执行，确保DOM更新后再清除验证
    setTimeout(() => {
      // 清除所有字段的验证
      registerRef.value.clearValidate()
    }, 0)
  }
})

const equalToPassword = (rule, value, callback) => {
  if (registerForm.value.password !== value) {
    callback(new Error("两次输入的密码不一致"))
  } else {
    callback()
  }
}

const validatePhone = (rule, value, callback) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(value)) {
    callback(new Error("请输入正确的手机号码"))
  } else {
    callback()
  }
}

const validateEmail = (rule, value, callback) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (!emailRegex.test(value)) {
    callback(new Error("请输入正确的邮箱地址"))
  } else {
    callback()
  }
}

// 动态计算验证规则
const registerRules = computed(() => {
  const baseRules = {
    username: [
      { required: true, trigger: "", message: "请输入您的用户名" },
      { min: 2, max: 20, message: "用户名长度必须介于 2 和 20 之间", trigger: "" }
    ],
    phoneNumber: [
      { required: true, trigger: "", message: "请输入您的手机号" },
      { validator: validatePhone, trigger: "" }
    ],
    smsCode: [
      { required: true, trigger: "", message: "请输入短信验证码" }
    ],
    email: [
      { required: true, trigger: "", message: "请输入您的邮箱" },
      { validator: validateEmail, trigger: "" }
    ],
    userType: [
      { required: true, trigger: "change", message: "请选择用户类型" }
    ],
    password: [
      { required: true, trigger: "", message: "请输入您的密码" },
      { min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "" },
      { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "" }
    ],
    confirmPassword: [
      { required: true, trigger: "", message: "请再次输入您的密码" },
      { required: true, validator: equalToPassword, trigger: "" }
    ],
    agreement: [
      { required: true, trigger: "change", message: "请阅读并同意用户协议和隐私政策" }
    ]
  }
  
  // 如果是公司类型用户，添加公司相关字段的验证规则
  if (showCompanyFields.value) {
    baseRules.companyName = [
      { required: true, trigger: "", message: `请输入${registerForm.value.userType === 'university' ? '高校名称' : '公司名称'}` }
    ]
    
    // 只有企业类型用户才需要验证这些字段
    if (showCompanyOnlyFields.value) {
      baseRules.companyWebsite = [
        { required: true, trigger: "", message: "请输入公司官网" }
      ]
      baseRules.companyRegistration = [
        { required: true, trigger: "", message: "请上传公司注册信息",
          validator: (rule, value, callback) => {
            if (!registerForm.value.companyRegistration) {
              callback(new Error('请上传公司注册信息'))
            } else {
              callback()
            }
          }
        }
      ]
      baseRules.ndaContract = [
        { required: true, trigger: "", message: "请上传NDA合同电子档",
          validator: (rule, value, callback) => {
            if (!registerForm.value.ndaContract) {
              callback(new Error('请上传NDA合同电子档'))
            } else {
              callback()
            }
          }
        }
      ]
      
      // 商业合同类型特有字段验证规则
      if (showBusinessContractFields.value) {
        baseRules.contractNumber = [
          { required: true, trigger: "", message: "请输入合作合同号" }
        ]
      }
    }
  }
  
  return baseRules
})

const loading = ref(false)

function getSmsCode() {
  if (!registerForm.value.phoneNumber) {
    ElMessage.warning("请先输入手机号码")
    return
  }

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(registerForm.value.phoneNumber)) {
    ElMessage.warning("请输入正确的手机号码")
    return
  }

  // 调用发送短信验证码的API
  sendSmsCode(registerForm.value.phoneNumber).then(res => {
    if (res.code === 200) {
      smsCodeTimer.value = 60
      const timer = setInterval(() => {
        smsCodeTimer.value--
        if (smsCodeTimer.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)

      ElMessage.success("验证码已发送，请注意查收")
    } else {
      ElMessage.error(res.msg || "验证码发送失败")
    }
  }).catch(() => {
    ElMessage.error("验证码发送失败")
  })
}

// 验证短信验证码
function verifySmsCode() {
  if (!registerForm.value.smsCode) {
    ElMessage.warning("请输入验证码")
    return
  }
  
  if (registerForm.value.smsCode.length < 4) {
    ElMessage.warning("请输入完整的验证码")
    return
  }

  // 这里应该调用验证验证码的API
  // 模拟验证成功
  isSmsCodeVerified.value = true
  ElMessage.success("验证码验证成功")
}

// 监听短信验证码变化，自动设置为已验证状态
watch(() => registerForm.value.smsCode, (newVal) => {
  if (newVal && newVal.length >= 4) {
    isSmsCodeVerified.value = true
  }
})

function handleCompanyRegistrationClick() {
  companyRegistrationRef.value.click()
}

function handleNdaContractClick() {
  ndaContractRef.value.click()
}

function handleCompanyRegistrationChange(e) {
  const file = e.target.files[0]
  if (file) {
    registerForm.value.companyRegistrationName = file.name
    registerForm.value.companyRegistration = file
  }
}

function handleNdaContractChange(e) {
  const file = e.target.files[0]
  if (file) {
    registerForm.value.ndaContractName = file.name
    registerForm.value.ndaContract = file
  }
}

function handleRegister() {
  showValidateMessage.value = true
  
  // 如果用户输入了验证码但未点击验证按钮，自动设置为已验证状态
  if (registerForm.value.smsCode && registerForm.value.smsCode.length >= 4) {
    isSmsCodeVerified.value = true
  }
  
  proxy.$refs.registerRef.validate(valid => {
    if (valid) {
      // 检查短信验证码是否已验证
      if (!isSmsCodeVerified.value) {
        ElMessage.warning("请先输入短信验证码")
        return
      }
      
      // 检查必要的文件是否已上传（对于公司类型用户）
      if (showCompanyOnlyFields.value) {
        if (!registerForm.value.companyRegistration) {
          ElMessage.warning("请上传公司注册信息")
          return
        }
        if (!registerForm.value.ndaContract) {
          ElMessage.warning("请上传NDA合同电子档")
          return
        }
      }
      
      loading.value = true

      let apiCall

      // 如果是企业用户且有文件上传，使用文件上传接口
      if (showCompanyOnlyFields.value && registerForm.value.userType !== 'university' && (registerForm.value.companyRegistration || registerForm.value.ndaContract)) {
        // 创建FormData对象用于文件上传
        const formData = new FormData()

        // 添加表单字段
        Object.keys(registerForm.value).forEach(key => {
          if (key !== 'companyRegistration' && key !== 'ndaContract' &&
              key !== 'companyRegistrationName' && key !== 'ndaContractName') {
            formData.append(key, registerForm.value[key])
          }
        })

        // 添加文件
        if (registerForm.value.companyRegistration) {
          formData.append('companyRegistration', registerForm.value.companyRegistration)
        }

        if (registerForm.value.ndaContract) {
          formData.append('ndaContract', registerForm.value.ndaContract)
        }

        apiCall = registerWithFile(formData)
      } else {
        // 普通注册，使用JSON格式
        const registerData = {
          username: registerForm.value.username,
          phoneNumber: registerForm.value.phoneNumber,
          smsCode: registerForm.value.smsCode,
          email: registerForm.value.email,
          userType: registerForm.value.userType,
          companyName: registerForm.value.companyName,
          companyWebsite: registerForm.value.companyWebsite,
          password: registerForm.value.password,
          confirmPassword: registerForm.value.confirmPassword,
          code: registerForm.value.code,
          uuid: registerForm.value.uuid,
          agreement: registerForm.value.agreement
        }

        apiCall = register(registerData)
      }

      apiCall.then(res => {
        const username = registerForm.value.username
        ElMessageBox.alert("<font color='red'>恭喜你，您的账号 " + username + " 注册成功！</font>", "系统提示", {
          dangerouslyUseHTMLString: true,
          type: "success",
        }).then(() => {
          router.push("/login")
        }).catch(() => {})
      }).catch(() => {
        loading.value = false
      })
    }
  })
}

// 显示用户协议对话框
function showUserAgreement() {
  try {
    // 将文本内容转换为HTML格式
    agreementContent.value = userAgreement.split('\n').map(line => {
      // 处理标题
      if (line.startsWith('第') && line.includes('条')) {
        return `<h4>${line}</h4>`
      }
      // 处理小标题
      if (/^\d+\.\d+/.test(line)) {
        return `<h5>${line}</h5>`
      }
      return `<p>${line}</p>`
    }).join('')
    dialogVisible.value = true
  } catch (error) {
    ElMessage.error('加载用户协议失败')
  }
}

// 关闭对话框前的回调
function handleClose(done) {
  done()
}

// 接受协议
function acceptAgreement() {
  registerForm.value.agreement = true
  dialogVisible.value = false
}

// 添加一个方法来重置表单
function resetForm() {
  // 重置验证消息显示状态
  showValidateMessage.value = false
  
  // 重置表单验证状态
  if (registerRef.value) {
    registerRef.value.clearValidate()
  }
}

// 在组件挂载后重置表单
onMounted(() => {
  resetForm()
})
</script>

<style lang='scss' scoped>
.register {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.register-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}
.register-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.register-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-register-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.register-code-img {
  height: 40px;
  padding-left: 12px;
}
.get-code-btn {
  width: 35%;
  height: 40px;
  float: right;
}
.upload-btn {
  width: 100px;
  height: 40px;
}
.file-name {
  margin-left: 10px;
  font-size: 14px;
  color: #606266;
}
.file-label {
  margin-left: 10px;
  font-size: 14px;
  color: #909399;
}
.agreement-content {
  max-height: 60vh;
  overflow-y: auto;
  white-space: pre-line;
  line-height: 1.8;
  font-size: 14px;
  padding: 20px;
  color: #303133;
  text-align: left;

  p {
    margin-bottom: 12px;
    text-indent: 2em;
  }

  h3, h4, h5 {
    font-weight: bold;
    margin: 20px 0 10px;
    text-indent: 0;
  }

  h3 {
    font-size: 18px;
    text-align: center;
  }

  h4 {
    font-size: 16px;
  }

  h5 {
    font-size: 14px;
  }
}
</style>

