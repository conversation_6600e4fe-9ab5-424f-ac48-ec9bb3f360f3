import request from '@/utils/request'

// 执行模型转换
export function executeTransfer(data) {
  return request({
    url: '/v1/ai/transfer/execute',
    method: 'post',
    data: data
  })
}

// 获取模型转换工具下载地址
export function getModelTransferDownloadUrl(identifier, expires = 3600, fileType = null) {
  return request({
    url: `/v1/ai/transfer/download/${identifier}`,
    method: 'get',
    params: { expires, fileType }
  })
}


// 获取模型转换状态
export function transferStatus(taskId) {
  return request({
    url: `/v1/ai/transfer/transferStatus/${taskId}`,
    method: 'get',  // 修改为GET请求，通常状态查询应该是GET请求
  })
} 