import request from '@/utils/request'

// 获取上传进度
export function getUploadProgress(identifier, fileType) {
  return request({
    url: `/v1/ai/file/${identifier}`,
    method: 'get',
    params: fileType ? { fileType } : {}
  })
}

// 创建上传任务
export function createUploadTask(data) {
  return request({
    url: '/v1/ai/file',
    method: 'post',
    data
  })
}

// 获取分片预签名上传地址
export function getPreSignedUrl(identifier, partNumber, fileType) {
  return request({
    url: `/v1/ai/file/${identifier}/${partNumber}/${fileType}`,
    method: 'get'
  })
}

// 合并分片
export function mergeChunks(identifier, fileType) {
  return request({
    url: `/v1/ai/file/merge/${identifier}/${fileType}`,
    method: 'post'
  })
}

// 获取文件下载地址
export function getDownloadUrl(identifier, expires = 3600, fileType = null) {
  return request({
    url: `/v1/ai/file/download/${identifier}`,
    method: 'get',
    params: { expires, fileType }
  })
}

// 批量创建上传任务
export function batchCreateUploadTask(data) {
  return request({
    url: '/v1/ai/file/batchUpload',
    method: 'post',
    data
  })
} 