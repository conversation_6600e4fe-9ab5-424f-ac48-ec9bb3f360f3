import request from '@/utils/request'

// 查询训练工程列表
export function listTrainEngineer(query) {
  return request({
    url: '/admin/train/list',
    method: 'get',
    params: query
  })
}

// 获取训练工程详细信息
export function getTrainEngineer(id) {
  return request({
    url: '/admin/train/' + id,
    method: 'get'
  })
}

// 下载训练工程文件
export function downloadTrainEngineer(identifier) {
  return request({
    url: '/admin/train/ai/download/' + identifier,
    method: 'get'
  })
} 