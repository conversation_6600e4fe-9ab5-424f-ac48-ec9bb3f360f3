import request from '@/utils/request'

// 查询模型转换列表
export function listModelConversion(query) {
  return request({
    url: '/ai/modelConversion/list',
    method: 'get',
    params: query
  })
}

// 查询模型转换详细
export function getModelConversion(id) {
  return request({
    url: '/ai/modelConversion/' + id,
    method: 'get'
  })
}

// 新增模型转换
export function addModelConversion(data) {
  return request({
    url: '/ai/modelConversion',
    method: 'post',
    data: data
  })
}

// 修改模型转换
export function updateModelConversion(data) {
  return request({
    url: '/ai/modelConversion',
    method: 'put',
    data: data
  })
}

// 删除模型转换
export function delModelConversion(id) {
  return request({
    url: '/ai/modelConversion/' + id,
    method: 'delete'
  })
}

// 下载转换后的模型
export function downloadConvertedModel(id) {
  return request({
    url: '/ai/modelConversion/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
} 