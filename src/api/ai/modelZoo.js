import request from '@/utils/request'

// 查询ModelZoo列表
export function listModel<PERSON>oo(query) {
  return request({
    url: '/v1/ai/modelZoo/list',
    method: 'get',
    params: query
  })
}

// 查询ModelZoo详细
export function getModel<PERSON>oo(id) {
  return request({
    url: '/v1/ai/modelZoo/' + id,
    method: 'get'
  })
}

// 下载模型示例代码
export function downloadModelCode(id) {
  return request({
    url: '/v1/ai/modelZoo/code/' + id,
    method: 'get',
    responseType: 'blob'
  })
} 

// 获取文件下载地址
export function getDownloadUrl(identifier, expires = 3600, fileType = null) {
  return request({
    url: `/v1/ai/modelZoo/download/${identifier}`,
    method: 'get',
    params: { expires, fileType }
  })
}