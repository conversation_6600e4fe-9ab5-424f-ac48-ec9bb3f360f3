import request from '@/utils/request'

// 查询主机列表
export function listHost(query) {
  return request({
    url: '/v1/ai/host/list',
    method: 'get',
    timeout: 60000, // 1分钟超时
    params: query
  })
}

// 查询主机详细
export function getHost(hostId) {
  return request({
    url: '/v1/ai/host/' + hostId,
    method: 'get'
  })
}

// 新增主机
export function addHost(data) {
  return request({
    url: '/v1/ai/host',
    method: 'post',
    data: data
  })
}

// 修改主机
export function updateHost(data) {
  return request({
    url: '/v1/ai/host',
    method: 'put',
    data: data
  })
}

// 删除主机
export function delHost(hostId) {
  return request({
    url: '/v1/ai/host/' + hostId,
    method: 'delete'
  })
}

// 上传可执行文件
export function uploadExecutable(data) {
  return request({
    url: '/v1/ai/host/uploadExecutable',
    method: 'post',
    timeout: 300000, // 5分钟超时
    data: data
  })
} 