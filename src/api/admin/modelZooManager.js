import request from '@/utils/request'

// 查询ModelZoo列表
export function listModel<PERSON>oo(query) {
  return request({
    url: '/v1/ai/modelZoo/list',
    method: 'get',
    params: query
  })
}

// 查询ModelZoo详细
export function getModel<PERSON>oo(id) {
  return request({
    url: '/v1/ai/modelZoo/' + id,
    method: 'get'
  })
}

// 新增ModelZoo
export function addModel<PERSON>oo(data) {
  return request({
    url: '/v1/ai/modelZoo',
    method: 'post',
    data: data
  })
}

// 修改ModelZoo
export function updateModelZoo(data) {
  return request({
    url: '/v1/ai/modelZoo',
    method: 'put',
    data: data
  })
}

// 删除ModelZoo
export function delModelZoo(ids) {
  return request({
    url: '/v1/ai/modelZoo/' + ids,
    method: 'delete'
  })
} 