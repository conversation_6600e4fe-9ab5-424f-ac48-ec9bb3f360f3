import request from '@/utils/request'

// 查询文件类型列表
export function listFileType(query) {
  return request({
    url: '/ai/file/type/list',
    method: 'get',
    params: query
  })
}

// 查询文件类型详细
export function getFileType(id) {
  return request({
    url: '/ai/file/type/' + id,
    method: 'get'
  })
}

// 新增文件类型
export function addFileType(data) {
  return request({
    url: '/ai/file/type',
    method: 'post',
    data: data
  })
}

// 修改文件类型
export function updateFileType(data) {
  return request({
    url: '/ai/file/type',
    method: 'put',
    data: data
  })
}

// 删除文件类型
export function delFileType(id) {
  return request({
    url: '/ai/file/type/' + id,
    method: 'delete'
  })
} 