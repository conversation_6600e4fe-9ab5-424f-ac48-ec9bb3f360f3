import request from '@/utils/request'

// 查询模型转换日志列表
export function listModelConversionLog(query) {
  return request({
    url: '/v1/ai/transferRecord/list',
    method: 'get',
    params: query
  })
}

// 查询模型转换日志详细
export function getModelConversionLog(id) {
  return request({
    url: '/v1/ai/transferRecord/' + id,
    method: 'get'
  })
}

// 新增模型转换日志
export function addModelConversionLog(data) {
  return request({
    url: '/v1/ai/transferRecord',
    method: 'post',
    data: data
  })
}

// 修改模型转换日志
export function updateModelConversionLog(data) {
  return request({
    url: '/v1/ai/transferRecord',
    method: 'put',
    data: data
  })
}

// 删除模型转换日志
export function delModelConversionLog(ids) {
  return request({
    url: '/v1/ai/transferRecord/' + ids,
    method: 'delete'
  })
}

// 导出模型转换日志
export function exportModelConversionLog(query) {
  return request({
    url: '/v1/ai/transferRecord/export',
    method: 'post',
    params: query
  })
}

// 清空模型转换日志
export function cleanModelConversionLog() {
  return request({
    url: '/admin/modelConversionLog/clean',
    method: 'delete'
  })
} 