import request from '@/utils/request'

// 查询文件列表
export function listFile(query) {
  return request({
    url: '/v1/ai/file/list',
    method: 'get',
    params: query
  })
}

// 获取文件详情
export function getFileInfo(id) {
  return request({
    url: '/v1/ai/file/info/' + id,
    method: 'get'
  })
}

// 获取文件详情
export function getFileInfoByFileIdentifier(fileIdentifier) {
  return request({
    url: '/v1/ai/file/task/' + fileIdentifier,
    method: 'get'
  })
}

// 新增上传任务
export function addUploadTask(data) {
  return request({
    url: '/v1/ai/file',
    method: 'post',
    data: data
  })
}

// 修改上传任务
export function updateFile(data) {
  return request({
    url: '/v1/ai/file',
    method: 'put',
    data: data
  })
}

// 删除文件
export function delFile(id) {
  return request({
    url: '/v1/ai/file/' + id,
    method: 'delete',
    timeout: 120000  // 为单个文件删除也设置2分钟超时
  })
}

// 批量删除文件
export function batchDelFile(ids) {
  return request({
    url: '/v1/ai/file/batch/' + ids,
    method: 'delete',
    timeout: 120000  // 为批量删除操作单独设置2分钟超时
  })
}

// 获取文件下载地址
export function getDownloadUrl(identifier, expires, isRecord, fileType = null) {
  return request({
    url: `/v1/ai/file/download/${identifier}`,
    method: 'get',
    params: { expires, isRecord, fileType }
  })
}

// 查询上传进度
export function getUploadProgress(identifier, fileType) {
  return request({
    url: `/v1/ai/file/${identifier}`,
    method: 'get',
    params: fileType ? { fileType } : {}
  })
}

// 批量下载文件
export function batchDownloadFiles(identifiers) {
  return request({
    url: '/v1/ai/file/batchDownload',
    method: 'post',
    data: identifiers,
    responseType: 'blob',
    headers: {
      'Accept': 'application/zip'
    }
  })
} 