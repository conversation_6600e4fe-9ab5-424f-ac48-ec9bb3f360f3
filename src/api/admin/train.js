import request from '@/utils/request'

// 查询训练工程列表
export function listTrain(query) {
  return request({
    url: '/admin/train/list',
    method: 'get',
    params: query
  })
}

// 查询训练工程详细
export function getTrain(id) {
  return request({
    url: '/admin/train/' + id,
    method: 'get'
  })
}

// 新增训练工程
export function addTrain(data) {
  return request({
    url: '/admin/train',
    method: 'post',
    data: data
  })
}

// 修改训练工程
export function updateTrain(data) {
  return request({
    url: '/admin/train',
    method: 'put',
    data: data
  })
}

// 删除训练工程
export function delTrain(id) {
  return request({
    url: '/admin/train/' + id,
    method: 'delete'
  })
}

// 下载训练工程文件
export function downloadTrain(identifier) {
  return request({
    url: '/admin/train/download/' + identifier,
    method: 'get'
  })
} 