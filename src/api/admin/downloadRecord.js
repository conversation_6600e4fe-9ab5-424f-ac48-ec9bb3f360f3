import request from '@/utils/request'

// 查询下载记录列表
export function listDownloadRecord(query) {
  return request({
    url: '/admin/downloadRecord/list',
    method: 'get',
    params: query
  })
}

// 查询下载记录详细
export function getDownloadRecord(id) {
  return request({
    url: '/admin/downloadRecord/' + id,
    method: 'get'
  })
}

// 新增下载记录
export function addDownloadRecord(data) {
  return request({
    url: '/admin/downloadRecord',
    method: 'post',
    data: data
  })
}

// 修改下载记录
export function updateDownloadRecord(data) {
  return request({
    url: '/admin/downloadRecord',
    method: 'put',
    data: data
  })
}

// 删除下载记录
export function delDownloadRecord(id) {
  return request({
    url: '/admin/downloadRecord/' + id,
    method: 'delete'
  })
} 