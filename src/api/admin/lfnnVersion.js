import request from '@/utils/request'

// 查询LFNN版本列表
export function listLfnnVersion(query) {
  return request({
    url: '/admin/lfnnVersion/list',
    method: 'get',
    params: query
  })
}

// 查询LFNN版本详细
export function getLfnnVersion(versionId) {
  return request({
    url: '/admin/lfnnVersion/' + versionId,
    method: 'get'
  })
}

// 新增LFNN版本
export function addLfnnVersion(data) {
  return request({
    url: '/admin/lfnnVersion',
    method: 'post',
    data: data
  })
}

// 修改LFNN版本
export function updateLfnnVersion(data) {
  return request({
    url: '/admin/lfnnVersion',
    method: 'put',
    data: data
  })
}

// 删除LFNN版本
export function delLfnnVersion(versionId) {
  return request({
    url: '/admin/lfnnVersion/' + versionId,
    method: 'delete'
  })
}

// 下载LFNN版本
export function downloadLfnnVersion(identifier) {
  return request({
    url: '/admin/lfnnVersion/download/' + identifier,
    method: 'get'
  })
} 