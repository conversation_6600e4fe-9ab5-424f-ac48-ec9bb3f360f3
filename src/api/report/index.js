import request from '@/utils/request'
import { mockRegionData, mockUserTypeData, mockModuleData, mockConversionRateData, mockTrendData, mockBasicStatsData } from './mock'

/**
 * 获取模型转换地区分布数据
 * @param {string} dataType - 数据类型: conversion(模型转换次数)或download(SDK下载次数)
 * @param {string} startTime - 开始时间(可选，格式: yyyy-MM-dd)
 * @param {string} endTime - 结束时间(可选，格式: yyyy-MM-dd)
 */
export function getRegionDistribution(dataType, startTime, endTime) {
  const params = { dataType }
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime
  
  return new Promise((resolve, reject) => {
    request({
      url: '/v1/report/region-distribution',
      method: 'get',
      params
    }).then(res => {
      resolve(res)
    }).catch(err => {
      console.warn('地区分布数据API请求失败，使用模拟数据', err)
      // 使用模拟数据作为备选
      resolve({
        code: 200,
        msg: '操作成功(模拟数据)',
        data: mockRegionData
      })
    })
  })
}

/**
 * 获取平台使用趋势数据
 * @param {string} timeUnit - 时间单位：day(日)、week(周)、month(月)
 * @param {string} startTime - 开始时间(可选，格式: yyyy-MM-dd)
 * @param {string} endTime - 结束时间(可选，格式: yyyy-MM-dd)
 */
export function getTrendData(timeUnit, startTime, endTime) {
  const params = { timeUnit }
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime
  
  return new Promise((resolve, reject) => {
    request({
      url: '/v1/report/trend-data',
      method: 'get',
      params
    }).then(res => {
      resolve(res)
    }).catch(err => {
      console.warn('趋势数据API请求失败，使用模拟数据', err)
      // 使用模拟数据作为备选
      resolve({
        code: 200,
        msg: '操作成功(模拟数据)',
        data: mockTrendData(timeUnit)
      })
    })
  })
}

/**
 * 获取各模块访问分布数据
 * @param {string} startTime - 开始时间(可选，格式: yyyy-MM-dd)
 * @param {string} endTime - 结束时间(可选，格式: yyyy-MM-dd)
 */
export function getModuleDistribution(startTime, endTime) {
  const params = {}
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime
  
  return new Promise((resolve, reject) => {
    request({
      url: '/v1/report/module-distribution',
      method: 'get',
      params
    }).then(res => {
      resolve(res)
    }).catch(err => {
      console.warn('模块分布数据API请求失败，使用模拟数据', err)
      // 使用模拟数据作为备选
      resolve({
        code: 200,
        msg: '操作成功(模拟数据)',
        data: mockModuleData
      })
    })
  })
}

/**
 * 获取用户类型访问分布数据
 * @param {string} startTime - 开始时间(可选，格式: yyyy-MM-dd)
 * @param {string} endTime - 结束时间(可选，格式: yyyy-MM-dd)
 */
export function getUserTypeDistribution(startTime, endTime) {
  const params = {}
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime
  
  return new Promise((resolve, reject) => {
    request({
      url: '/v1/report/user-type-distribution',
      method: 'get',
      params
    }).then(res => {
      resolve(res)
    }).catch(err => {
      console.warn('用户类型分布数据API请求失败，使用模拟数据', err)
      // 使用模拟数据作为备选
      resolve({
        code: 200,
        msg: '操作成功(模拟数据)',
        data: mockUserTypeData
      })
    })
  })
}

/**
 * 获取模型转换成功率数据
 * @param {string} startTime - 开始时间(可选，格式: yyyy-MM-dd)
 * @param {string} endTime - 结束时间(可选，格式: yyyy-MM-dd)
 */
export function getConversionRate(startTime, endTime) {
  const params = {}
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime
  
  return new Promise((resolve, reject) => {
    request({
      url: '/v1/report/conversion-rate',
      method: 'get',
      params
    }).then(res => {
      resolve(res)
    }).catch(err => {
      console.warn('转换成功率数据API请求失败，使用模拟数据', err)
      // 使用模拟数据作为备选
      resolve({
        code: 200,
        msg: '操作成功(模拟数据)',
        data: mockConversionRateData
      })
    })
  })
}

/**
 * 获取基础统计数据（注册用户总数、用户访问次数）
 * @param {string} startTime - 开始时间(可选，格式: yyyy-MM-dd)
 * @param {string} endTime - 结束时间(可选，格式: yyyy-MM-dd)
 */
export function getBasicStats(startTime, endTime) {
  const params = {}
  if (startTime) params.startTime = startTime
  if (endTime) params.endTime = endTime
  
  return new Promise((resolve, reject) => {
    request({
      url: '/v1/report/basic-stats',
      method: 'get',
      params
    }).then(res => {
      resolve(res)
    }).catch(err => {
      console.warn('基础统计数据API请求失败，使用模拟数据', err)
      // 使用模拟数据作为备选
      resolve({
        code: 200,
        msg: '操作成功(模拟数据)',
        data: mockBasicStatsData
      })
    })
  })
}

// 添加访问记录（带防重复提交处理）
export function addAccessRecord(data) {
  // 生成当前时间戳，用于区分不同的请求
  const timestamp = new Date().getTime()
  return request({
    url: '/v1/accessRecord',
    method: 'post',
    data: {
      ...data,
      _t: timestamp // 添加时间戳参数，避免被认为是重复提交
    },
    headers: {
      'repeatSubmit': false // 设置为false，禁用防重复提交检查
    }
  })
} 