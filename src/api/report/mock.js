/**
 * 报表模块的模拟数据，用于API请求失败时提供测试数据
 */

// 区域分布模拟数据
export const mockRegionData = {
  totalCount: 5834,
  trend: 5,
  items: [
    { name: '北京', count: 864 },
    { name: '上海', count: 752 },
    { name: '广东', count: 701 },
    { name: '浙江', count: 542 },
    { name: '江苏', count: 498 },
    { name: '四川', count: 421 },
    { name: '湖北', count: 302 },
    { name: '湖南', count: 289 },
    { name: '河南', count: 267 },
    { name: '河北', count: 253 },
    { name: '陕西', count: 187 },
    { name: '山东', count: 174 },
    { name: '福建', count: 152 },
    { name: '辽宁', count: 127 },
    { name: '安徽', count: 113 },
    { name: '江西', count: 89 },
    { name: '黑龙江', count: 78 },
    { name: '吉林', count: 65 },
    { name: '云南', count: 61 },
    { name: '广西', count: 58 },
    { name: '重庆', count: 54 },
    { name: '天津', count: 47 },
    { name: '山西', count: 45 },
    { name: '内蒙古', count: 37 },
    { name: '贵州', count: 32 },
    { name: '新疆', count: 29 },
    { name: '甘肃', count: 23 },
    { name: '海南', count: 18 },
    { name: '宁夏', count: 12 },
    { name: '青海', count: 8 },
    { name: '西藏', count: 5 },
    { name: '香港', count: 44 },
    { name: '台湾', count: 33 },
    { name: '澳门', count: 11 }
  ]
}

// 用户类型分布模拟数据
export const mockUserTypeData = {
  totalCount: 12456,
  trend: 12,
  items: [
    { typeName: '普通用户', count: 0 },
    { typeName: '企业用户', count: 0 },
    { typeName: '学校用户', count: 0 },
    { typeName: '商业合同用户', count: 0 }
  ]
}

// 模块访问分布模拟数据
export const mockModuleData = {
  totalCount: 89745,
  trend: 8,
  items: [
    { moduleName: '模型转换', count: 0 },
    { moduleName: '文档', count: 0 },
    { moduleName: 'Model Zoo', count: 0 },
    { moduleName: 'SDK下载', count: 0 },
    { moduleName: '其他', count: 0 }
  ]
}

// 模型转换成功率模拟数据
export const mockConversionRateData = {
  totalCount: 0,
  trend: 0,
  successCount: 0,
  failCount: 0
}

// 使用趋势模拟数据
export const mockTrendData = (timeUnit) => {
  let timeLabels = []
  let visitData = []
  let conversionData = []
  
  if (timeUnit === 'day') {
    timeLabels = Array.from({ length: 30 }, (_, i) => `${i + 1}日`)
    visitData = Array.from({ length: 30 }, () => Math.floor(Math.random() * 500) + 100)
    conversionData = Array.from({ length: 30 }, () => Math.floor(Math.random() * 100) + 20)
  } else if (timeUnit === 'week') {
    timeLabels = Array.from({ length: 12 }, (_, i) => `第${i + 1}周`)
    visitData = Array.from({ length: 12 }, () => Math.floor(Math.random() * 2000) + 500)
    conversionData = Array.from({ length: 12 }, () => Math.floor(Math.random() * 500) + 100)
  } else {
    timeLabels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    visitData = Array.from({ length: 12 }, () => Math.floor(Math.random() * 8000) + 2000)
    conversionData = Array.from({ length: 12 }, () => Math.floor(Math.random() * 2000) + 500)
  }
  
  return {
    timeLabels,
    visitData,
    conversionData
  }
}

// 基础统计数据模拟数据
export const mockBasicStatsData = {
  totalUsers: 0,
  totalVisits: 0
} 