{"name": "ruoyi", "version": "3.8.9", "description": "若依管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "china-area-data": "^5.0.1", "clipboard": "2.0.11", "echarts": "5.5.1", "echarts-extension-gmap": "^1.7.0", "echarts-gl": "^2.0.9", "element-plus": "2.7.6", "file-saver": "2.0.5", "fuse.js": "6.6.2", "highlight.js": "^11.11.1", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "katex": "^0.16.22", "markdown-it": "^14.1.0", "markdown-it-anchor": "^9.2.0", "markdown-it-attrs": "^4.3.1", "markdown-it-container": "^4.0.0", "markdown-it-deflist": "^3.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-image-figures": "^2.1.1", "markdown-it-implicit-figures": "^0.12.0", "markdown-it-inline-comments": "^1.0.1", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-task-lists": "^2.1.1", "markdown-it-texmath": "^1.0.0", "nanoid": "^5.1.5", "nprogress": "0.2.0", "pinia": "2.1.7", "promise-queue-plus": "^1.2.2", "spark-md5": "^3.0.2", "splitpanes": "3.1.5", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-router": "4.4.0", "vuedraggable": "4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}, "overrides": {"quill": "2.0.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}