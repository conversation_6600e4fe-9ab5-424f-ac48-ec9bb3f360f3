package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysUserRole;

/**
 * 用户角色关联服务接口
 * 
 * <AUTHOR>
 */
public interface ISysUserRoleService
{
    /**
     * 查询用户角色关联信息（包含有效期）
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    public List<SysUserRole> selectUserRolesByUserId(Long userId);
    
    /**
     * 查询用户当前有效的角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    public List<Long> selectValidRoleIdsByUserId(Long userId);
}
