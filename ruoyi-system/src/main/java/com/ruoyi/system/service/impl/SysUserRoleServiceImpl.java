package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysUserRoleService;

/**
 * 用户角色关联服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysUserRoleServiceImpl implements ISysUserRoleService
{
    @Autowired
    private SysUserRoleMapper userRoleMapper;

    /**
     * 查询用户角色关联信息（包含有效期）
     *
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    @Override
    public List<SysUserRole> selectUserRolesByUserId(Long userId)
    {
        return userRoleMapper.selectUserRolesByUserId(userId);
    }

    /**
     * 查询用户当前有效的角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Override
    public List<Long> selectValidRoleIdsByUserId(Long userId)
    {
        return userRoleMapper.selectValidRoleIdsByUserId(userId);
    }
}
