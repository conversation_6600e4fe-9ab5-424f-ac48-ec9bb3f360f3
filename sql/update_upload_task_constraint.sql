-- 修改sys_upload_task表的唯一约束
-- 将file_identifier的唯一约束改为file_identifier + create_by的组合唯一约束
--
-- 目的：解决模型转换页面文件上传的用户权限问题
-- 1. 同一用户上传相同文件：复用已有记录，避免重复上传
-- 2. 不同用户上传相同文件：创建新记录，保持权限隔离
--
-- 执行前请确保：
-- 1. 备份数据库
-- 2. 确认当前没有重复的 (file_identifier, create_by) 组合
-- 3. 在维护窗口期间执行

-- 删除原有的file_identifier唯一约束
ALTER TABLE `sys_upload_task` DROP INDEX `uq_file_identifier`;

-- 添加新的组合唯一约束
ALTER TABLE `sys_upload_task` ADD UNIQUE KEY `uq_file_identifier_user` (`file_identifier`, `create_by`) USING BTREE;

-- 验证约束是否创建成功
SHOW INDEX FROM `sys_upload_task` WHERE Key_name = 'uq_file_identifier_user';
