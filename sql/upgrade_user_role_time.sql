-- 用户角色关联表添加有效期字段升级脚本
-- 执行时间：2024-12-19

-- 为 sys_user_role 表添加有效期字段
ALTER TABLE sys_user_role 
ADD COLUMN start_time datetime DEFAULT NULL COMMENT '权限有效开始时间',
ADD COLUMN end_time datetime DEFAULT NULL COMMENT '权限有效结束时间';

-- 添加索引以提高查询性能
CREATE INDEX idx_user_role_time ON sys_user_role(user_id, start_time, end_time);

-- 注释：
-- start_time 和 end_time 都为 NULL 表示永久有效
-- start_time 不为 NULL，end_time 为 NULL 表示从指定时间开始永久有效
-- start_time 为 NULL，end_time 不为 NULL 表示到指定时间结束
-- start_time 和 end_time 都不为 NULL 表示在指定时间范围内有效
