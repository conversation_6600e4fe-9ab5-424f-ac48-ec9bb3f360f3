-- ----------------------------
-- Table structure for sys_upload_task
-- ----------------------------
DROP TABLE IF EXISTS `sys_upload_task`;
CREATE TABLE `sys_upload_task` (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `upload_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分片上传的uploadId',
                                   `file_identifier` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件唯一标识（md5）',
                                   `file_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件名',
                                   `bucket_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属桶名',
                                   `object_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件的key',
                                   `total_size` bigint NOT NULL COMMENT '文件大小（byte）',
                                   `chunk_size` bigint NOT NULL COMMENT '每个分片大小（byte）',
                                   `chunk_num` int NOT NULL COMMENT '分片数量',
                                   `batch_no` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '批次号',
                                   `file_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'other' COMMENT '文件类型（model-模型文件，config-配置文件，image-图片）',
                                   `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
                                   `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
                                   `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`),
--                                    UNIQUE KEY `uq_file_identifier` (`file_identifier`) USING BTREE,
                                   UNIQUE KEY `uq_upload_id` (`upload_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分片上传-分片任务记录';

SET FOREIGN_KEY_CHECKS = 1;


DROP TABLE IF EXISTS `sys_upload_task_type`;
CREATE TABLE `sys_upload_task_type` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `file_type_name` varchar(64) DEFAULT NULL COMMENT '文件类型名称',
                                        `file_type_identifier` varchar(64) DEFAULT NULL COMMENT '文件类型标识',
                                        `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `sys_ai_task_host`;
CREATE TABLE `sys_ai_task_host` (
                                    `host_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主机ID',
                                    `host_name` varchar(50) NOT NULL COMMENT '主机名称',
                                    `host_ip` varchar(50) NOT NULL COMMENT '主机IP',
                                    `ssh_port` int(11) NOT NULL DEFAULT '22' COMMENT 'SSH端口',
                                    `ssh_username` varchar(50) NOT NULL COMMENT 'SSH用户名',
                                    `ssh_password` varchar(100) NOT NULL COMMENT 'SSH密码',
                                    `executable_path` varchar(255) NOT NULL COMMENT '可执行文件路径',
                                    `status` char(1) NOT NULL DEFAULT '0' COMMENT '主机状态（0空闲 1执行中 2离线）',
                                    `cpu_usage` decimal(5,2) DEFAULT NULL COMMENT 'CPU使用率',
                                    `memory_usage` decimal(5,2) DEFAULT NULL COMMENT '内存使用率',
                                    `disk_usage` decimal(5,2) DEFAULT NULL COMMENT '磁盘使用率',
                                    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`host_id`),
                                    UNIQUE KEY `idx_host_ip` (`host_ip`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='AI任务主机信息表';

DROP TABLE IF EXISTS `sys_ai_transfer_record`;
CREATE TABLE `sys_ai_transfer_record` (
                                          `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                                          `username` varchar(50) DEFAULT NULL COMMENT '转换用户',
                                          `model_identifier` varchar(100) DEFAULT NULL COMMENT '原始转换模型标识',
                                          `config_identifier` varchar(100) DEFAULT NULL COMMENT '配置文件标识',
                                          `image_identifiers` varchar(2048) DEFAULT NULL COMMENT '量化图片列表标识',
                                          `log_identifier` varchar(100) DEFAULT NULL COMMENT '日志文件标识',
                                          `status` char(1) DEFAULT '0' COMMENT '转换状态（0失败 1成功）',
                                          `login_location` varchar(255) DEFAULT NULL COMMENT '登录地址',
                                          `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                          `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
                                          `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                          `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                          `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='AI模型转换日志记录表';

DROP TABLE IF EXISTS `sys_download_record`;
CREATE TABLE `sys_download_record` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                       `download_file_name` varchar(500) DEFAULT NULL COMMENT '下载文件名',
                                       `download_type` varchar(50) DEFAULT NULL COMMENT '下载类型',
                                       `download_time` datetime DEFAULT NULL COMMENT '下载时间',
                                       `username` varchar(50) DEFAULT NULL COMMENT '下载用户',
                                       `opt_location` varchar(255) DEFAULT NULL COMMENT '操作地址',
                                       `remark` varchar(500) DEFAULT NULL COMMENT '下载备注说明（可用于手动标记）',
                                       `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                       `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='下载记录表';